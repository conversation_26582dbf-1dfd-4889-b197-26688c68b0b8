import type { <PERSON><PERSON>, StoryObj } from '@storybook/vue3';
import { ref, reactive } from 'vue';
import SeqLogViewer from './SeqLogViewer.vue';
import type { SeqEvent } from './useSeqService';

const meta = {
  title: 'Components/SeqLogViewer',
  component: SeqLogViewer,
  tags: ['autodocs'],
  argTypes: {
    baseUrl: {
      control: 'text',
      description: 'SEQ 服务器地址',
    },
    apiKey: {
      control: 'text',
      description: 'SEQ API 密钥',
    },
    autoConnect: {
      control: 'boolean',
      description: '是否自动连接',
    },
    showExportButtons: {
      control: 'boolean',
      description: '是否显示导出按钮',
    },
    enableAutoRefresh: {
      control: 'boolean',
      description: '是否启用自动刷新功能',
    },
    autoRefreshInterval: {
      control: 'number',
      description: '自动刷新间隔（毫秒）',
    },
    tableHeight: {
      control: 'number',
      description: '表格高度（像素）',
    },
    enableAdvancedSettings: {
      control: 'boolean',
      description: '是否显示高级设置',
    },
  },
  args: {
    baseUrl: 'http://*************:8880',
    apiKey: 'vIAV2wnkd0EdkyGz46qZ',
    autoConnect: false,
    showExportButtons: true,
    enableAutoRefresh: true,
    autoRefreshInterval: 30000,
    tableHeight: 650,
    enableAdvancedSettings: false,
  },
  parameters: {
    docs: {
      description: {
        component:
          'SeqLogViewer 是一个功能完整的 SEQ 日志查询组件，支持实时连接监控、查询过滤、数据导出、自动刷新等功能。适用于生产环境中的日志监控和分析。',
      },
    },
  },
} satisfies Meta<typeof SeqLogViewer>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  name: '基础示例',
  args: {
    baseUrl: 'http://*************:8880',
    apiKey: 'vIAV2wnkd0EdkyGz46qZ',
    autoConnect: false,
  },
  render: (args) => ({
    components: { SeqLogViewer },
    setup() {
      const handleEventSelected = (event: SeqEvent) => {
        console.log('选中事件:', event);
      };

      const handleQueryCompleted = (events: SeqEvent[]) => {
        console.log('查询完成，获得事件数量:', events.length);
      };

      const handleConnectionChanged = (connected: boolean) => {
        console.log('连接状态变化:', connected);
      };

      return {
        args,
        handleEventSelected,
        handleQueryCompleted,
        handleConnectionChanged,
      };
    },
    template: `
      <div style="height: 800px;">
        <SeqLogViewer 
          v-bind="args"
          @event-selected="handleEventSelected"
          @query-completed="handleQueryCompleted"
          @connection-changed="handleConnectionChanged"
        />
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: '基础的 SEQ 日志查询器示例，展示了组件的核心功能。',
      },
    },
  },
};

// 自定义配置示例
export const CustomConfig: Story = {
  name: '自定义配置',
  args: {
    baseUrl: 'http://*************:8880',
    apiKey: 'vIAV2wnkd0EdkyGz46qZ',
    autoConnect: true,
    autoRefreshInterval: 0,
    showExportButtons: true,
    enableAutoRefresh: false,
    tableHeight: 500,
  },
  render: (args) => ({
    components: { SeqLogViewer },
    setup() {
      return { args };
    },
    template: `
      <div style="height: 600px;">
        <SeqLogViewer v-bind="args" />
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story:
          '自定义配置示例，展示如何配置不同的 SEQ 服务器地址、API Key 和其他选项。',
      },
    },
  },
};

// 不同服务器配置
export const DifferentServers: Story = {
  name: '不同服务器配置',
  render: (args) => ({
    components: { SeqLogViewer },
    setup() {
      const servers = [
        {
          name: '本地开发服务器',
          config: {
            baseUrl: 'http://localhost:5341',
            autoConnect: false,
          },
        },
        {
          name: '测试环境',
          config: {
            baseUrl: 'http://*************:8880',
            apiKey: 'vIAV2wnkd0EdkyGz46qZ',
            autoConnect: false,
          },
        },
        {
          name: '生产环境',
          config: {
            baseUrl: 'https://seq.example.com',
            apiKey: 'production-api-key',
            autoConnect: false,
          },
        },
      ];

      const selectedServer = ref(0);

      return {
        args,
        servers,
        selectedServer,
      };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="padding: 16px; border: 1px solid #eee; border-radius: 8px; background: #f9f9f9;">
          <h3 style="margin: 0 0 16px 0;">服务器配置</h3>
          <div style="display: flex; gap: 10px; flex-wrap: wrap;">
            <button 
              v-for="(server, index) in servers" 
              :key="index"
              @click="selectedServer = index"
              :style="{
                padding: '8px 16px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                cursor: 'pointer',
                background: selectedServer === index ? '#2196F3' : '#ffffff',
                color: selectedServer === index ? '#ffffff' : '#333333'
              }"
            >
              {{ server.name }}
            </button>
          </div>
          <div style="margin-top: 16px; padding: 12px; background: #ffffff; border-radius: 4px; border: 1px solid #ddd;">
            <strong>当前配置:</strong>
            <pre style="margin: 8px 0 0 0;">{{ JSON.stringify(servers[selectedServer].config, null, 2) }}</pre>
          </div>
        </div>
        
        <div style="height: 700px;">
          <SeqLogViewer 
            :key="selectedServer"
            v-bind="args"
            :base-url="servers[selectedServer].config.baseUrl"
            :api-key="servers[selectedServer].config.apiKey"
            :auto-connect="servers[selectedServer].config.autoConnect"
          />
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story:
          '展示如何配置不同的 SEQ 服务器，包括本地开发、测试和生产环境的配置示例。',
      },
    },
  },
};

// 自动刷新示例
export const AutoRefresh: Story = {
  name: '自动刷新功能',
  args: {
    baseUrl: 'http://*************:8880',
    apiKey: 'vIAV2wnkd0EdkyGz46qZ',
    autoConnect: false,
    enableAutoRefresh: true,
    autoRefreshInterval: 10000, // 10秒
  },
  render: (args) => ({
    components: { SeqLogViewer },
    setup() {
      const lastRefreshTime = ref('--');
      const refreshCount = ref(0);

      const handleQueryCompleted = (events: SeqEvent[]) => {
        refreshCount.value++;
        lastRefreshTime.value = new Date().toLocaleTimeString('zh-CN');
      };

      return {
        args,
        lastRefreshTime,
        refreshCount,
        handleQueryCompleted,
      };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="padding: 16px; border: 1px solid #eee; border-radius: 8px; background: #f0f9ff;">
          <h3 style="margin: 0 0 12px 0;">自动刷新状态</h3>
          <div style="display: flex; gap: 24px;">
            <div>
              <strong>刷新间隔:</strong> {{ args.autoRefreshInterval / 1000 }} 秒
            </div>
            <div>
              <strong>最后刷新时间:</strong> {{ lastRefreshTime }}
            </div>
            <div>
              <strong>刷新次数:</strong> {{ refreshCount }}
            </div>
          </div>
          <div style="margin-top: 12px; font-size: 12px; color: #666;">
            <em>💡 启动组件后，请先手动点击"检查连接"并确保连接成功，然后启用自动刷新功能。</em>
          </div>
        </div>
        
        <div style="height: 700px;">
          <SeqLogViewer 
            v-bind="args"
            @query-completed="handleQueryCompleted"
          />
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: '展示自动刷新功能，可以配置刷新间隔，实时监控日志变化。',
      },
    },
  },
};

// 表格高度自定义
export const CustomTableHeight: Story = {
  name: '自定义表格高度',
  render: (args) => ({
    components: { SeqLogViewer },
    setup() {
      const tableHeight = ref(400);
      const heights = [300, 400, 500, 600, 700, 800];

      return {
        args,
        tableHeight,
        heights,
      };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="padding: 16px; border: 1px solid #eee; border-radius: 8px;">
          <h3 style="margin: 0 0 16px 0;">表格高度控制</h3>
          <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
            <span>表格高度:</span>
            <button 
              v-for="height in heights" 
              :key="height"
              @click="tableHeight = height"
              :style="{
                padding: '6px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                cursor: 'pointer',
                background: tableHeight === height ? '#2196F3' : '#ffffff',
                color: tableHeight === height ? '#ffffff' : '#333333'
              }"
            >
              {{ height }}px
            </button>
          </div>
          <div style="margin-top: 12px;">
            <span>当前高度: <strong>{{ tableHeight }}px</strong></span>
          </div>
        </div>
        
        <div :style="{ height: (tableHeight + 200) + 'px' }">
          <SeqLogViewer 
            v-bind="args"
            base-url="http://*************:8880"
            api-key="vIAV2wnkd0EdkyGz46qZ"
            :auto-connect="false"
            :tableHeight="tableHeight"
          />
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: '展示如何自定义表格高度，适应不同的页面布局需求。',
      },
    },
  },
};

// 事件处理示例
export const EventHandling: Story = {
  name: '事件处理示例',
  render: (args) => ({
    components: { SeqLogViewer },
    setup() {
      const logs = ref<string[]>([]);
      const maxLogs = 10;

      const addLog = (message: string) => {
        logs.value.unshift(`[${new Date().toLocaleTimeString()}] ${message}`);
        if (logs.value.length > maxLogs) {
          logs.value = logs.value.slice(0, maxLogs);
        }
      };

      const handleEventSelected = (event: SeqEvent) => {
        addLog(
          `选中事件: ${event.Level} - ${
            event.MessageTemplateTokens?.map((t) => t.Text).join('') || 'N/A'
          }`,
        );
      };

      const handleQueryCompleted = (events: SeqEvent[]) => {
        addLog(`查询完成: 获得 ${events.length} 条日志记录`);
      };

      const handleConnectionChanged = (connected: boolean) => {
        addLog(`连接状态变化: ${connected ? '已连接' : '已断开'}`);
      };

      const clearLogs = () => {
        logs.value = [];
      };

      return {
        args,
        logs,
        handleEventSelected,
        handleQueryCompleted,
        handleConnectionChanged,
        clearLogs,
      };
    },
    template: `
      <div style="display: flex; gap: 20px; height: 800px;">
        <div style="flex: 1;">
          <SeqLogViewer 
            v-bind="args"
            base-url="http://*************:8880"
            api-key="vIAV2wnkd0EdkyGz46qZ"
            :auto-connect="false"
            @event-selected="handleEventSelected"
            @query-completed="handleQueryCompleted"
            @connection-changed="handleConnectionChanged"
          />
        </div>
        
        <div style="width: 350px; border: 1px solid #eee; border-radius: 8px; padding: 16px; display: flex; flex-direction: column;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
            <h3 style="margin: 0;">事件日志</h3>
            <button 
              @click="clearLogs"
              style="padding: 4px 8px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;"
            >
              清空
            </button>
          </div>
          
          <div style="flex: 1; overflow-y: auto; background: #f9f9f9; border-radius: 4px; padding: 12px;">
            <div v-if="logs.length === 0" style="color: #999; font-style: italic; text-align: center;">
              暂无事件日志
            </div>
            <div 
              v-for="(log, index) in logs" 
              :key="index"
              style="margin-bottom: 8px; padding: 6px; background: white; border-radius: 4px; font-family: monospace; font-size: 12px; word-break: break-all;"
            >
              {{ log }}
            </div>
          </div>
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story:
          '展示如何处理组件的各种事件，包括事件选择、查询完成和连接状态变化。',
      },
    },
  },
};

// 功能配置示例
export const FeatureConfiguration: Story = {
  name: '功能配置',
  render: (args) => ({
    components: { SeqLogViewer },
    setup() {
      const config = reactive({
        showExportButtons: true,
        enableAutoRefresh: true,
        autoRefreshInterval: 30000,
        tableHeight: 650,
        showAdvancedSettings: false,
      });

      const seqConfig = {
        baseUrl: 'http://*************:8880',
        apiKey: 'vIAV2wnkd0EdkyGz46qZ',
        autoConnect: false,
      };

      return {
        args,
        config,
        seqConfig,
      };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="padding: 16px; border: 1px solid #eee; border-radius: 8px; background: #f9f9f9;">
          <h3 style="margin: 0 0 16px 0;">功能配置</h3>
          
          <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
            <div style="display: flex; align-items: center; gap: 8px;">
              <input 
                type="checkbox" 
                v-model="config.showExportButtons" 
                id="showExportButtons"
              />
              <label for="showExportButtons">显示导出按钮</label>
            </div>
            
            <div style="display: flex; align-items: center; gap: 8px;">
              <input 
                type="checkbox" 
                v-model="config.enableAutoRefresh" 
                id="enableAutoRefresh"
              />
              <label for="enableAutoRefresh">启用自动刷新</label>
            </div>
            
            <div style="display: flex; align-items: center; gap: 8px;">
              <input 
                type="checkbox" 
                v-model="config.showAdvancedSettings" 
                id="showAdvancedSettings"
              />
              <label for="showAdvancedSettings">显示高级设置</label>
            </div>
            
            <div style="display: flex; align-items: center; gap: 8px;">
              <label for="autoRefreshInterval">刷新间隔(秒):</label>
              <input 
                type="number" 
                v-model.number="config.autoRefreshInterval" 
                id="autoRefreshInterval"
                style="width: 80px; padding: 4px;"
                min="5000"
                step="1000"
                @input="config.autoRefreshInterval = config.autoRefreshInterval * 1000"
              />
            </div>
            
            <div style="display: flex; align-items: center; gap: 8px;">
              <label for="tableHeight">表格高度:</label>
              <input 
                type="range" 
                v-model.number="config.tableHeight" 
                id="tableHeight"
                min="300"
                max="800"
                style="flex: 1;"
              />
              <span style="min-width: 50px;">{{ config.tableHeight }}px</span>
            </div>
          </div>
        </div>
        
        <div style="height: 750px;">
          <SeqLogViewer 
            :base-url="seqConfig.baseUrl"
            :api-key="seqConfig.apiKey"
            :auto-connect="seqConfig.autoConnect"
            :showExportButtons="config.showExportButtons"
            :enableAutoRefresh="config.enableAutoRefresh"
            :autoRefreshInterval="config.autoRefreshInterval"
            :tableHeight="config.tableHeight"
            :showAdvancedSettings="config.showAdvancedSettings"
          />
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: '交互式功能配置示例，可以实时调整组件的各种功能开关和参数。',
      },
    },
  },
};
