import type { Meta, StoryObj } from '@storybook/vue3';
import DateTime from './index.vue';

const meta: Meta<typeof DateTime> = {
  title: 'Components/DateTime',
  component: DateTime,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          '日期时间展示组件，支持实时显示当前时间或显示指定时间，可自定义格式。',
      },
    },
  },
  argTypes: {
    value: {
      control: { type: 'text' },
      description:
        '指定的时间值，支持字符串、Date对象或时间戳。如果不传则显示当前时间',
    },
    format: {
      control: { type: 'text' },
      description:
        '时间格式字符串，支持 yyyy、MM、dd、HH、mm、ss、SSS 等占位符',
    },
    realTime: {
      control: { type: 'boolean' },
      description: '是否实时更新（仅在未传入value时生效）',
    },
    interval: {
      control: { type: 'number', min: 100, max: 10000, step: 100 },
      description: '更新间隔（毫秒）',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 默认实时时间
export const Default: Story = {
  args: {},
};

// 自定义格式的实时时间
export const CustomFormat: Story = {
  args: {
    format: 'yyyy年MM月dd日 HH:mm:ss',
  },
};

// 简单日期格式
export const DateOnly: Story = {
  args: {
    format: 'yyyy-MM-dd',
  },
};

// 时间格式
export const TimeOnly: Story = {
  args: {
    format: 'HH:mm:ss',
  },
};

// 包含毫秒的格式
export const WithMilliseconds: Story = {
  args: {
    format: 'HH:mm:ss.SSS',
  },
};

// 指定时间值
export const SpecificTime: Story = {
  args: {
    value: '2024-01-01 12:30:45',
    format: 'yyyy-MM-dd HH:mm:ss',
  },
};

// 使用时间戳
export const WithTimestamp: Story = {
  args: {
    value: 1704096645000, // 2024-01-01 12:30:45
    format: 'yyyy年MM月dd日 HH时mm分ss秒',
  },
};

// 快速更新间隔
export const FastUpdate: Story = {
  args: {
    interval: 100,
    format: 'HH:mm:ss.SSS',
  },
};

// 慢速更新间隔
export const SlowUpdate: Story = {
  args: {
    interval: 5000,
    format: 'HH:mm:ss',
  },
};

// 禁用实时更新
export const NoRealTime: Story = {
  args: {
    realTime: false,
  },
};
