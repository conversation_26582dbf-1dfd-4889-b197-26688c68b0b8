# MQTT自定义解析访问组件Props功能

## 概述

DCP组件库的MQTT自定义解析功能现在支持访问组件props，使得自定义解析函数能够根据组件的配置和状态进行更智能的数据处理。这个增强功能让MQTT消息解析变得更加灵活和强大。

## 功能特性

- ✅ **访问组件Props**: 自定义函数可以访问组件的所有props（除了dynamicPartInfo）
- ✅ **三参数函数**: 函数现在接收`(result, componentProps, index)`三个参数
- ✅ **智能过滤**: 自动排除dynamicPartInfo属性，避免循环引用
- ✅ **向后兼容**: 保持原有API的兼容性
- ✅ **类型安全**: 完整的TypeScript类型支持

## 函数参数详解

### 新的函数签名

```javascript
function parseMessage(result, componentProps, index) {
  // result: bodyPath下的数据对象
  // componentProps: 组件props对象（已排除dynamicPartInfo）
  // index: 多主题时的索引（单主题时为undefined）
  
  return processedData;
}
```

### 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| `result` | any | MQTT消息中bodyPath路径下的数据 |
| `componentProps` | Record<string, any> | 组件props对象（排除dynamicPartInfo） |
| `index` | number \| undefined | 多主题时的索引，单主题时为undefined |

## 使用示例

### 基本用法

```vue
<storage-box
  :rows="4"
  :columns="6"
  :showItemNames="true"
  :showGridNumbers="true"
  :items="[]"
  :dynamicPartInfo="{
    items: {
      type: 'mqtt',
      baseUrl: 'ws://*************:8083/mqtt',
      topic: 'guiMessage/component/s1,guiMessage/component/s2',
      qos: 1,
      bodyPath: 'Payload',
      parseType: 'func',
      contentFields: `function parseMessage(result, componentProps, index) {
        // 访问组件配置
        const totalSlots = componentProps.rows * componentProps.columns;
        const showNames = componentProps.showItemNames;
        
        // 基于配置的逻辑处理
        const slotName = showNames ? 'Slot ' + index : '';
        const isFirstHalf = index < totalSlots / 2;
        
        return result.MaterialInfos?.find(item =>
          item.CurrentModule === 'StorageBox' &&
          item.CurrentSlot === index
        )?.status || {
          status: 'empty',
          name: slotName,
          color: isFirstHalf ? '#e0e0e0' : '#f0f0f0'
        };
      }`
    }
  }"
/>
```

### 高级用法示例

#### 1. 基于组件尺寸的动态处理

```javascript
function parseMessage(result, componentProps, index) {
  const { rows, columns, width, height } = componentProps;
  const totalSlots = rows * columns;
  
  // 根据组件尺寸计算网格位置
  const row = Math.floor(index / columns);
  const col = index % columns;
  
  // 基于位置的特殊处理
  const isCorner = (row === 0 || row === rows - 1) && 
                   (col === 0 || col === columns - 1);
  
  return {
    ...result.data,
    position: { row, col },
    isCorner: isCorner,
    priority: isCorner ? 'high' : 'normal'
  };
}
```

#### 2. 基于显示配置的条件渲染

```javascript
function parseMessage(result, componentProps, index) {
  const { showItemNames, showGridNumbers, titleColor } = componentProps;
  
  // 根据显示配置决定返回的数据结构
  if (!showItemNames && !showGridNumbers) {
    return { status: 'empty' }; // 最简化显示
  }
  
  let name = '';
  if (showItemNames) name += `Item ${index}`;
  if (showGridNumbers) name += ` [${index}]`;
  
  return {
    ...result.itemData,
    name: name.trim(),
    textColor: titleColor || '#333333'
  };
}
```

#### 3. 复杂的业务逻辑处理

```javascript
function parseMessage(result, componentProps, index) {
  const { rows, columns, showItemNames } = componentProps;
  const totalSlots = rows * columns;
  
  // 查找对应的物品信息
  const item = result.MaterialInfos?.find(item =>
    item.CurrentModule === 'StorageBox' &&
    item.CurrentSlot === index
  );
  
  if (item) {
    return {
      ...item.status,
      slotName: showItemNames ? `Slot ${index}` : '',
      position: {
        row: Math.floor(index / columns),
        col: index % columns,
        isFirstHalf: index < totalSlots / 2
      },
      metadata: {
        totalSlots,
        componentSize: `${rows}x${columns}`,
        timestamp: new Date().toISOString()
      }
    };
  }
  
  // 默认空状态
  return {
    status: 'empty',
    name: showItemNames ? `Empty Slot ${index}` : '',
    color: index < totalSlots / 2 ? '#e0e0e0' : '#f0f0f0'
  };
}
```

## 可访问的Props

### StorageBox组件可访问的props

```javascript
{
  rows: 4,                    // 行数
  columns: 6,                 // 列数
  width: 500,                 // 宽度
  height: 350,                // 高度
  title: '收纳盒',            // 标题
  showTitle: true,            // 是否显示标题
  showItemNames: true,        // 是否显示物品名称
  showGridNumbers: true,      // 是否显示格子编号
  boxColor: '#f5f5f5',       // 盒体颜色
  titleColor: '#333333',     // 标题颜色
  titleFontSize: 16,         // 标题字体大小
  itemNameColor: '#666666',  // 物品名称颜色
  statusToColor: {...},      // 状态颜色映射
  // ... 其他props（除了dynamicPartInfo）
}
```

### 注意事项

- `dynamicPartInfo`属性会被自动排除，避免循环引用
- 所有其他props都可以正常访问
- Props值是实时的，反映组件的当前状态

## 实现原理

### 参数传递流程

1. **组件层面**: 组件调用`useComponentState`时传递props
2. **Hook层面**: `useComponentState`过滤props并传递给解析函数
3. **解析层面**: `parseMqttMessage`接收props并传递给自定义函数
4. **执行层面**: 自定义函数可以访问所有传递的props

### 代码流程

```typescript
// 1. 组件调用
useComponentState(
  props.partId,
  stateFactory,
  props.dynamicPartInfo,
  props // 传递完整的props
);

// 2. Hook内部过滤
const filteredProps = { ...componentProps };
delete filteredProps.dynamicPartInfo;

// 3. 调用解析函数
parseMqttMessage(message, config, filteredProps, index);

// 4. 执行自定义函数
executeParseFunction(contentFields, result, filteredProps, index);
```

## 性能考虑

- **Props过滤**: 只在需要时进行props过滤，避免不必要的对象复制
- **函数缓存**: 相同的函数代码只编译一次
- **浅拷贝**: 使用浅拷贝过滤props，提高性能
- **按需访问**: 只有使用`parseType: 'func'`时才传递props

## 调试技巧

### 查看可用Props

```javascript
function parseMessage(result, componentProps, index) {
  console.log('可用的props:', Object.keys(componentProps));
  console.log('组件配置:', componentProps);
  
  return result.data;
}
```

### 条件调试

```javascript
function parseMessage(result, componentProps, index) {
  // 只在特定条件下输出调试信息
  if (index === 0) {
    console.log('首个格子的props:', componentProps);
  }
  
  return processData(result, componentProps, index);
}
```

## 最佳实践

1. **合理使用Props**: 只访问需要的props，避免过度依赖
2. **提供默认值**: 为可能不存在的props提供默认值
3. **类型检查**: 在函数中进行必要的类型检查
4. **性能优化**: 避免在函数中进行复杂计算
5. **可读性**: 保持函数逻辑清晰，便于维护

## 兼容性

- ✅ 完全向后兼容：现有的两参数函数仍然有效
- ✅ 渐进增强：可以逐步迁移到三参数版本
- ✅ 类型安全：TypeScript提供完整的类型支持
- ✅ 错误处理：访问不存在的props不会导致错误

这个增强功能让MQTT自定义解析变得更加强大和灵活，能够根据组件的实际配置进行智能的数据处理！
