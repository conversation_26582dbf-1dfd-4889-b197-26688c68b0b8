import type { Meta, StoryObj } from '@storybook/vue3';
import { MultiColorLight } from './index';

const meta = {
  title: 'Equipment/MultiColorLight',
  component: MultiColorLight,
  tags: ['autodocs'],
  argTypes: {
    width: { control: 'number', description: '指示灯宽度（像素）' },
    height: { control: 'number', description: '指示灯高度（像素）' },
    lightList: {
      control: 'object',
      description: '灯光列表，每个灯包含颜色属性',
    },
    lightBorderStyle: {
      control: { type: 'text' },
      description: '自定义边框样式，可以是 Boolean 或者 字符串',
    },
  },
  args: {
    width: 60,
    height: 200,
    lightList: ['#FF0000', '#FFFF00', '#00FF00'],
  },
  parameters: {
    docs: {
      description: {
        component:
          'MultiColorLight组件用于显示多色指示灯，常用于设备状态显示，支持自定义灯光颜色和数量。',
      },
    },
    controls: { expanded: true },
  },
} satisfies Meta<typeof MultiColorLight>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  name: '基础示例',
  args: {},
};

// 自定义颜色
export const CustomColors: Story = {
  name: '自定义颜色',
  args: {
    lightList: ['#1677ff', '#52c41a', '#f5222d'],
  },
};

// 自定义边框样式
export const CustomBorder: Story = {
  name: '自定义边框样式',
  args: {
    lightList: ['#FF0000', '#FFFF00', '#00FF00'],
    lightBorderStyle: true,
  },
};

// 双色指示灯
export const TwoColors: Story = {
  name: '双色指示灯',
  args: {
    lightList: ['#FF0000', '#00FF00'],
  },
};

// 多色指示灯
export const MultiColors: Story = {
  name: '多色指示灯',
  args: {
    lightList: ['#FF0000', '#FF7F00', '#FFFF00', '#00FF00', '#0000FF'],
  },
};

// 不同尺寸
export const DifferentSizes: Story = {
  name: '不同尺寸',
  render: (args) => ({
    components: { MultiColorLight },
    setup() {
      return { args };
    },
    template: `
      <div style="display: flex; gap: 20px; flex-wrap: wrap;">
        <div style="text-align: center;">
          <MultiColorLight
            v-bind="args"
            :width="40"
            :height="120"
          />
          <div style="margin-top: 10px;">小型 (40x120)</div>
        </div>
        <div style="text-align: center;">
          <MultiColorLight
            v-bind="args"
            :width="60"
            :height="200"
          />
          <div style="margin-top: 10px;">标准 (60x200)</div>
        </div>
        <div style="text-align: center;">
          <MultiColorLight
            v-bind="args"
            :width="80"
            :height="280"
          />
          <div style="margin-top: 10px;">大型 (80x280)</div>
        </div>
      </div>
    `,
  }),
};
