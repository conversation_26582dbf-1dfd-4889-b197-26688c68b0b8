<template>
  <div class="conveyor-belts-container">
    <canvas ref="conveyorBeltCanvas"></canvas>
  </div>
</template>

<script lang="ts">
import {
  onMounted,
  ref,
  onUnmounted,
  watch,
  toRefs,
  defineComponent,
} from 'vue';
import * as PIXI from 'pixi.js';

interface Material {
  id: string;
  sprite: PIXI.Sprite;
  startElapsedTime: number; // 记录物料创建时的累计运行时间
}

export default defineComponent({
  name: 'Transfer',
  props: {
    width: {
      type: Number,
      default: 500,
    },
    height: {
      type: Number,
      default: 100,
    },
    rotate: {
      type: Number,
      default: 0,
    },
    speed: {
      type: Number,
      default: 10,
    },
    direction: {
      type: Number,
      default: 1,
    },
    running: {
      type: Boolean,
      default: false,
    },
    segmentCount: {
      type: Number,
      default: 30,
    },
    materialImgPath: {
      type: String,
      default: '',
    },
    materialWidth: {
      type: Number,
      default: 0,
    },
    materialHeight: {
      type: Number,
      default: 0,
    },
    listenMaterialCmd: {
      type: String,
      default: '',
    },
    listenMaterialRemoveCmd: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const {
      width,
      height,
      rotate,
      segmentCount,
      running,
      speed,
      direction,
      materialImgPath,
      materialWidth,
      materialHeight,
      listenMaterialCmd,
      listenMaterialRemoveCmd,
    } = toRefs(props);

    const conveyorBeltCanvas = ref<HTMLCanvasElement | null>(null);
    const app = ref<PIXI.Application | null>(null);
    let markers: PIXI.Container[] = [];
    let materials: Material[] = [];

    class MaterialPool {
      private pool: Material[] = [];

      acquire(id: string): Material {
        return this.pool.length > 0
          ? this.pool.pop()!
          : {
              id,
              sprite: new PIXI.Sprite(),
              startElapsedTime: 0, // 初始化为0
            };
      }

      release(material: Material) {
        app.value?.stage.removeChild(material.sprite);
        this.pool.push(material);
      }

      // 新增的方法，通过 id 移除物料
      releaseById(id: string): void {
        const materialIndex = materials.findIndex(
          (material) => material.id === id,
        );
        if (materialIndex !== -1) {
          const [material] = materials.splice(materialIndex, 1);
          app.value?.stage.removeChild(material.sprite);
          this.pool.push(material);
        } else {
          console.warn(`Material with id ${id} not found.`);
        }
      }
    }

    const transformInfo = ref({
      width: 0,
      height: 0,
      centerX: 0,
      centerY: 0,
    });

    const elapsedTime = ref(0); // 累计运行时间（以秒为单位）

    const initCanvas = () => {
      if (!conveyorBeltCanvas.value) {
        return;
      }

      // 清空 markers 和 materials
      markers = [];
      materials = [];
      materialPool['pool'] = []; // 清空物料池

      app.value = new PIXI.Application({
        view: conveyorBeltCanvas.value as HTMLCanvasElement,
        width: width.value,
        height: height.value,
        backgroundColor: 0xe8e8e8,
        backgroundAlpha: 0,
        antialias: true,
      });

      const centerX = width.value / 2;
      const centerY = height.value / 2;
      const margin = 15;

      transformInfo.value = {
        width: width.value,
        height: height.value,
        centerX,
        centerY,
      };

      // 创建金属边框
      const metalFrame = new PIXI.Graphics();
      metalFrame.lineStyle(15, 0xc0c0c0);
      metalFrame.drawRect(
        centerX - width.value / 2,
        centerY - height.value / 2,
        width.value,
        height.value,
      );

      // 创建传送带节点
      for (let i = 0; i < segmentCount.value; i++) {
        const segment = new PIXI.Container();

        const plasticBase = new PIXI.Graphics();
        plasticBase.beginFill(0x3a3a3a);

        const nodeHeight = 40;
        const nodeWidth = height.value - margin * 2;
        const teethHeight = 4;

        const teethCount = Math.max(5, Math.floor(nodeHeight / 10));
        const teethWidth = nodeHeight / teethCount;

        plasticBase.moveTo(-nodeWidth / 2, -nodeHeight / 2);
        for (let t = 0; t < teethCount; t++) {
          plasticBase.lineTo(
            -nodeWidth / 2 + t * teethWidth,
            -nodeHeight / 2 - teethHeight,
          );
          plasticBase.lineTo(
            -nodeWidth / 2 + (t + 0.5) * teethWidth,
            -nodeHeight / 2,
          );
          plasticBase.lineTo(
            -nodeWidth / 2 + (t + 1) * teethWidth,
            -nodeHeight / 2 - teethHeight,
          );
        }

        plasticBase.lineTo(nodeWidth / 2, nodeHeight / 2);
        for (let t = 0; t < teethCount; t++) {
          plasticBase.lineTo(
            nodeWidth / 2 - t * teethWidth,
            nodeHeight / 2 + teethHeight,
          );
          plasticBase.lineTo(
            nodeWidth / 2 - (t + 0.5) * teethWidth,
            nodeHeight / 2,
          );
          plasticBase.lineTo(
            nodeWidth / 2 - (t + 1) * teethWidth,
            nodeHeight / 2 + teethHeight,
          );
        }

        plasticBase.closePath();
        plasticBase.endFill();

        segment.addChild(plasticBase);

        const x =
          centerX - width.value / 2 + (i / segmentCount.value) * width.value;
        const y = centerY;
        segment.x = x;
        segment.y = y;
        segment.rotation = Math.PI / 2;

        app.value.stage.addChild(segment);
        markers.push(segment);
      }

      app.value.stage.addChild(metalFrame);

      // 根据需求决定是否需要重新创建物料
      // createMaterial('material1');
    };

    const materialPool = new MaterialPool();

    const createMaterial = (id: string) => {
      if (!app.value) return;

      const material = materialPool.acquire(id);

      // 加载图片纹理
      const texture = PIXI.Texture.from(materialImgPath.value);
      material.sprite = new PIXI.Sprite(texture);
      material.sprite.width = materialWidth.value;
      material.sprite.height = materialHeight.value;

      // 设置物料的起始累计时间
      material.startElapsedTime = elapsedTime.value;

      // 设置物料的初始位置
      material.sprite.x =
        transformInfo.value.centerX - transformInfo.value.width / 2;
      material.sprite.y =
        transformInfo.value.centerY - materialHeight.value / 2;

      // 可以根据需要调整图片大小
      // material.sprite.anchor.set(0.5);
      // material.sprite.scale.set(0.5); // 示例缩放比例

      app.value.stage.addChild(material.sprite);
      materials.push(material);
    };

    const animate = (delta: number) => {
      // 假设每帧 delta 是基于 60fps 的增量
      const deltaSeconds = delta / 60;
      // 累计实际经过的时间
      elapsedTime.value += deltaSeconds;

      const currentElapsed = elapsedTime.value;
      const { width, centerX } = transformInfo.value;

      // 计算传送带的移动速度（像素/秒）
      const conveyorSpeed = width / speed.value; // 如果 speed.value 是物料走完全程的秒数

      // 更新传送带段的位置
      markers.forEach((segment, index) => {
        const movement = conveyorSpeed * deltaSeconds * direction.value; // 移动距离
        let newX = segment.x + movement;

        // 环绕传送带
        if (direction.value > 0) {
          if (newX > centerX + width / 2) {
            newX -= width;
          }
        } else {
          if (newX < centerX - width / 2) {
            newX += width;
          }
        }

        segment.x = newX;

        const wobble = Math.sin(currentElapsed * 3 + index * 0.5) * 0.02;
        segment.scale.set(1 + wobble);
      });

      // 更新物料的位置
      materials.forEach((material) => {
        const progress =
          (currentElapsed - material.startElapsedTime) / speed.value;
        const dir = direction.value;

        if (dir > 0) {
          material.sprite.x = centerX - width / 2 + width * progress;
        } else {
          material.sprite.x = centerX + width / 2 - width * progress;
        }

        if (progress >= 1) {
          // 进度达到或超过1时，物料完成移动
          materialPool.release(material);
          materials = materials.filter((m) => m.id !== material.id);
        }
      });
    };

    // const handleWsMsg = (msg: WsMessage) => {
    //   const { Cmd, Par } = msg;
    //   if (Cmd === listenMaterialCmd.value) {
    //     createMaterial(Par as any);
    //   } else if (Cmd === listenMaterialRemoveCmd.value) {
    //     materialPool.releaseById(Par as any);
    //   }
    // };

    watch(
      () => [running.value, app.value?.ticker],
      ([newValue, _ticker]) => {
        if (newValue && _ticker) {
          app.value?.ticker.add(animate);
          app.value?.ticker.start();
        } else {
          app.value?.ticker.remove(animate);
          app.value?.ticker.stop();
        }
      },
      {
        immediate: true,
      },
    );

    onMounted(() => {
      // ws.addListener(`updateMaterial-${props.chartConfig.id}`, handleWsMsg);
      initCanvas();
    });

    onUnmounted(() => {
      // ws.removeListener(`updateMaterial-${props.chartConfig.id}`);
      app.value?.ticker.remove(animate);
      app.value?.ticker.destroy();
      markers = [];
      materials = [];
    });

    return {
      conveyorBeltCanvas,
    };
  },
});
</script>

<style scoped>
.conveyor-belts-container {
  transform: v-bind('"rotate(" + rotate + "deg)"');
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
