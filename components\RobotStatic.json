{"id": 1, "version": "0.0.17", "name": {"zh_CN": "静态机械臂"}, "component": "RobotStatic", "icon": "robot-hands", "description": "静态机械臂组件，可显示机械手臂或机械臂图标，支持晶圆展示", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "RobotStatic", "version": "0.0.17", "script": "http://*************:4874/@dcp/component-library@0.0.17/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "group": "DCP", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["type", "stateColor", "rotate", "waferName"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}}, "schema": {"properties": [{"label": {"zh_CN": "基本配置"}, "description": {"zh_CN": "机械臂的基本样式和类型配置"}, "content": [{"property": "type", "label": {"text": {"zh_CN": "图标类型"}}, "description": {"zh_CN": "机械臂图标类型，手臂或完整机械臂"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "robot", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "抓手", "value": "hand"}, {"label": "机械臂", "value": "robot"}]}}}, {"property": "state", "label": {"text": {"zh_CN": "状态"}}, "description": {"zh_CN": "机械臂当前状态"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "normal", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "正常", "value": "normal"}, {"label": "警告", "value": "warning"}, {"label": "错误", "value": "error"}, {"label": "空闲", "value": "idle"}]}}}, {"property": "stateColor", "label": {"text": {"zh_CN": "图标颜色"}}, "description": {"zh_CN": "机械臂图标的颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#409EFF", "widget": {"component": "ColorConfigurator", "props": {"showAlpha": true}}}, {"property": "rotate", "label": {"text": {"zh_CN": "旋转角度"}}, "description": {"zh_CN": "机械臂图标的旋转角度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 0, "widget": {"component": "SliderConfigurator", "props": {"min": 0, "max": 360, "step": 15}}}]}, {"label": {"zh_CN": "晶圆配置"}, "description": {"zh_CN": "当类型为手臂时的晶圆配置"}, "content": [{"property": "waferName", "label": {"text": {"zh_CN": "晶圆名称"}}, "description": {"zh_CN": "显示在晶圆上的文本名称"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "", "widget": {"component": "InputConfigurator"}, "hidden": {"type": "JSFunction", "value": "function hidden(p) { return p.type !== 'hand'; }"}}, {"property": "waferColor", "label": {"text": {"zh_CN": "晶圆颜色"}}, "description": {"zh_CN": "晶圆的背景颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#0088ff", "widget": {"component": "ColorConfigurator", "props": {"showAlpha": true}}, "hidden": {"type": "JSFunction", "value": "function hidden(p) { return p.type !== 'hand' || !p.waferName; }"}}]}], "events": {}}, "snippets": [{"name": {"zh_CN": "静态机械臂"}, "icon": "robot-hands", "screenshot": "", "snippetName": "RobotStatic", "schema": {"props": {"type": "robot", "state": "normal", "stateColor": "#409EFF", "rotate": 0}}}], "category": "DCP"}