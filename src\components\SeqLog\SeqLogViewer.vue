<template>
  <div class="seq-log-viewer">
    <!-- 新的搜索工具栏 -->
    <SearchToolbar
      :loading="loading"
      :checking-connection="checkingConnection"
      :connection-status="connectionStatus"
      :show-export-buttons="showExportButtons"
      :enable-auto-refresh="enableAutoRefresh"
      :auto-refresh-active="autoRefreshActive"
      :has-data="eventsList.length > 0"
      :enable-advanced-settings="enableAdvancedSettings"
      @search="handleSearch"
      @refresh="handleRefresh"
      @clear="handleClear"
      @check-connection="handleCheckConnection"
      @toggle-auto-refresh="toggleAutoRefresh"
      @export="handleExport"
    />

    <!-- 主要内容区域 -->
    <div class="content-area">
      <!-- 统计信息 -->
      <div v-if="statistics" class="stats-bar">
        <span>总计: {{ statistics.TotalMatches }} 条</span>
        <span>当前页: {{ currentPage }}</span>
        <span>每页: {{ queryParams.count }} 条</span>
        <span v-if="lastQueryTime"
          >查询时间: {{ formatTimestamp(lastQueryTime) }}</span
        >
      </div>

      <!-- 日志表格 -->
      <el-table
        v-if="eventsList && eventsList.length > 0"
        v-loading="loading"
        :data="eventsList"
        class="events-table"
        stripe
        @row-click="showEventDetail"
        element-loading-text="正在查询日志数据..."
        element-loading-background="rgba(255, 255, 255, 0.8)"
        :max-height="tableHeight"
      >
        <el-table-column
          prop="MessageTemplateTokens"
          label="消息"
          min-width="300"
        >
          <template #default="{ row }">
            <div class="message-cell">
              <div class="message-content-single-line">
                {{ getMessageText(row.MessageTemplateTokens) }}
              </div>
              <div v-if="row.Exception" class="exception-indicator">
                <!-- <el-icon><Warning /></el-icon> -->
                <el-icon>
                  <Icon icon="mdi:alert-circle" />
                </el-icon>
                异常
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="Timestamp" label="时间戳" width="180">
          <template #default="{ row }">
            <span class="timestamp">{{ formatTimestamp(row.Timestamp) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="Level" label="级别" width="120">
          <template #default="{ row }">
            <el-tag :type="getLevelType(row.Level)" size="small">
              {{ row.Level }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="Properties" label="属性" width="180">
          <template #default="{ row }">
            <div
              v-if="row.Properties && row.Properties.length > 0"
              class="properties-container"
            >
              <el-tag
                v-for="prop in row.Properties"
                :key="prop.Name"
                size="small"
                type="info"
                class="property-tag"
              >
                {{ prop.Value }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button
              size="small"
              type="primary"
              @click.stop="showEventDetail(row)"
              link
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页控件 -->
      <div
        v-if="statistics && statistics.TotalMatches > (queryParams.count || 0)"
        class="pagination-wrapper"
      >
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="queryParams.count"
          :total="statistics.TotalMatches"
          layout="total, prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && eventsList.length === 0" class="empty-state">
        <el-empty description="暂无日志数据">
          <template #image>
            <div class="empty-icon">
              <el-icon size="64">
                <Icon icon="mdi:file-outline" />
              </el-icon>
            </div>
          </template>
          <template #description>
            <p v-if="!connectionStatus.connected" class="empty-desc">
              请先检查 SEQ 服务连接状态
            </p>
            <p v-else class="empty-desc">尝试调整查询条件或时间范围</p>
          </template>
        </el-empty>
      </div>
    </div>

    <!-- 详情抽屉 -->
    <el-drawer
      v-model="detailVisible"
      title="日志事件详情"
      direction="rtl"
      size="50%"
      class="detail-drawer"
    >
      <EventDetail
        v-if="selectedEvent"
        :event="selectedEvent"
        @close="detailVisible = false"
      />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue';
import {
  ElMessage,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElEmpty,
  ElDrawer,
  ElTag,
  ElButton,
  ElIcon,
} from 'element-plus';
import { Icon } from '@iconify/vue';
// import { Warning, Document } from '@element-plus/icons-vue';
import { useSeqService } from './useSeqService';
import EventDetail from './EventDetail.vue';
import SearchToolbar from './components/SearchToolbar.vue';
import 'element-plus/dist/index.css';
import type {
  SeqEvent,
  SeqServiceConfig,
  MessageTemplateToken,
} from './useSeqService';
defineOptions({
  name: 'SeqLogViewer',
});

// Props 定义
interface Props {
  baseUrl?: string;
  apiKey?: string;
  autoConnect?: boolean;
  showExportButtons?: boolean;
  enableAutoRefresh?: boolean;
  autoRefreshInterval?: number;
  tableHeight?: number;
  enableAdvancedSettings?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  baseUrl: 'http://*************:8880',
  apiKey: 'vIAV2wnkd0EdkyGz46qZ',
  autoConnect: true,
  autoRefreshInterval: 30000,
  showExportButtons: true,
  enableAutoRefresh: true,
  tableHeight: 650,
  enableAdvancedSettings: false,
});

// 事件定义
const emit = defineEmits<{
  eventSelected: [event: SeqEvent];
  queryCompleted: [events: SeqEvent[]];
  connectionChanged: [connected: boolean];
}>();

// 使用 SEQ 服务
const serviceConfig: SeqServiceConfig = {
  baseUrl: props.baseUrl,
  apiKey: props.apiKey,
  autoConnect: props.autoConnect,
  autoRefreshInterval: props.autoRefreshInterval,
};
const {
  loading,
  checkingConnection,
  connectionStatus,
  queryParams,
  eventsList,
  statistics,
  checkConnection,
  queryEvents,
  refreshEvents,
  clearFilters,
  setTimeRange,
  startAutoRefresh,
  stopAutoRefresh,
  exportEvents,
  initialize,
  destroy,
} = useSeqService(serviceConfig);

// 组件状态
const detailVisible = ref(false);
const currentPage = ref(1);
const selectedEvent = ref<SeqEvent | null>(null);
const autoRefreshActive = ref(false);
const lastQueryTime = ref<string>('');

// 搜索工具栏的事件处理接口
interface SearchParams {
  keyword: string;
  levels: string[];
  timeRange?: [string, string];
  quickTime?: string;
  customFilter: string;
  count: number;
}

// 监听器
watch(
  () => connectionStatus.connected,
  (connected) => {
    emit('connectionChanged', connected);
  },
);

watch(eventsList, (events) => {
  emit('queryCompleted', events);
});

// 新的事件处理方法
const handleSearch = async (searchParams: SearchParams) => {
  // 更新查询参数
  queryParams.count = searchParams.count;

  // 构建过滤器查询
  const filterParts: string[] = [];

  // 添加自定义过滤器
  if (searchParams.customFilter && searchParams.customFilter.trim()) {
    filterParts.push(`(${searchParams.customFilter.trim()})`);
  }

  // 添加级别过滤
  if (searchParams.levels && searchParams.levels.length > 0) {
    if (searchParams.levels.length === 1) {
      filterParts.push(`@Level = '${searchParams.levels[0]}'`);
    } else {
      const levelFilter = searchParams.levels
        .map((level) => `'${level}'`)
        .join(', ');
      filterParts.push(`@Level in [${levelFilter}]`);
    }
  }

  // 添加关键词搜索
  if (searchParams.keyword && searchParams.keyword.trim()) {
    const searchText = searchParams.keyword.trim().replace(/'/g, "''");
    filterParts.push(`@Message like '%${searchText}%'`);
  }

  // 设置时间范围
  if (searchParams.timeRange) {
    setTimeRange(searchParams.timeRange[0], searchParams.timeRange[1]);
  } else {
    setTimeRange();
  }

  // 设置最终过滤器
  queryParams.filter = filterParts.length > 0 ? filterParts.join(' and ') : '';

  // 执行查询
  const events = await queryEvents();
  lastQueryTime.value = new Date().toISOString();
  currentPage.value = 1;
  return events;
};

const handleCheckConnection = async () => {
  const connected = await checkConnection();
  if (connected && autoRefreshActive.value) {
    startAutoRefresh(props.autoRefreshInterval);
  }
};

const handleRefresh = () => {
  refreshEvents();
};

const handleClear = () => {
  clearFilters();
  lastQueryTime.value = '';
  currentPage.value = 1;
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  // 注意：这里需要API支持分页参数
  refreshEvents();
};

const showEventDetail = (event: SeqEvent) => {
  selectedEvent.value = event;
  detailVisible.value = true;
  emit('eventSelected', event);
};

const toggleAutoRefresh = () => {
  if (autoRefreshActive.value) {
    stopAutoRefresh();
    autoRefreshActive.value = false;
    ElMessage.info('已停止自动刷新');
  } else {
    if (connectionStatus.connected) {
      startAutoRefresh(props.autoRefreshInterval);
      autoRefreshActive.value = true;
      ElMessage.success(
        `已启动自动刷新 (${props.autoRefreshInterval / 1000}秒)`,
      );
    } else {
      ElMessage.error('请先检查连接状态');
    }
  }
};

const handleExport = (format: 'json' | 'csv') => {
  const data = exportEvents(format);
  const blob = new Blob([data], {
    type: format === 'json' ? 'application/json' : 'text/csv',
  });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `seq-logs-${new Date().toISOString().split('T')[0]}.${format}`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
  ElMessage.success(`已导出 ${eventsList.value.length} 条日志`);
};

const formatTimestamp = (timestamp: string): string => {
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

const getLevelType = (level: string) => {
  const levelMap: Record<string, any> = {
    Verbose: 'info',
    Debug: 'info',
    Information: 'success',
    Warning: 'warning',
    Error: 'danger',
    Fatal: 'danger',
  };
  return levelMap[level] || 'info';
};

const getMessageText = (tokens: MessageTemplateToken[]): string => {
  if (!tokens || tokens.length === 0) return '';
  return tokens.map((token) => token.Text).join('');
};

const setQuickTimeRange = (hours: number) => {
  const now = new Date();
  const start = new Date(now.getTime() - hours * 60 * 60 * 1000);

  setTimeRange(start.toISOString(), now.toISOString());
};

// 生命周期
onMounted(async () => {
  await initialize();
  if (props.enableAutoRefresh) {
    autoRefreshActive.value = true;
  }
});

onUnmounted(() => {
  destroy();
});

// 暴露方法给父组件
defineExpose({
  query: handleSearch,
  refresh: handleRefresh,
  clear: handleClear,
  checkConnection: handleCheckConnection,
  exportData: handleExport,
});
</script>

<style scoped>
.seq-log-viewer {
  background: #ffffff;
  color: #303133;
  display: flex;
  flex-direction: column;
}

/* 为新的搜索工具栏预留间距 */
.seq-log-viewer > *:first-child {
  margin-bottom: 16px;
}

.content-area {
  flex: 1;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.stats-bar {
  padding: 12px 20px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  gap: 24px;
  font-size: 12px;
  color: #606266;
}

.events-table {
  background: transparent;
}

.events-table :deep(.el-table__header) {
  background: #fafafa;
}

.events-table :deep(.el-table__header th) {
  background: #fafafa;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 600;
}

.events-table :deep(.el-table__body tr) {
  background: #ffffff;
  color: #303133;
  cursor: pointer;
  transition: all 0.2s ease;
}

.events-table :deep(.el-table__body tr:hover) {
  background: #f5f7fa;
}

.events-table :deep(.el-table__body tr.el-table__row--striped) {
  background: #fafafa;
}

.events-table :deep(.el-table__body tr.el-table__row--striped:hover) {
  background: #f5f7fa;
}

.events-table :deep(.el-table__body td) {
  border-bottom: 1px solid #ebeef5;
}

.timestamp {
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  font-size: 11px;
  color: #909399;
}

.message-cell {
  line-height: 1.4;
  display: flex;
  align-items: center;
  gap: 8px;
}

.message-content {
  word-break: break-word;
  margin-bottom: 4px;
}

.message-content-single-line {
  word-break: break-word;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.exception-indicator {
  font-size: 11px;
  color: #f56c6c;
  font-style: italic;
  display: flex;
  align-items: center;
  gap: 4px;
}

.trace-id {
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  font-size: 11px;
  color: #909399;
}

.properties-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 60px;
  overflow-y: auto;
}

.property-tag {
  font-size: 11px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 130px;
}

.pagination-wrapper {
  padding: 16px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e4e7ed;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  color: #c0c4cc;
}

.empty-desc {
  color: #909399;
  margin: 8px 0;
}

/* Element Plus 亮色主题覆盖 */
:deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  background: #ffffff;
  border: 1px solid #dcdfe6;
  box-shadow: none;
}

:deep(.el-input__wrapper:hover) {
  border-color: #409eff;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2);
}

:deep(.el-input__inner) {
  color: #303133;
}

:deep(.el-input__inner::placeholder) {
  color: #a8abb2;
}

:deep(.el-select .el-input__wrapper) {
  background: #ffffff;
}

:deep(.el-date-editor) {
  background: #ffffff;
  border: 1px solid #dcdfe6;
}

:deep(.el-range-separator) {
  color: #606266;
}

:deep(.el-pagination) {
  --el-pagination-bg-color: transparent;
  --el-pagination-text-color: #303133;
}

:deep(.el-pager li) {
  background: #ffffff;
  color: #303133;
  border: 1px solid #dcdfe6;
}

:deep(.el-pager li.is-active) {
  background: #409eff;
  border-color: #409eff;
  color: #ffffff;
}

:deep(.el-drawer) {
  background: #ffffff;
}

:deep(.el-drawer__header) {
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  color: #303133;
  margin-bottom: 0;
  padding: 20px;
}

:deep(.el-drawer__body) {
  padding: 0;
}
</style>
