<template>
  <div
    ref="container"
    :style="{ width: `${canvasWidth}px`, height: `${canvasHeight}px` }"
  ></div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  ref,
  onMounted,
  onUnmounted,
  watch,
  computed,
  toRefs,
} from 'vue';
import * as PIXI from 'pixi.js';
import { useComponentState } from '@/communication/hooks/useComponentState';
import { createComponentProps } from '@/context/ComponentContext';

interface ShelfItem {
  name: string;
  status: string;
}

export default defineComponent({
  name: 'Shelf',
  props: createComponentProps({
    width: {
      type: Number,
      default: 200,
    },
    height: {
      type: Number,
      default: 150,
    },
    depth: {
      type: Number,
      default: 100,
    },
    borderColor: {
      type: String,
      default: '#2C3E50',
    },
    columns: {
      type: Number,
      default: 3,
    },
    rows: {
      type: Number,
      default: 2,
    },
    items: {
      type: Array as PropType<ShelfItem[]>,
      default: () => [],
    },
    /** 状态到颜色的映射 */
    statusToColor: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({
        Idled: '#909399',
        Processing: '#409EFF',
        Completed: '#67C23A',
        Aborted: '#E6A23C',
        Failed: '#DC3545',
        Returning: '#9370DB',
        Returned: '#B0C4DE',
        CompleteReturning: '#48D1CC',
      }),
    },
    showBackPanel: {
      type: Boolean,
      default: true,
    },
    shelfColor: {
      type: String,
      default: '#DDEEFF',
    },
    showDebugInfo: {
      type: Boolean,
      default: false,
    },
    itemStartPosition: {
      type: String as PropType<
        'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
      >,
      default: 'top-left',
      description: '物品排列的起始位置：左上、右上、左下、右下',
    },
  }),
  setup(props) {
    const { items } = toRefs(props);

    // 使用组件状态Hook（响应式工厂函数版本）
    const { state } = useComponentState(
      props.partId,
      () => ({
        items: items.value,
      }),
      props.dynamicPartInfo,
    );

    const container = ref<HTMLDivElement>();
    let app: PIXI.Application | null = null;
    let shelfContainer: PIXI.Container;

    // 斜二测投影参数
    const ISO_ANGLE = Math.PI / 6; // 30度角
    const ISO_SCALE = 0.5; // 深度缩放比例

    // 计算画布尺寸
    const canvasWidth = computed(() => {
      return props.width + props.depth * ISO_SCALE * Math.cos(ISO_ANGLE) + 40;
    });

    const canvasHeight = computed(() => {
      return props.height + props.depth * ISO_SCALE * Math.sin(ISO_ANGLE) + 40;
    });

    // 斜二测投影变换函数
    const isometricTransform = (x: number, y: number, z: number) => {
      return {
        x: x + z * ISO_SCALE * Math.cos(ISO_ANGLE),
        y: y + z * ISO_SCALE * Math.sin(ISO_ANGLE),
      };
    };

    // 绘制货架框架
    const drawShelfFrame = () => {
      const frameContainer = new PIXI.Container();

      // 绘制背板（可选）- 最先绘制，在最后面
      if (props.showBackPanel) {
        drawBackPanel(frameContainer);
      }

      // 按视觉深度顺序绘制立柱和货架板层
      // 1. 先绘制右后立柱（视觉上最远，被货架板层遮挡）
      drawSinglePost(frameContainer, { x: props.width, z: props.depth });

      // 2. 绘制右前立柱（视觉上中后位置，部分被货架板层遮挡）
      drawSinglePost(frameContainer, { x: props.width, z: 0 });

      // 3. 绘制货架板层 - 中间层
      drawShelfBoards(frameContainer);

      // 4. 绘制左前立柱（视觉上最接近观察者）
      drawSinglePost(frameContainer, { x: 0, z: 0 });

      // 5. 最后绘制左后立柱（视觉上中前位置，在货架板层前方）
      drawSinglePost(frameContainer, { x: 0, z: props.depth });

      shelfContainer.addChild(frameContainer);
    };

    // 绘制单个立柱
    const drawSinglePost = (
      container: PIXI.Container,
      post: { x: number; z: number },
    ) => {
      const postWidth = 6;
      const postDepth = 6;
      const graphics = new PIXI.Graphics();

      // 立柱正面（较亮）
      graphics.beginFill(0xcccccc);
      const frontTopLeft = isometricTransform(
        post.x - postWidth / 2,
        0,
        post.z - postDepth / 2,
      );
      const frontTopRight = isometricTransform(
        post.x + postWidth / 2,
        0,
        post.z - postDepth / 2,
      );
      const frontBottomLeft = isometricTransform(
        post.x - postWidth / 2,
        props.height,
        post.z - postDepth / 2,
      );
      const frontBottomRight = isometricTransform(
        post.x + postWidth / 2,
        props.height,
        post.z - postDepth / 2,
      );

      graphics.moveTo(frontTopLeft.x, frontTopLeft.y);
      graphics.lineTo(frontTopRight.x, frontTopRight.y);
      graphics.lineTo(frontBottomRight.x, frontBottomRight.y);
      graphics.lineTo(frontBottomLeft.x, frontBottomLeft.y);
      graphics.lineTo(frontTopLeft.x, frontTopLeft.y);
      graphics.endFill();

      // 立柱右侧面（较暗）
      graphics.beginFill(0x999999);
      const rightTopFront = isometricTransform(
        post.x + postWidth / 2,
        0,
        post.z - postDepth / 2,
      );
      const rightTopBack = isometricTransform(
        post.x + postWidth / 2,
        0,
        post.z + postDepth / 2,
      );
      const rightBottomFront = isometricTransform(
        post.x + postWidth / 2,
        props.height,
        post.z - postDepth / 2,
      );
      const rightBottomBack = isometricTransform(
        post.x + postWidth / 2,
        props.height,
        post.z + postDepth / 2,
      );

      graphics.moveTo(rightTopFront.x, rightTopFront.y);
      graphics.lineTo(rightTopBack.x, rightTopBack.y);
      graphics.lineTo(rightBottomBack.x, rightBottomBack.y);
      graphics.lineTo(rightBottomFront.x, rightBottomFront.y);
      graphics.lineTo(rightTopFront.x, rightTopFront.y);
      graphics.endFill();

      // 立柱顶面（最亮）
      graphics.beginFill(0xe0e0e0);
      const topFrontLeft = isometricTransform(
        post.x - postWidth / 2,
        0,
        post.z - postDepth / 2,
      );
      const topFrontRight = isometricTransform(
        post.x + postWidth / 2,
        0,
        post.z - postDepth / 2,
      );
      const topBackLeft = isometricTransform(
        post.x - postWidth / 2,
        0,
        post.z + postDepth / 2,
      );
      const topBackRight = isometricTransform(
        post.x + postWidth / 2,
        0,
        post.z + postDepth / 2,
      );

      graphics.moveTo(topFrontLeft.x, topFrontLeft.y);
      graphics.lineTo(topFrontRight.x, topFrontRight.y);
      graphics.lineTo(topBackRight.x, topBackRight.y);
      graphics.lineTo(topBackLeft.x, topBackLeft.y);
      graphics.lineTo(topFrontLeft.x, topFrontLeft.y);
      graphics.endFill();

      // 添加立柱边框
      graphics.lineStyle(1, props.borderColor, 0.8);
      graphics.moveTo(frontTopLeft.x, frontTopLeft.y);
      graphics.lineTo(frontTopRight.x, frontTopRight.y);
      graphics.lineTo(frontBottomRight.x, frontBottomRight.y);
      graphics.lineTo(frontBottomLeft.x, frontBottomLeft.y);
      graphics.lineTo(frontTopLeft.x, frontTopLeft.y);

      container.addChild(graphics);
    };

    // 绘制货架板层
    const drawShelfBoards = (container: PIXI.Container) => {
      const boardThickness = 4;
      const cellHeight = props.height / props.rows;

      // 绘制每一层货架板（包括顶层和底层）
      for (let row = 0; row <= props.rows; row++) {
        const y = row * cellHeight;
        const graphics = new PIXI.Graphics();

        // 货架板顶面（最亮）
        const boardColor = parseInt(props.shelfColor.replace('#', ''), 16);
        graphics.beginFill(boardColor);
        const topFrontLeft = isometricTransform(0, y, 0);
        const topFrontRight = isometricTransform(props.width, y, 0);
        const topBackLeft = isometricTransform(0, y, props.depth);
        const topBackRight = isometricTransform(props.width, y, props.depth);

        graphics.moveTo(topFrontLeft.x, topFrontLeft.y);
        graphics.lineTo(topFrontRight.x, topFrontRight.y);
        graphics.lineTo(topBackRight.x, topBackRight.y);
        graphics.lineTo(topBackLeft.x, topBackLeft.y);
        graphics.lineTo(topFrontLeft.x, topFrontLeft.y);
        graphics.endFill();

        // 货架板前侧面（中等亮度）
        graphics.beginFill(0xd0d0d0);
        const frontTop = isometricTransform(0, y, 0);
        const frontBottom = isometricTransform(0, y + boardThickness, 0);
        const frontTopRight = isometricTransform(props.width, y, 0);
        const frontBottomRight = isometricTransform(
          props.width,
          y + boardThickness,
          0,
        );

        graphics.moveTo(frontTop.x, frontTop.y);
        graphics.lineTo(frontTopRight.x, frontTopRight.y);
        graphics.lineTo(frontBottomRight.x, frontBottomRight.y);
        graphics.lineTo(frontBottom.x, frontBottom.y);
        graphics.lineTo(frontTop.x, frontTop.y);
        graphics.endFill();

        // 货架板右侧面（较暗）
        graphics.beginFill(0xb0b0b0);
        const rightTop = isometricTransform(props.width, y, 0);
        const rightBottom = isometricTransform(
          props.width,
          y + boardThickness,
          0,
        );
        const rightTopBack = isometricTransform(props.width, y, props.depth);
        const rightBottomBack = isometricTransform(
          props.width,
          y + boardThickness,
          props.depth,
        );

        graphics.moveTo(rightTop.x, rightTop.y);
        graphics.lineTo(rightTopBack.x, rightTopBack.y);
        graphics.lineTo(rightBottomBack.x, rightBottomBack.y);
        graphics.lineTo(rightBottom.x, rightBottom.y);
        graphics.lineTo(rightTop.x, rightTop.y);
        graphics.endFill();

        // 添加货架板边框
        graphics.lineStyle(1, props.borderColor, 0.6);
        graphics.moveTo(topFrontLeft.x, topFrontLeft.y);
        graphics.lineTo(topFrontRight.x, topFrontRight.y);
        graphics.lineTo(topBackRight.x, topBackRight.y);
        graphics.lineTo(topBackLeft.x, topBackLeft.y);
        graphics.lineTo(topFrontLeft.x, topFrontLeft.y);

        container.addChild(graphics);
      }
    };

    // 绘制背板
    const drawBackPanel = (container: PIXI.Container) => {
      const graphics = new PIXI.Graphics();

      // 背板面（较暗的灰色）
      graphics.beginFill(0xa0a0a0, 0.3);
      const backTopLeft = isometricTransform(0, 0, props.depth);
      const backTopRight = isometricTransform(props.width, 0, props.depth);
      const backBottomLeft = isometricTransform(0, props.height, props.depth);
      const backBottomRight = isometricTransform(
        props.width,
        props.height,
        props.depth,
      );

      graphics.moveTo(backTopLeft.x, backTopLeft.y);
      graphics.lineTo(backTopRight.x, backTopRight.y);
      graphics.lineTo(backBottomRight.x, backBottomRight.y);
      graphics.lineTo(backBottomLeft.x, backBottomLeft.y);
      graphics.lineTo(backTopLeft.x, backTopLeft.y);
      graphics.endFill();

      // 背板边框
      graphics.lineStyle(1, props.borderColor, 0.4);
      graphics.moveTo(backTopLeft.x, backTopLeft.y);
      graphics.lineTo(backTopRight.x, backTopRight.y);
      graphics.lineTo(backBottomRight.x, backBottomRight.y);
      graphics.lineTo(backBottomLeft.x, backBottomLeft.y);
      graphics.lineTo(backTopLeft.x, backTopLeft.y);

      container.addChild(graphics);
    };

    // 绘制网格分隔线
    const drawGrid = () => {
      const graphics = new PIXI.Graphics();
      graphics.lineStyle(1, props.borderColor, 0.4);

      const cellWidth = props.width / props.columns;
      const cellHeight = props.height / props.rows;

      // 只在货架板层上绘制垂直分隔线
      for (let i = 1; i < props.columns; i++) {
        const x = i * cellWidth;

        // 在每个货架板层上绘制分隔线
        for (let row = 0; row <= props.rows; row++) {
          const y = row * cellHeight;
          const frontPoint = isometricTransform(x, y, 0);
          const backPoint = isometricTransform(x, y, props.depth);

          graphics.moveTo(frontPoint.x, frontPoint.y);
          graphics.lineTo(backPoint.x, backPoint.y);
        }
      }

      shelfContainer.addChild(graphics);
    };

    // 验证物品位置的合理性
    const validateItemPosition = (row: number, column: number): boolean => {
      return (
        row >= 0 && row < props.rows && column >= 0 && column < props.columns
      );
    };

    // 根据起始位置和索引计算行列位置
    const calculateItemPosition = (index: number, startPosition: string) => {
      switch (startPosition) {
        case 'top-left':
          // 从左上开始，从左到右、从上到下
          return {
            row: props.rows - 1 - Math.floor(index / props.columns),
            column: index % props.columns,
          };

        case 'top-right':
          // 从右上开始，从右到左、从上到下
          return {
            row: props.rows - 1 - Math.floor(index / props.columns),
            column: props.columns - 1 - (index % props.columns),
          };

        case 'bottom-left':
          // 从左下开始，从左到右、从下到上
          return {
            row: Math.floor(index / props.columns),
            column: index % props.columns,
          };

        case 'bottom-right':
          // 从右下开始，从右到左、从下到上
          return {
            row: Math.floor(index / props.columns),
            column: props.columns - 1 - (index % props.columns),
          };

        default:
          // 默认使用左上角模式
          return {
            row: props.rows - 1 - Math.floor(index / props.columns),
            column: index % props.columns,
          };
      }
    };

    // 绘制物品
    const drawItems = () => {
      state.items.forEach((item, index) => {
        if (!item || !item.status || item.status === 'empty') {
          return;
        }

        // 根据起始位置配置和数组索引计算行列位置
        const { row, column } = calculateItemPosition(
          index,
          props.itemStartPosition,
        );

        // 验证位置合理性
        if (!validateItemPosition(row, column)) {
          console.warn(
            `物品 "${item.name}" 的位置超出货架范围: index=${index}, row=${row}, column=${column}`,
          );
          return;
        }

        const cellWidth = props.width / props.columns;
        const cellHeight = props.height / props.rows;

        // 物品尺寸
        const itemWidth = cellWidth * 0.4;
        const itemHeight = cellHeight * 0.3;
        const itemDepth = props.depth * 0.35;

        // 精确计算物品位置
        const boardThickness = 4;

        // 物品X位置：在对应列中居中放置
        const centerX = (column + 0.5) * cellWidth;

        // 物品Y位置计算：
        // 1. 计算货架板的Y位置（从顶部向下，row=0为顶层）
        const shelfBoardY = (props.rows - row) * cellHeight;
        // 2. 物品放置在货架板上方，物品顶部位置
        const itemY = shelfBoardY - itemHeight - boardThickness / 2;

        // 物品Z位置：在深度方向略偏前方，便于观察和标签显示
        const centerZ = props.depth * 0.4;

        // 根据状态确定颜色
        let baseColor: number;
        let lightColor: number;
        let darkColor: number;

        const colorHex = props.statusToColor[item.status] || '#909399';
        baseColor = parseInt(colorHex.replace('#', ''), 16);
        lightColor = baseColor + 0x111111;
        darkColor = baseColor - 0x111111;

        const graphics = new PIXI.Graphics();

        // 绘制阴影（在货架板表面）
        const shadowOffset = 1;
        const shadowOpacity = 0.12;
        graphics.beginFill(0x000000, shadowOpacity);
        // 阴影应该投射在货架板的表面上
        const shadowY = shelfBoardY + shadowOffset; // 阴影位置在货架板表面
        const shadowFrontLeft = isometricTransform(
          centerX - itemWidth / 2 + shadowOffset,
          shadowY,
          centerZ - itemDepth / 2 + shadowOffset,
        );
        const shadowFrontRight = isometricTransform(
          centerX + itemWidth / 2 + shadowOffset,
          shadowY,
          centerZ - itemDepth / 2 + shadowOffset,
        );
        const shadowBackLeft = isometricTransform(
          centerX - itemWidth / 2 + shadowOffset,
          shadowY,
          centerZ + itemDepth / 2 + shadowOffset,
        );
        const shadowBackRight = isometricTransform(
          centerX + itemWidth / 2 + shadowOffset,
          shadowY,
          centerZ + itemDepth / 2 + shadowOffset,
        );

        graphics.moveTo(shadowFrontLeft.x, shadowFrontLeft.y);
        graphics.lineTo(shadowFrontRight.x, shadowFrontRight.y);
        graphics.lineTo(shadowBackRight.x, shadowBackRight.y);
        graphics.lineTo(shadowBackLeft.x, shadowBackLeft.y);
        graphics.lineTo(shadowFrontLeft.x, shadowFrontLeft.y);
        graphics.endFill();

        // 绘制3D盒子 - 使用更精确的坐标计算
        // 顶面（最亮）
        graphics.beginFill(lightColor);
        const topFrontLeft = isometricTransform(
          centerX - itemWidth / 2,
          itemY,
          centerZ - itemDepth / 2,
        );
        const topFrontRight = isometricTransform(
          centerX + itemWidth / 2,
          itemY,
          centerZ - itemDepth / 2,
        );
        const topBackLeft = isometricTransform(
          centerX - itemWidth / 2,
          itemY,
          centerZ + itemDepth / 2,
        );
        const topBackRight = isometricTransform(
          centerX + itemWidth / 2,
          itemY,
          centerZ + itemDepth / 2,
        );

        graphics.moveTo(topFrontLeft.x, topFrontLeft.y);
        graphics.lineTo(topFrontRight.x, topFrontRight.y);
        graphics.lineTo(topBackRight.x, topBackRight.y);
        graphics.lineTo(topBackLeft.x, topBackLeft.y);
        graphics.lineTo(topFrontLeft.x, topFrontLeft.y);
        graphics.endFill();

        // 前面（中等亮度）
        graphics.beginFill(baseColor);
        const frontBottomLeft = isometricTransform(
          centerX - itemWidth / 2,
          itemY + itemHeight,
          centerZ - itemDepth / 2,
        );
        const frontBottomRight = isometricTransform(
          centerX + itemWidth / 2,
          itemY + itemHeight,
          centerZ - itemDepth / 2,
        );

        graphics.moveTo(topFrontLeft.x, topFrontLeft.y);
        graphics.lineTo(topFrontRight.x, topFrontRight.y);
        graphics.lineTo(frontBottomRight.x, frontBottomRight.y);
        graphics.lineTo(frontBottomLeft.x, frontBottomLeft.y);
        graphics.lineTo(topFrontLeft.x, topFrontLeft.y);
        graphics.endFill();

        // 右侧面（较暗）
        graphics.beginFill(darkColor);
        const rightBottomBack = isometricTransform(
          centerX + itemWidth / 2,
          itemY + itemHeight,
          centerZ + itemDepth / 2,
        );

        graphics.moveTo(topFrontRight.x, topFrontRight.y);
        graphics.lineTo(topBackRight.x, topBackRight.y);
        graphics.lineTo(rightBottomBack.x, rightBottomBack.y);
        graphics.lineTo(frontBottomRight.x, frontBottomRight.y);
        graphics.lineTo(topFrontRight.x, topFrontRight.y);
        graphics.endFill();

        // 添加物品边框 - 绘制所有可见边线
        graphics.lineStyle(1, 0x000000, 0.4);
        // 顶面边框
        graphics.moveTo(topFrontLeft.x, topFrontLeft.y);
        graphics.lineTo(topFrontRight.x, topFrontRight.y);
        graphics.lineTo(topBackRight.x, topBackRight.y);
        graphics.lineTo(topBackLeft.x, topBackLeft.y);
        graphics.lineTo(topFrontLeft.x, topFrontLeft.y);

        // 前面和右侧面的垂直边线
        graphics.moveTo(topFrontLeft.x, topFrontLeft.y);
        graphics.lineTo(frontBottomLeft.x, frontBottomLeft.y);
        graphics.moveTo(topFrontRight.x, topFrontRight.y);
        graphics.lineTo(frontBottomRight.x, frontBottomRight.y);
        graphics.moveTo(topBackRight.x, topBackRight.y);
        graphics.lineTo(rightBottomBack.x, rightBottomBack.y);

        // 添加文字标签（显示在物品前方的货架板上）
        const labelPosition = isometricTransform(
          centerX,
          shelfBoardY + 2, // 标签位置在货架板表面略上方
          centerZ - itemDepth / 2 - 8, // 标签显示在物品前方，距离稍远
        );
        const text = new PIXI.Text(item.name, {
          fontFamily: 'Arial',
          fontSize: Math.max(8, Math.min(12, cellWidth * 0.08)), // 根据格子大小动态调整字体
          fill: '#333333',
          align: 'center',
          fontWeight: '500',
        });
        text.anchor.set(0.5);
        text.position.set(labelPosition.x, labelPosition.y);

        // 添加交互效果
        graphics.interactive = true;
        graphics.cursor = 'pointer';

        // 存储原始字体大小
        const originalFontSize = Math.max(8, Math.min(12, cellWidth * 0.08));

        graphics.on('pointerover', () => {
          graphics.alpha = 0.85;
          text.style.fill = '#000000';
          text.style.fontSize = originalFontSize * 1.1; // 轻微放大字体
        });
        graphics.on('pointerout', () => {
          graphics.alpha = 1;
          text.style.fill = '#333333';
          text.style.fontSize = originalFontSize; // 恢复原始字体大小
        });

        // 添加点击事件（用于调试和用户交互）
        graphics.on('pointerdown', () => {
          console.log(
            `点击了物品: ${item.name} (行:${row}, 列:${column}, 状态:${item.status})`,
          );
        });

        shelfContainer.addChild(graphics);
        shelfContainer.addChild(text);
      });
    };

    // 初始化Pixi应用
    const initPixi = () => {
      if (!container.value) return;

      app = new PIXI.Application({
        width: canvasWidth.value,
        height: canvasHeight.value,
        backgroundColor: 0xf5f5f5,
        backgroundAlpha: 0,
        antialias: true,
        resolution: window.devicePixelRatio || 1,
        autoDensity: true,
        resizeTo: container.value, // 让PIXI自动处理容器尺寸
      });

      container.value.appendChild(app.view as HTMLCanvasElement);

      // 创建货架容器
      shelfContainer = new PIXI.Container();
      shelfContainer.position.set(20, 20); // 添加边距
      app.stage.addChild(shelfContainer);

      renderShelf();
    };

    // 渲染货架
    const renderShelf = () => {
      if (!shelfContainer) return;

      // 清除之前的内容
      shelfContainer.removeChildren();

      // 绘制货架组件
      drawShelfFrame();
      drawGrid();
      drawItems();
    };

    // 监听画布尺寸变化，重新调整PIXI应用大小
    watch(
      [canvasWidth, canvasHeight],
      ([newWidth, newHeight]) => {
        if (app && container.value) {
          // 手动调整画布尺寸
          app.renderer.resize(newWidth, newHeight);
          // 确保画布元素的CSS尺寸也正确
          const canvas = app.view as HTMLCanvasElement;
          canvas.style.width = `${newWidth}px`;
          canvas.style.height = `${newHeight}px`;
        }
      },
      { immediate: false },
    );

    // 监听props变化重新渲染
    watch(
      () => [
        props.width,
        props.height,
        props.depth,
        props.columns,
        props.rows,
        state.items,
        props.borderColor,
      ],
      () => {
        renderShelf();
      },
      { deep: true },
    );

    onMounted(() => {
      initPixi();
    });

    onUnmounted(() => {
      if (app) {
        app.destroy(true);
        app = null;
      }
    });

    return {
      container,
      canvasWidth,
      canvasHeight,
    };
  },
});
</script>

<style scoped lang="scss">
div {
  display: inline-block;
  overflow: visible; // 确保内容不会被裁剪
  min-width: 0; // 防止flex容器限制宽度
  min-height: 0; // 防止flex容器限制高度
}
</style>
