<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="content">
    <div class="chamber" :style="{ ...chamberStyle }">
      <div class="name" v-if="name">{{ name }}</div>
      <div class="conner" :style="connerInfo('lt').style">
        <div v-html="connerInfo('lt').svg"></div>
      </div>
      <div class="conner" :style="connerInfo('rt').style">
        <div v-html="connerInfo('rt').svg"></div>
      </div>
      <div class="conner" :style="connerInfo('lb').style">
        <div v-html="connerInfo('lb').svg"></div>
      </div>
      <div class="conner" :style="connerInfo('rb').style">
        <div v-html="connerInfo('rb').svg"></div>
      </div>
      <div class="wafer" v-if="waferState">
        <wafer
          :size="waferSize"
          :color="waferColor"
          :name="waferName"
          :wafer-shape="waferType"
          :font-size="waferFontSize"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent } from 'vue';
import Wafer from '../Wafer';

export default defineComponent({
  name: 'Chamber',
  components: {
    Wafer,
  },
  props: {
    name: {
      type: String,
      default: '',
    },
    equipmentWidth: {
      type: Number,
      required: true,
    },
    equipmentHeight: {
      type: Number,
      required: true,
    },
    LTConner: {
      type: String,
      default: 'rect',
    },
    LBConner: {
      type: String,
      default: 'rect',
    },
    RTConner: {
      type: String,
      default: 'rect',
    },
    RBConner: {
      type: String,
      default: 'rect',
    },
    waferColor: {
      type: String,
      default: '#000',
    },
    connerColor: {
      type: String,
      default: '#00c1e4',
    },
    backgroundColor: {
      type: String,
      default: '#bcbcbc',
    },
    waferType: {
      type: String,
      default: 'circle',
    },
    waferState: {
      type: Boolean,
      default: false,
    },
    waferName: {
      type: String,
      default: '',
    },
    waferSize: {
      type: Number,
      default: 60,
    },
    waferFontSize: {
      type: Number,
      default: 14,
    },
    isDisabled: {
      type: Boolean,
      default: false,
    },
    link: {
      type: String,
      default: '',
    },
    forSelect: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    type ConnerType = 'lt' | 'rt' | 'lb' | 'rb';

    const borderMap = (gap: number, conner: ConnerType): any => {
      const info = {
        lt: {
          borderTop: `2px solid ${props.connerColor}`,
          borderLeft: `2px solid ${props.connerColor}`,
          top: `${gap}px`,
          left: `${gap}px`,
        },
        rt: {
          borderTop: `2px solid ${props.connerColor}`,
          borderRight: `2px solid ${props.connerColor}`,
          top: `${gap}px`,
          right: `${gap}px`,
        },
        lb: {
          borderBottom: `2px solid ${props.connerColor}`,
          borderLeft: `2px solid ${props.connerColor}`,
          bottom: `${gap}px`,
          left: `${gap}px`,
        },
        rb: {
          borderBottom: `2px solid ${props.connerColor}`,
          borderRight: `2px solid ${props.connerColor}`,
          bottom: `${gap}px`,
          right: `${gap}px`,
        },
      };
      return info[conner];
    };

    const svgInfo = (size: number, conner: ConnerType) => {
      const contentSize = size * 1.25;
      const radius = contentSize - 1;
      const info = {
        lt: {
          size: contentSize,
          d: `M ${radius} 1 A ${radius} ${radius} 1 0 0 1 ${radius}`,
        },
        lb: {
          size: contentSize,
          d: `M 1 0 A ${radius} ${radius} 1 0 0 ${radius} ${radius}`,
        },
        rt: {
          size: contentSize,
          d: `M ${radius} ${radius} A ${radius} ${radius} 1 0 0 0 1`,
        },
        rb: {
          size: contentSize,
          d: `M 1 ${radius} A ${radius} ${radius} 1 0 0 ${radius} 1 `, // M 起始位置  A 半径  || 弧度 顺逆 旋转 圆弧终点坐标
        },
      };
      return info[conner];
    };

    const chamberStyle = computed(() => {
      return {
        'border-radius': `${props.LTConner === 'rect' ? 0 : '20px'} ${
          props.RTConner === 'rect' ? 0 : '20px'
        } ${props.RBConner === 'rect' ? 0 : '20px'} ${
          props.LBConner === 'rect' ? 0 : '20px'
        }`,
        backgroundColor: props.backgroundColor,
      };
    });

    const connerInfo = computed(() => {
      return function (conner: 'lt' | 'rt' | 'lb' | 'rb') {
        const size = Math.min(
          props.equipmentWidth * 0.1,
          props.equipmentHeight * 0.1,
        );
        const position = size / 2; // 6
        const type =
          conner === 'lt'
            ? props.LTConner
            : conner === 'rt'
            ? props.RTConner
            : conner === 'lb'
            ? props.LBConner
            : props.RBConner;

        if (type === 'rect') {
          return {
            style: {
              width: `${size}px`,
              height: `${size}px`,
              ...borderMap(position, conner),
            },
          };
        } else {
          const {
            borderTop,
            borderLeft,
            borderRight,
            borderBottom,
            ...positionInfo
          } = borderMap(position, conner);
          const svgData = svgInfo(size, conner);
          const { size: svgSize, d } = svgData;
          return {
            style: {
              width: `${size + 4}px`,
              height: `${size + 4}px`,
              ...positionInfo,
            },
            svg: `<svg width="${svgSize}" height="${svgSize}" xmlns="http://www.w3.org/2000/svg">
                <path d="${d}" fill="transparent" stroke="${props.connerColor}" stroke-width="2" />
                </svg>`,
          };
        }
      };
    });

    return {
      chamberStyle,
      connerInfo,
    };
  },
});
</script>

<style lang="scss" scoped>
.content {
  width: v-bind('equipmentWidth + "px"');
  height: v-bind('equipmentHeight + "px"');
}

.disabled .name {
  color: #fff;
}

.chamber {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;
  .name {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .conner {
    position: absolute;
    box-sizing: border-box;
  }
  .wafer {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
