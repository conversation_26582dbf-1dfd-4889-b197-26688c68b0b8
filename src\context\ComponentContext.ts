/**
 * 组件上下文
 * 提供通用的组件配置和属性
 */
import { DynamicPartInfo } from '@/types';
import { PropType } from 'vue';

/**
 * 通用组件属性
 * 所有组件都可以继承这些属性
 */
export const CommonComponentProps = {
  /**
   * 部件ID，用于订阅对应的消息
   */
  partId: {
    type: String,
    default: '',
  },

  /**
   * 动态部件信息，用于动态连接策略
   * 每个组件都可以使用此属性而无需单独声明
   */
  dynamicPartInfo: {
    type: Object as PropType<DynamicPartInfo>,
    default: () => ({}),
  },
};

/**
 * 创建组件Props
 * 合并通用Props和组件特定Props
 *
 * @param componentProps 组件特定的Props
 * @returns 合并后的Props
 */
export function createComponentProps<T extends Record<string, any>>(
  componentProps: T,
): T & typeof CommonComponentProps {
  return {
    ...CommonComponentProps,
    ...componentProps,
  };
}
