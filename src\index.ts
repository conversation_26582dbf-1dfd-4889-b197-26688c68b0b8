import type { App } from 'vue';
import * as components from './components';
import './styles/index.scss';

// 导出所有组件
export * from './components';
// 通信适配器接口
export type {
  ICommunicationAdapter,
  ICommunicationAdapterFactory,
} from './communication/adapter/CommunicationAdapter';

// 消息转换器接口
export type {
  IMessageTransformer,
  DefaultMessageTransformer,
} from './communication/adapter/MessageTransformer';

// MQTT 相关类型（从全局类型定义中导出）
export type { MqttInfo, DynamicPartInfo } from './types/index';

// 组件通信管理器
export { ComponentCommunicationManager } from './communication/manager/ComponentCommunicationManager';

export type { ComponentMessageHandler } from './communication/manager/ComponentCommunicationManager';

// 组件通信Hook
export { useComponentCommunication } from './communication/hooks/useComponentCommunication';

// 组件状态管理
export { ComponentStateManager } from './communication/state/ComponentStateManager';
export { useComponentState } from './communication/hooks/useComponentState';

// 组件库对外接口
export { ComponentLibrary } from './communication/ComponentLibrary';

// 导出 Vue 插件
export const install = (app: App) => {
  Object.entries(components).forEach(([name, component]) => {
    app.component(name, component);
  });
};

// 默认导出
export default {
  install,
  ...components, // 将所有组件也包含在默认导出中
};
