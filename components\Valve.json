{"id": 1, "version": "0.0.17", "name": {"zh_CN": "阀门"}, "component": "Valve", "icon": "value", "description": "可配置的阀门组件，支持切换状态和流向控制", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "Valve", "version": "0.0.17", "script": "http://*************:4874/@dcp/component-library@0.0.17/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "group": "DCP", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["color", "rotate", "isDisabled", "pauseFlow"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}}, "schema": {"properties": [{"label": {"zh_CN": "外观配置"}, "description": {"zh_CN": "阀门的外观样式配置"}, "content": [{"property": "color", "label": {"text": {"zh_CN": "阀门颜色"}}, "description": {"zh_CN": "阀门的填充颜色"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#409EFF", "widget": {"component": "ColorConfigurator", "props": {"showAlpha": true}}}, {"property": "rotate", "label": {"text": {"zh_CN": "旋转角度"}}, "description": {"zh_CN": "阀门的旋转角度，影响阀门的方向"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 0, "widget": {"component": "SliderConfigurator", "props": {"min": 0, "max": 360, "step": 90}}}]}, {"label": {"zh_CN": "功能配置"}, "description": {"zh_CN": "阀门的功能性配置"}, "content": [{"property": "isDisabled", "label": {"text": {"zh_CN": "禁用状态"}}, "description": {"zh_CN": "阀门是否处于禁用状态"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator"}}, {"property": "pauseFlow", "label": {"text": {"zh_CN": "暂停流动"}}, "description": {"zh_CN": "是否暂停流体流动"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator"}}]}], "events": {}}, "snippets": [{"name": {"zh_CN": "阀门"}, "icon": "valve", "screenshot": "", "snippetName": "Valve", "schema": {"props": {"color": "#409EFF", "rotate": 0, "isDisabled": false, "pauseFlow": false}}}], "category": "DCP"}