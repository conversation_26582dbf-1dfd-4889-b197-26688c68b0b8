<template>
  <div class="datetime-container">
    <span class="datetime-display">
      {{ formattedDateTime }}
    </span>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, onUnmounted } from 'vue';

export default defineComponent({
  name: 'DateTime',
  props: {
    // 指定的时间值，如果不传则显示当前时间
    value: {
      type: [String, Date, Number],
      default: null,
    },
    // 时间格式
    format: {
      type: String,
      default: 'yyyy-MM-dd HH:mm:ss',
    },
    // 是否实时更新（仅在未传入value时生效）
    realTime: {
      type: Boolean,
      default: true,
    },
    // 更新间隔（毫秒）
    interval: {
      type: Number,
      default: 1000,
    },
  },
  setup(props) {
    const currentTime = ref(new Date());
    let timer: NodeJS.Timeout | null = null;

    // 判断是否为实时模式
    const isRealTime = computed(() => {
      return !props.value && props.realTime;
    });

    // 获取要显示的时间
    const displayTime = computed(() => {
      if (props.value) {
        if (typeof props.value === 'string') {
          return new Date(props.value);
        } else if (typeof props.value === 'number') {
          return new Date(props.value);
        } else {
          return props.value;
        }
      }
      return currentTime.value;
    });

    // 格式化时间
    const formatDateTime = (date: Date, format: string): string => {
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const hours = date.getHours();
      const minutes = date.getMinutes();
      const seconds = date.getSeconds();
      const milliseconds = date.getMilliseconds();

      const formatMap: Record<string, string> = {
        yyyy: year.toString(),
        yy: year.toString().slice(-2),
        MM: month.toString().padStart(2, '0'),
        M: month.toString(),
        dd: day.toString().padStart(2, '0'),
        d: day.toString(),
        HH: hours.toString().padStart(2, '0'),
        H: hours.toString(),
        mm: minutes.toString().padStart(2, '0'),
        m: minutes.toString(),
        ss: seconds.toString().padStart(2, '0'),
        s: seconds.toString(),
        SSS: milliseconds.toString().padStart(3, '0'),
      };

      let result = format;
      Object.keys(formatMap).forEach((key) => {
        result = result.replace(new RegExp(key, 'g'), formatMap[key]);
      });

      return result;
    };

    // 格式化后的时间字符串
    const formattedDateTime = computed(() => {
      return formatDateTime(displayTime.value, props.format);
    });

    // 启动定时器
    const startTimer = () => {
      if (isRealTime.value && props.interval > 0) {
        timer = setInterval(() => {
          currentTime.value = new Date();
        }, props.interval);
      }
    };

    // 停止定时器
    const stopTimer = () => {
      if (timer) {
        clearInterval(timer);
        timer = null;
      }
    };

    onMounted(() => {
      startTimer();
    });

    onUnmounted(() => {
      stopTimer();
    });

    return {
      formattedDateTime,
    };
  },
});
</script>

<style lang="scss" scoped>
.datetime-container {
  display: inline-block;
}
</style>
