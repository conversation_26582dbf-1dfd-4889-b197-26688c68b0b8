{"id": 1, "version": "0.0.17", "name": {"zh_CN": "状态"}, "component": "DcpStatus", "icon": "status", "description": "状态指示器组件", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "DcpStatus", "version": "0.0.17", "script": "http://*************:4874/@dcp/component-library@0.0.17/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "group": "DCP组件库", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["status"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent", "createBlock"], "disable": []}, "invalidity": [""], "clickCapture": true, "framework": "<PERSON><PERSON>"}, "schema": {"properties": [{"name": "0", "label": {"zh_CN": "基础属性"}, "content": [{"property": "status", "label": {"text": {"zh_CN": "状态"}}, "description": {"zh_CN": "状态类型"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "online", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "在线", "value": "online"}, {"label": "离线", "value": "offline"}]}}}], "description": {"zh_CN": ""}}], "events": {}, "slots": {}}, "snippets": [{"name": {"zh_CN": "状态"}, "icon": "status", "screenshot": "", "snippetName": "DcpStatus", "schema": {"props": {"type": "online"}}}], "category": "DCP"}