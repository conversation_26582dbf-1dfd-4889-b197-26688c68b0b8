{"component": "<PERSON><PERSON>", "name": {"zh_CN": "货架"}, "icon": "<PERSON><PERSON>", "group": "DCP", "category": "DCP", "description": "3D等轴测视角的货架组件，支持多行多列布局和物品状态展示", "tags": "", "keywords": "", "doc_url": "", "devMode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "<PERSON><PERSON>", "version": "0.0.49", "script": "http://*************:4874/@dcp/component-library@0.0.49/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "isNullNode": false, "isLayout": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "rootSelector": "", "shortcuts": {"properties": ["columns", "rows", "items"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}, "clickCapture": false, "framework": "<PERSON><PERSON>"}, "schema": {"properties": [{"name": "0", "label": {"zh_CN": "基础属性"}, "description": {"zh_CN": "货架的结构和内容配置"}, "content": [{"property": "columns", "label": {"text": {"zh_CN": "列数"}}, "description": "货架的列数", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 3, "widget": {"component": "NumberConfigurator", "props": {"min": 1, "max": 8, "step": 1}}}, {"property": "rows", "label": {"text": {"zh_CN": "行数"}}, "description": "货架的行数", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 2, "widget": {"component": "NumberConfigurator", "props": {"min": 1, "max": 6, "step": 1}}}, {"property": "items", "label": {"text": {"zh_CN": "物品列表"}}, "description": "货架中的物品数组，每个物品包含：name(名称), state(状态)", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "array", "defaultValue": [], "widget": {"component": "CodeConfigurator", "props": {"language": "json", "height": 150}}}, {"property": "statusToColor", "label": {"text": {"zh_CN": "状态到颜色映射"}}, "description": {}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "object", "defaultValue": {"Idled": "#909399", "Processing": "#409EFF", "Completed": "#67C23A", "Aborted": "#E6A23C", "Failed": "#DC3545", "Returning": "#9370DB", "Returned": "#B0C4DE", "CompleteReturning": "#48D1CC"}, "widget": {"component": "CodeConfigurator", "props": {"language": "json", "height": 150}}}]}, {"name": "1", "label": {"zh_CN": "样式属性"}, "description": {"zh_CN": "货架的外观和尺寸配置"}, "content": [{"property": "width", "label": {"text": {"zh_CN": "宽度"}}, "description": "货架宽度", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 200, "widget": {"component": "NumberConfigurator", "props": {"min": 100, "max": 500, "step": 10}}}, {"property": "height", "label": {"text": {"zh_CN": "高度"}}, "description": "货架高度", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 150, "widget": {"component": "NumberConfigurator", "props": {"min": 100, "max": 800, "step": 10}}}, {"property": "depth", "label": {"text": {"zh_CN": "深度"}}, "description": "货架深度", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 100, "widget": {"component": "NumberConfigurator", "props": {"min": 50, "max": 200, "step": 10}}}, {"property": "itemStartPosition", "label": {"text": {"zh_CN": "物品起始位置"}}, "description": "物品排列的起始位置", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "top-left", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "左上", "value": "top-left"}, {"label": "右上", "value": "top-right"}, {"label": "左下", "value": "bottom-left"}, {"label": "右下", "value": "bottom-right"}]}}}, {"property": "borderColor", "label": {"text": {"zh_CN": "边框颜色"}}, "description": "边框颜色", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#2C3E50", "widget": {"component": "ColorConfigurator", "props": {}}}, {"property": "shelfColor", "label": {"text": {"zh_CN": "货架板颜色"}}, "description": "货架板颜色", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#F0F0F0", "widget": {"component": "ColorConfigurator", "props": {}}}, {"property": "showBackPanel", "label": {"text": {"zh_CN": "显示背板"}}, "description": "是否显示背板", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": true, "widget": {"component": "SwitchConfigurator", "props": {}}}]}, {"name": "2", "label": {"zh_CN": "高级属性"}, "description": {"zh_CN": "调试和开发配置"}, "content": [{"property": "showDebugInfo", "label": {"text": {"zh_CN": "显示调试信息"}}, "description": "显示调试信息", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator", "props": {}}}]}], "events": {}, "slots": {}}, "snippets": [{"name": {"zh_CN": "货架"}, "icon": "<PERSON><PERSON>", "snippetName": "<PERSON><PERSON>", "schema": {"props": {"width": 200, "height": 150, "depth": 100, "borderColor": "#2C3E50", "shelfColor": "#DDEEFF", "columns": 3, "rows": 2, "items": [], "showBackPanel": true, "showDebugInfo": false, "statusToColor": {"Idled": "#909399", "Processing": "#409EFF", "Completed": "#67C23A", "Aborted": "#E6A23C", "Failed": "#DC3545", "Returning": "#9370DB", "Returned": "#B0C4DE", "CompleteReturning": "#48D1CC"}}}}]}