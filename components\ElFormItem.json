{"id": 1, "version": "2.4.2", "name": {"zh_CN": "表单子项"}, "component": "ElFormItem", "icon": "formItem", "description": "表单包含 输入框, 单选框, 下拉选择, 多选框 等用户输入的组件。 使用表单，您可以收集、验证和提交数据。", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "element-plus", "exportName": "ElFormItem"}, "group": "表单组件", "category": "element-plus", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": true, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["inline", "label-width"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent", "createBlock"], "disable": []}, "invalidity": [""], "clickCapture": true, "framework": "<PERSON><PERSON>"}, "schema": {"properties": [{"name": "0", "label": {"zh_CN": "基础属性"}, "content": [{"property": "prop", "label": {"text": {"zh_CN": "键名"}}, "description": {"zh_CN": "model 的键名。 它可以是一个属性的值(如 a.b.0 或 [a', 'b', '0'])。 在定义了 validate、resetFields 的方法时，该属性是必填的"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "label", "label": {"text": {"zh_CN": "标签文本"}}, "description": {"zh_CN": "标签文本"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "label-width", "label": {"text": {"zh_CN": "标签宽度"}}, "description": {"zh_CN": "标签宽度，例如 '50px'。 可以使用 auto"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "required", "label": {"text": {"zh_CN": "必填项"}}, "description": {"zh_CN": "是否为必填项，如不设置，则会根据校验规则确认"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "rules", "label": {"text": {"zh_CN": "验证规则"}}, "description": {"zh_CN": "表单验证规则, 更多内容可以参考async-validator"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "top", "widget": {"component": "CodeConfigurator", "props": {"language": "json"}}}, {"property": "error", "label": {"text": {"zh_CN": "错误信息"}}, "description": {"zh_CN": "表单域验证错误时的提示信息。设置该值会导致表单验证状态变为 error，并显示该错误信息"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "show-message", "label": {"text": {"zh_CN": "显示错误信息"}}, "description": {"zh_CN": "是否显示校验错误信息"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "inline-message", "label": {"text": {"zh_CN": "行内显示错误信息"}}, "description": {"zh_CN": "是否在行内显示校验信息"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "size", "label": {"text": {"zh_CN": "尺寸"}}, "description": {"zh_CN": "用于控制该表单内组件的尺寸"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "default", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "large", "value": "large"}, {"label": "default", "value": "default"}, {"label": "small", "value": "small"}]}}}, {"property": "for", "label": {"text": {"zh_CN": "for"}}, "description": {"zh_CN": "和原生标签相同能力"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "validate-status", "label": {"text": {"zh_CN": "校验状态"}}, "description": {"zh_CN": "formItem 校验的状态"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "error", "value": "error"}, {"label": "validating", "value": "validating"}, {"label": "success", "value": "success"}]}}}], "description": {"zh_CN": ""}}], "events": {}, "slots": {"label": {"label": {"zh_CN": "label"}, "description": {"zh_CN": "标签位置显示的内容"}}, "error": {"label": {"zh_CN": "error"}, "description": {"zh_CN": "验证错误信息的显示内容"}}}}}