/**
 * MQTT适配器示例实现
 * 注意：这只是一个示例，实际实现应该由宿主应用提供
 */
import { ICommunicationAdapter } from './CommunicationAdapter';

/**
 * MQTT适配器示例
 * 这个类展示了如何实现通信适配器接口
 * 实际使用时，宿主应用应该提供自己的实现
 */
export class MqttAdapterExample implements ICommunicationAdapter {
  private client: any; // 实际使用时应该是具体的MQTT客户端类型
  private subscriptions: Map<string, Array<(message: any, topic: string) => void>> = new Map();
  private connected: boolean = false;

  constructor(client: any) {
    this.client = client;
    this.setupMessageHandler();
    
    // 监听连接状态
    this.client.on('connect', () => {
      this.connected = true;
    });
    
    this.client.on('disconnect', () => {
      this.connected = false;
    });
  }

  /**
   * 设置消息处理器
   */
  private setupMessageHandler() {
    this.client.on('message', (topic: string, message: Buffer) => {
      const callbacks = this.subscriptions.get(topic);
      if (callbacks) {
        try {
          // 尝试解析JSON
          const messageStr = message.toString();
          const messageObj = JSON.parse(messageStr);
          callbacks.forEach(callback => callback(messageObj, topic));
        } catch (e) {
          // 如果解析失败，传递原始字符串
          const messageStr = message.toString();
          callbacks.forEach(callback => callback(messageStr, topic));
        }
      }
    });
  }

  /**
   * 订阅MQTT主题
   * @param topic 主题
   * @param callback 回调函数
   * @returns 取消订阅的函数
   */
  subscribe(topic: string, callback: (message: any, topic: string) => void): () => void {
    if (!this.subscriptions.has(topic)) {
      this.subscriptions.set(topic, []);
      // 在MQTT服务器上订阅
      this.client.subscribe(topic);
    }
    
    const callbacks = this.subscriptions.get(topic)!;
    callbacks.push(callback);

    // 返回取消订阅的函数
    return () => this.unsubscribe(topic, callback);
  }

  /**
   * 取消订阅
   * @param topic 主题
   * @param callback 回调函数
   */
  private unsubscribe(topic: string, callback: (message: any, topic: string) => void) {
    const callbacks = this.subscriptions.get(topic);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index !== -1) {
        callbacks.splice(index, 1);
      }

      // 如果没有回调函数了，取消对这个主题的订阅
      if (callbacks.length === 0) {
        this.subscriptions.delete(topic);
        this.client.unsubscribe(topic);
      }
    }
  }

  /**
   * 发布消息到主题
   * @param topic 主题
   * @param message 消息
   */
  publish(topic: string, message: any): void {
    const messageStr = typeof message === 'string' 
      ? message 
      : JSON.stringify(message);
    
    this.client.publish(topic, messageStr);
  }

  /**
   * 检查连接状态
   * @returns 是否已连接
   */
  isConnected(): boolean {
    return this.connected;
  }
}
