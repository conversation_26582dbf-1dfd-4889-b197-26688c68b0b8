{"id": 1, "version": "2.4.2", "name": {"zh_CN": "表单"}, "component": "ElForm", "icon": "form", "description": "表单包含 输入框, 单选框, 下拉选择, 多选框 等用户输入的组件。 使用表单，您可以收集、验证和提交数据。", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "element-plus", "exportName": "ElForm"}, "group": "表单组件", "category": "element-plus", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": true, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": ["ElFormItem"], "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["inline", "label-width"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent", "createBlock"], "disable": []}, "invalidity": [""], "clickCapture": true, "framework": "<PERSON><PERSON>"}, "schema": {"properties": [{"name": "0", "label": {"zh_CN": "基础属性"}, "content": [{"property": "model", "label": {"text": {"zh_CN": "数据对象"}}, "description": {"zh_CN": "表单数据对象"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "top", "widget": {"component": "CodeConfigurator", "props": {"language": "json"}}}, {"property": "rules", "label": {"text": {"zh_CN": "验证规则"}}, "description": {"zh_CN": "表单验证规则"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "top", "widget": {"component": "CodeConfigurator", "props": {"language": "json"}}}, {"property": "inline", "label": {"text": {"zh_CN": "行内模式"}}, "description": {"zh_CN": "行内表单模式"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "label-position", "label": {"text": {"zh_CN": "标签位置"}}, "description": {"zh_CN": "表单域标签的位置， 当设置为 left 或 right 时，则也需要设置标签宽度属性"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "right", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "left", "value": "left"}, {"label": "right", "value": "right"}, {"label": "top", "value": "top"}]}}}, {"property": "label-width", "label": {"text": {"zh_CN": "标签宽度"}}, "description": {"zh_CN": "标签的长度，例如 '50px'。 作为 Form 直接子元素的 form-item 会继承该值。 可以使用 auto。"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "widget": {"component": "InputConfigurator", "props": {}}, "device": []}, {"property": "label-suffix", "label": {"text": {"zh_CN": "标签后缀"}}, "description": {"zh_CN": "表单域标签的后缀"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "widget": {"component": "InputConfigurator", "props": {}}, "device": []}, {"property": "hide-required-asterisk", "label": {"text": {"zh_CN": "隐藏必填星号"}}, "description": {"zh_CN": "是否隐藏必填字段标签旁边的红色星号"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "require-asterisk-position", "label": {"text": {"zh_CN": "星号位置"}}, "description": {"zh_CN": "星号的位置"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "left", "widget": {"component": "ButtonGroupConfigurator", "props": {"options": [{"label": "left", "value": "left"}, {"label": "right", "value": "right"}]}}}, {"property": "show-message", "label": {"text": {"zh_CN": "显示校验信息"}}, "description": {"zh_CN": "是否显示校验错误信息"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "defaultValue": true, "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "inline-message", "label": {"text": {"zh_CN": "行内显示校验信息"}}, "description": {"zh_CN": "是否以行内形式展示校验信息"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "defaultValue": false, "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "status-icon", "label": {"text": {"zh_CN": "显示校验结果图标"}}, "description": {"zh_CN": "是否在输入框中显示校验结果反馈图标"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "defaultValue": false, "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "validate-on-rule-change", "label": {"text": {"zh_CN": "触发验证"}}, "description": {"zh_CN": "是否在 rules 属性改变后立即触发一次验证"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "defaultValue": true, "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "size", "label": {"text": {"zh_CN": "尺寸"}}, "description": {"zh_CN": "用于控制该表单内组件的尺寸"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "default", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "large", "value": "large"}, {"label": "default", "value": "default"}, {"label": "small", "value": "small"}]}}}, {"property": "disabled", "label": {"text": {"zh_CN": "禁用"}}, "description": {"zh_CN": "是否禁用该表单内的所有组件。 如果设置为 true, 它将覆盖内部组件的 disabled 属性"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "defaultValue": false, "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}, "device": []}, {"property": "scroll-to-error", "label": {"text": {"zh_CN": "滚动到错误项"}}, "description": {"zh_CN": "当校验失败时，滚动到第一个错误表单项"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "defaultValue": false, "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}, "device": []}], "description": {"zh_CN": ""}}], "events": {"onValidate": {"label": {"zh_CN": "任一表单项被校验后触发"}, "description": {"zh_CN": "任一表单项被校验后触发"}, "type": "event", "functionInfo": {"params": [], "returns": {}}, "defaultValue": ""}}, "slots": {}}, "snippets": [{"name": {"zh_CN": "表单"}, "icon": "form", "screenshot": "", "snippetName": "ElForm", "schema": {"children": [{"componentName": "ElFormItem", "props": {"label": "账号", "prop": "account"}, "children": [{"componentName": "ElInput", "props": {"modelValue": "", "placeholder": "请输入账号"}}]}, {"componentName": "ElFormItem", "props": {"label": "密码", "prop": "password"}, "children": [{"componentName": "ElInput", "props": {"modelValue": "", "placeholder": "请输入密码", "type": "password"}}]}, {"componentName": "ElFormItem", "props": {}, "children": [{"componentName": "ElButton", "props": {"type": "primary", "style": "margin-right: 10px"}, "children": [{"componentName": "Text", "props": {"text": "提交"}}]}, {"componentName": "ElButton", "props": {"type": "primary"}, "children": [{"componentName": "Text", "props": {"text": "重置"}}]}]}]}, "category": "element-plus"}]}