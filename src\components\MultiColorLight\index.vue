<template>
  <div class="go-text-box">
    <div class="content">
      <div
        class="light"
        v-for="(color, index) in lightList"
        :key="index"
        :style="{ backgroundColor: color }"
      ></div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, toRefs } from 'vue';
import { useComponentState } from '@/communication/hooks/useComponentState';
import { createComponentProps } from '@/context/ComponentContext';

export default defineComponent({
  name: 'MultiColorLight',
  props: createComponentProps({
    width: {
      type: Number,
      default: 60,
    },
    height: {
      type: Number,
      default: 200,
    },
    lightList: {
      type: Array as PropType<string[]>,
      default: () => ['#FF0000', '#FFFF00', '#00FF00'],
    },
    lightBorderStyle: {
      type: [Boolean, String],
      default: false,
    },
  }),
  setup(props) {
    const { lightList, lightBorderStyle } = toRefs(props);

    // 使用组件状态Hook（响应式工厂函数版本）
    const { state } = useComponentState(
      props.partId,
      () => ({
        lightList: lightList.value,
        lightBorderStyle: lightBorderStyle.value,
      }),
      props.dynamicPartInfo,
    );

    return {
      lightList: state.lightList,
      lightBorderStyle: state.lightBorderStyle,
    };
  },
});
</script>

<style lang="scss" scoped>
.go-text-box {
  display: flex;
  align-items: center;
  justify-content: center;

  .content {
    width: v-bind('width + "px"');
    height: v-bind('height + "px"');
    display: flex;
    flex-direction: column;

    .light {
      flex: 1;
      width: 100%;
      border: v-bind(
        'typeof lightBorderStyle === "string" ? lightBorderStyle : lightBorderStyle ? "2px solid #FFF" : "none"'
      );
      box-sizing: border-box;
    }
  }
}
</style>
