{"name": {"zh_CN": "标题"}, "component": ["h1", "h2", "h3", "h4", "h5", "h6"], "icon": "h16", "description": "标题", "docUrl": "", "screenshot": "", "tags": "", "keywords": "", "devMode": "proCode", "npm": {}, "group": "component", "category": "html", "priority": 20, "schema": {"properties": [{"label": {"zh_CN": "基础信息"}, "description": {"zh_CN": "基础信息"}, "content": [{"property": "children", "label": {"text": {"zh_CN": "类型"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "HtmlTextConfigurator", "props": {"showRadioButton": true}}, "description": {"zh_CN": ""}, "labelPosition": "none"}, {"property": "attributes3", "label": {"text": {"zh_CN": "原生属性"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "HtmlAttributesConfigurator", "props": {}}, "description": {"zh_CN": ""}, "labelPosition": "none"}]}], "events": {}}, "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "nestingRule": {"childWhitelist": [], "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["disabled", "size"]}, "contextMenu": {"actions": ["create symbol"], "disable": ["copy", "remove"]}}, "snippets": [{"name": {"zh_CN": "标题"}, "icon": "h16", "screenshot": "", "snippetName": "h1", "schema": {"componentName": "h1", "props": {}, "children": "Heading"}, "category": "basic"}]}