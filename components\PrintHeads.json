{"id": 1, "version": "0.0.17", "name": {"zh_CN": "喷头"}, "component": "PrintHeads", "icon": "print-header", "description": "可配置的喷头组件，支持液体和气体两种类型", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "PrintHeads", "version": "0.0.17", "script": "http://*************:4874/@dcp/component-library@0.0.17/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "group": "DCP", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["type", "color", "stateColor"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}}, "schema": {"properties": [{"label": {"zh_CN": "基础属性"}, "description": {"zh_CN": "喷头的基础配置属性"}, "content": [{"property": "type", "label": {"text": {"zh_CN": "喷头类型"}}, "description": {"zh_CN": "喷头的类型"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "liquid", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "液体", "value": "liquid"}, {"label": "气体", "value": "gas"}]}}}, {"property": "rotate", "label": {"text": {"zh_CN": "旋转角度"}}, "description": {"zh_CN": "打印头的旋转角度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 0, "widget": {"component": "NumberConfigurator", "props": {"min": 0, "max": 360, "step": 15}}}]}, {"label": {"zh_CN": "颜色配置"}, "description": {"zh_CN": "打印头的颜色配置属性"}, "content": [{"property": "color", "label": {"text": {"zh_CN": "主体颜色"}}, "description": {"zh_CN": "打印头主体的颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#4d4d4d", "widget": {"component": "ColorConfigurator", "props": {}}}, {"property": "stateColor", "label": {"text": {"zh_CN": "状态颜色"}}, "description": {"zh_CN": "打印头状态指示灯的颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#4d4d4d", "widget": {"component": "ColorConfigurator", "props": {}}}]}], "events": {}}, "snippets": [{"name": {"zh_CN": "喷头"}, "icon": "print-header", "screenshot": "", "snippetName": "PrintHeads", "schema": {"props": {"type": "liquid", "color": "#4d4d4d", "stateColor": "#4d4d4d", "rotate": 0}}}], "category": "DCP"}