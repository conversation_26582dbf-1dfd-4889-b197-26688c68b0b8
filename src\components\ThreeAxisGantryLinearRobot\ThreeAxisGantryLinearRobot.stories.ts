import type { Meta, StoryObj } from '@storybook/vue3';
import { ref, reactive } from 'vue';
import ThreeAxisGantryLinearRobot from './index.vue';

const meta: Meta<typeof ThreeAxisGantryLinearRobot> = {
  title: 'Equipment/ThreeAxisGantryLinearRobot',
  component: ThreeAxisGantryLinearRobot,
  tags: ['autodocs'],
  argTypes: {
    width: {
      control: { type: 'range', min: 200, max: 600, step: 10 },
      description: '组件画布宽度（像素）',
    },
    height: {
      control: { type: 'range', min: 200, max: 600, step: 10 },
      description: '组件画布高度（像素）',
    },
    frameWidth: {
      control: { type: 'range', min: 80, max: 300, step: 10 },
      description: '龙门框架宽度（X方向）',
    },
    frameHeight: {
      control: { type: 'range', min: 60, max: 200, step: 10 },
      description: '龙门框架高度（Z方向）',
    },
    frameDepth: {
      control: { type: 'range', min: 60, max: 200, step: 10 },
      description: '龙门框架深度（Y方向）',
    },
    xMaxStroke: {
      control: { type: 'range', min: 100, max: 1000, step: 50 },
      description: 'X轴最大行程（毫米）',
    },
    yMaxStroke: {
      control: { type: 'range', min: 100, max: 1000, step: 50 },
      description: 'Y轴最大行程（毫米）',
    },
    zMaxStroke: {
      control: { type: 'range', min: 100, max: 1000, step: 50 },
      description: 'Z轴最大行程（毫米）',
    },
    xPosition: {
      control: { type: 'range', min: 0, max: 500, step: 10 },
      description: 'X轴位置（实际行程值，毫米）',
    },
    yPosition: {
      control: { type: 'range', min: 0, max: 500, step: 10 },
      description: 'Y轴位置（实际行程值，毫米）',
    },
    zPosition: {
      control: { type: 'range', min: 0, max: 500, step: 10 },
      description: 'Z轴位置（实际行程值，毫米）',
    },
    suckerActive: {
      control: { type: 'boolean' },
      description: '吸盘状态（true为吸附状态，false为释放状态）',
    },
    animationDuration: {
      control: { type: 'range', min: 100, max: 2000, step: 100 },
      description: '动画持续时间（毫秒）',
    },
    enableAnimation: {
      control: { type: 'boolean' },
      description: '是否启用平滑动画过渡',
    },
    material: {
      control: 'object',
      description: '抓手上的物料配置，可以设置物料类型、颜色和尺寸',
    },
    xReversed: {
      control: { type: 'boolean' },
      description: 'X轴位置反转（true时0位置变为最大值位置）',
    },
    yReversed: {
      control: { type: 'boolean' },
      description: 'Y轴位置反转（true时0位置变为最大值位置）',
    },
    zReversed: {
      control: { type: 'boolean' },
      description: 'Z轴位置反转（true时0位置变为最大值位置）',
    },
  },
  parameters: {
    layout: 'centered',
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 默认状态 - 所有轴都在中心位置
export const Default: Story = {
  name: '基础示例',
  args: {
    width: 400,
    height: 350,
    frameWidth: 150,
    frameHeight: 100,
    frameDepth: 100,
    xMaxStroke: 500,
    yMaxStroke: 500,
    zMaxStroke: 500,
    xPosition: 250,
    yPosition: 250,
    zPosition: 250,
    suckerActive: false,
    animationDuration: 800,
    enableAnimation: true,
    material: null,
  },
  parameters: {
    docs: {
      description: {
        story: '展示三轴龙门线性机器人的默认状态，所有轴都位于中心位置。',
      },
    },
  },
};

// 动画演示
export const AnimationDemo: Story = {
  name: '动画效果演示',
  render: () => ({
    components: { ThreeAxisGantryLinearRobot },
    setup() {
      const currentPosition = ref({
        x: 250,
        y: 250,
        z: 250,
        sucker: false,
      });

      const animationSpeed = ref(800);
      const enableAnimation = ref(true);

      const predefinedPositions = [
        { x: 250, y: 250, z: 250, sucker: false, name: '中心位置' },
        { x: 50, y: 100, z: 375, sucker: false, name: '左前上' },
        { x: 450, y: 400, z: 50, sucker: false, name: '右后下' },
        { x: 125, y: 425, z: 175, sucker: true, name: '左后中（吸盘激活）' },
        { x: 425, y: 150, z: 450, sucker: true, name: '右前上（吸盘激活）' },
        { x: 250, y: 250, z: 25, sucker: false, name: '中心最低位置' },
      ];

      const moveToPosition = (pos: any) => {
        currentPosition.value = { ...pos };
      };

      return {
        currentPosition,
        animationSpeed,
        enableAnimation,
        predefinedPositions,
        moveToPosition,
      };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="padding: 16px; border: 1px solid #eee; border-radius: 8px;">
          <h3 style="margin: 0 0 16px 0;">动画控制面板</h3>
          
          <div style="display: flex; flex-wrap: wrap; gap: 12px; margin-bottom: 16px;">
            <label style="display: flex; align-items: center; gap: 8px;">
              <input type="checkbox" v-model="enableAnimation" />
              启用动画
            </label>
            
            <label style="display: flex; align-items: center; gap: 8px;">
              动画速度: {{ animationSpeed }}ms
              <input 
                type="range" 
                v-model="animationSpeed" 
                min="200" 
                max="2000" 
                step="100"
                style="width: 120px;"
              />
            </label>
          </div>
          
          <div style="margin-bottom: 16px;">
            <div style="margin-bottom: 8px; font-weight: bold;">预设位置快速跳转：</div>
            <div style="display: flex; flex-wrap: wrap; gap: 8px;">
              <button 
                v-for="(pos, index) in predefinedPositions" 
                :key="index"
                @click="moveToPosition(pos)"
                style="padding: 6px 12px; background: #1677ff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;"
              >
                {{ pos.name }}
              </button>
            </div>
          </div>
          
          <div style="font-size: 14px; color: #666;">
            <div>当前位置: X={{ currentPosition.x.toFixed(1) }}, Y={{ currentPosition.y.toFixed(1) }}, Z={{ currentPosition.z.toFixed(1) }}</div>
            <div>吸盘状态: {{ currentPosition.sucker ? '激活' : '释放' }}</div>
            <div>动画: {{ enableAnimation ? '开启' : '关闭' }} ({{ animationSpeed }}ms)</div>
          </div>
        </div>
        
        <div style="border: 1px solid #eee; border-radius: 8px; padding: 16px; display: flex; justify-content: center; background: #fafafa;">
          <ThreeAxisGantryLinearRobot
            :width="450"
            :height="380"
            :frame-width="150"
            :frame-height="100"
            :frame-depth="100"
            :x-position="currentPosition.x"
            :y-position="currentPosition.y"
            :z-position="currentPosition.z"
            :sucker-active="currentPosition.sucker"
            :animation-duration="animationSpeed"
            :enable-animation="enableAnimation"
          />
        </div>
        
        
      </div>
    `,
  }),
  parameters: {
    docs: {},
  },
};

// 自动化工作循环演示
export const AutomatedWorkCycle: Story = {
  name: '自动化工作循环演示',
  render: () => ({
    components: { ThreeAxisGantryLinearRobot },
    setup() {
      const isRunning = ref(false);
      const currentStep = ref(0);

      const workSteps = [
        {
          x: 250,
          y: 250,
          z: 450,
          sucker: false,
          name: '初始位置',
          description: '机器人回到安全的初始位置，所有轴居中，吸盘释放',
        },
        {
          x: 50,
          y: 100,
          z: 450,
          sucker: false,
          name: '移动到工件位置',
          description: 'X轴向左移动，Y轴向前移动，定位到第一个工件上方',
        },
        {
          x: 50,
          y: 100,
          z: 75,
          sucker: false,
          description: 'Z轴下降，吸盘接近工件表面',
        },
        {
          x: 50,
          y: 100,
          z: 75,
          sucker: true,
          name: '抓取工件',
          description: '激活吸盘，抓取工件（注意吸盘收缩变形）',
        },
        {
          x: 50,
          y: 100,
          z: 325,
          sucker: true,
          name: '提升工件',
          description: 'Z轴上升，提升工件到安全高度',
        },
        {
          x: 450,
          y: 400,
          z: 325,
          sucker: true,
          name: '移动到目标位置',
          description: 'X轴向右，Y轴向后，移动到目标放置位置',
        },
        {
          x: 450,
          y: 400,
          z: 125,
          sucker: true,
          name: '下降到放置位置',
          description: 'Z轴下降，将工件移动到放置位置',
        },
        {
          x: 450,
          y: 400,
          z: 125,
          sucker: false,
          name: '释放工件',
          description: '关闭吸盘，释放工件（注意吸盘恢复原状）',
        },
        {
          x: 450,
          y: 400,
          z: 375,
          sucker: false,
          name: '提升脱离',
          description: 'Z轴上升，脱离工件',
        },
        {
          x: 250,
          y: 250,
          z: 450,
          sucker: false,
          name: '返回初始位置',
          description: '所有轴回到初始安全位置，准备下一个循环',
        },
      ];

      const currentState = reactive({
        x: 250,
        y: 250,
        z: 450,
        sucker: false,
      });

      const startDemo = async () => {
        isRunning.value = true;
        currentStep.value = 0;

        for (let i = 0; i < workSteps.length; i++) {
          if (!isRunning.value) break;

          currentStep.value = i;
          const step = workSteps[i];

          // 更新机器人状态
          Object.assign(currentState, {
            x: step.x,
            y: step.y,
            z: step.z,
            sucker: step.sucker,
          });

          console.log(`步骤 ${i + 1}: ${step.name}`, step);

          // 等待2秒再执行下一步
          await new Promise((resolve) => setTimeout(resolve, 2000));
        }

        // 演示完成后循环
        if (isRunning.value) {
          setTimeout(() => {
            if (isRunning.value) {
              startDemo(); // 自动重新开始
            }
          }, 1000);
        }
      };

      const stopDemo = () => {
        isRunning.value = false;
        currentStep.value = 0;
        Object.assign(currentState, workSteps[0]);
      };

      return {
        isRunning,
        currentStep,
        workSteps,
        currentState,
        startDemo,
        stopDemo,
      };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="padding: 16px; border: 1px solid #eee; border-radius: 8px;">
          <h3 style="margin: 0 0 16px 0;">三轴龙门机器人自动化工作循环</h3>
          
          <div style="display: flex; gap: 10px; margin-bottom: 16px;">
            <button 
              @click="startDemo" 
              :disabled="isRunning"
              style="padding: 8px 16px; background: #1677ff; color: white; border: none; border-radius: 4px; cursor: pointer;"
            >
              {{ isRunning ? '运行中...' : '开始演示' }}
            </button>
            
            <button 
              @click="stopDemo" 
              :disabled="!isRunning"
              style="padding: 8px 16px; background: #f5222d; color: white; border: none; border-radius: 4px; cursor: pointer;"
            >
              停止演示
            </button>
          </div>
          
          <div style="margin-bottom: 16px;">
            <div style="margin-bottom: 8px;">
              <strong>步骤 {{ currentStep + 1 }}/{{ workSteps.length }}:</strong> 
              {{ workSteps[currentStep]?.name || '待开始' }}
            </div>
            
            <div style="margin-bottom: 8px; font-size: 14px; color: #666; line-height: 1.4;">
              {{ workSteps[currentStep]?.description || '点击开始演示按钮观看自动化工作循环' }}
            </div>
            
            <div style="margin-bottom: 10px; font-size: 12px; color: #999;">
              位置状态: X={{ currentState.x.toFixed(1) }}, Y={{ currentState.y.toFixed(1) }}, Z={{ currentState.z.toFixed(1) }}, 
              吸盘={{ currentState.sucker ? '激活' : '释放' }}
            </div>
          </div>
          
          <!-- 进度条 -->
          <div style="display: flex; gap: 2px; margin-bottom: 16px;">
            <div 
              v-for="(step, index) in workSteps" 
              :key="index"
              :style="{
                flex: 1,
                height: '6px',
                backgroundColor: index <= currentStep && isRunning ? '#1677ff' : '#f0f0f0',
                borderRadius: '3px',
                transition: 'background-color 0.3s'
              }"
            ></div>
          </div>
          
          
        </div>
        
        <div style="border: 1px solid #eee; border-radius: 8px; padding: 16px; display: flex; justify-content: center; background: #fafafa;">
          <ThreeAxisGantryLinearRobot
            :width="500"
            :height="400"
            :frame-width="150"
            :frame-height="100"
            :frame-depth="100"
            :x-position="currentState.x"
            :y-position="currentState.y"
            :z-position="currentState.z"
            :sucker-active="currentState.sucker"
            :animation-duration="600"
            :enable-animation="true"
          />
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {},
  },
};

// 物料演示示例
export const WithBlockMaterial: Story = {
  name: '龙门机器人携带木块',
  args: {
    material: {
      type: 'block',
      color: '#8B4513',
      label: '木块',
    },
    zPosition: 125, // 降低一些以便更好地看到物料
  },
};

export const WithCubeMaterial: Story = {
  name: '龙门机器人携带魔方',
  args: {
    material: {
      type: 'cube',
      color: '#FF6B6B',
      label: '魔方',
    },
    zPosition: 175,
  },
};

export const WithSphereMaterial: Story = {
  name: '龙门机器人携带球体',
  args: {
    material: {
      type: 'sphere',
      color: '#4ECDC4',
      size: { width: 20, height: 20 },
      label: '球体',
    },
    zPosition: 150,
  },
};

export const WithCylinderMaterial: Story = {
  name: '龙门机器人携带圆柱体',
  args: {
    material: {
      type: 'cylinder',
      color: '#95A5A6',
      size: { width: 16, height: 24 },
      label: '圆柱体',
    },
    zPosition: 100,
  },
};

export const WithTrayMaterial: Story = {
  name: '龙门机器人携带托盘',
  args: {
    material: {
      type: 'tray',
      color: '#E8E8E8',
      size: { width: 28, height: 12 },
      label: '托盘',
    },
    zPosition: 200,
  },
};

// 字符串形式的物料演示
export const WithStringMaterial: Story = {
  name: '龙门机器人字符串形式物料',
  args: {
    material: 'sphere', // 字符串形式，等同于使用默认配置的球体
    zPosition: 180,
  },
};

// 交互式物料和位置控制示例
export const MaterialAndPositionController: Story = {
  name: '龙门机器人物料和位置控制器',
  render: (args: any) => ({
    components: { ThreeAxisGantryLinearRobot },
    setup() {
      const currentMaterial = ref(null);
      const currentPosition = ref({
        x: 250,
        y: 250,
        z: 250,
        sucker: false,
      });

      const materialOptions = [
        { label: '无物料', value: null },
        {
          label: '木块',
          value: { type: 'block', color: '#8B4513' },
        },
        {
          label: '魔方',
          value: { type: 'cube', color: '#FF6B6B' },
        },
        {
          label: '球体',
          value: { type: 'sphere', color: '#4ECDC4' },
        },
        {
          label: '圆柱体',
          value: { type: 'cylinder', color: '#95A5A6' },
        },
        {
          label: '托盘',
          value: { type: 'tray', color: '#E8E8E8' },
        },
        {
          label: '字符串木块',
          value: 'block', // 字符串形式
        },
        {
          label: '字符串魔方',
          value: 'cube', // 字符串形式
        },
      ];

      return {
        args,
        currentMaterial,
        currentPosition,
        materialOptions,
      };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="display: flex; gap: 20px; padding: 16px; border: 1px solid #eee; border-radius: 8px;">
          <div style="flex: 1;">
            <h4 style="margin: 0 0 10px 0;">物料控制</h4>
            <div style="display: flex; flex-direction: column; gap: 8px;">
              <label>选择物料:</label>
              <select v-model="currentMaterial" style="padding: 6px; border-radius: 4px; border: 1px solid #ccc;">
                <option v-for="option in materialOptions" :key="option.label" :value="option.value">
                  {{ option.label }}
                </option>
              </select>
            </div>
          </div>

          <div style="flex: 1;">
            <h4 style="margin: 0 0 10px 0;">位置控制</h4>
            <div style="display: flex; flex-direction: column; gap: 8px;">
              <label>X轴位置: {{ currentPosition.x }}mm</label>
              <input type="range" v-model.number="currentPosition.x" min="0" max="500" step="10" style="width: 100%;" />

              <label>Y轴位置: {{ currentPosition.y }}mm</label>
              <input type="range" v-model.number="currentPosition.y" min="0" max="500" step="10" style="width: 100%;" />

              <label>Z轴位置: {{ currentPosition.z }}mm</label>
              <input type="range" v-model.number="currentPosition.z" min="0" max="500" step="10" style="width: 100%;" />

              <label style="display: flex; align-items: center; gap: 8px;">
                <input type="checkbox" v-model="currentPosition.sucker" />
                吸盘激活
              </label>
            </div>
          </div>
        </div>

        <div style="border: 1px solid #eee; border-radius: 8px; padding: 16px; display: flex; justify-content: center; background: #fafafa;">
          <ThreeAxisGantryLinearRobot
            :width="500"
            :height="450"
            :frame-width="150"
            :frame-height="100"
            :frame-depth="100"
            :x-position="currentPosition.x"
            :y-position="currentPosition.y"
            :z-position="currentPosition.z"
            :sucker-active="currentPosition.sucker"
            :material="currentMaterial"
            :animation-duration="600"
            :enable-animation="true"
          />
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story:
          '这个示例提供了一个交互式的龙门机器人控制器，您可以选择不同的物料类型和控制机器人的三轴位置以及吸盘状态。',
      },
    },
  },
};

// 位置反转功能演示
export const PositionReversalDemo: Story = {
  name: '位置反转功能演示',
  render: () => ({
    components: { ThreeAxisGantryLinearRobot },
    setup() {
      const currentPosition = ref({
        x: 100, // 设置为较小值，便于观察反转效果
        y: 150,
        z: 400,
        sucker: false,
      });

      const reversalSettings = ref({
        xReversed: false,
        yReversed: false,
        zReversed: false,
      });

      const presetPositions = [
        { x: 100, y: 150, z: 400, name: '左前上' },
        { x: 400, y: 350, z: 100, name: '右后下' },
        { x: 50, y: 450, z: 250, name: '左后中' },
        { x: 450, y: 50, z: 350, name: '右前上' },
      ];

      const moveToPosition = (pos: any) => {
        currentPosition.value = { ...currentPosition.value, ...pos };
      };

      const toggleAllReversals = () => {
        const allReversed =
          reversalSettings.value.xReversed &&
          reversalSettings.value.yReversed &&
          reversalSettings.value.zReversed;

        reversalSettings.value.xReversed = !allReversed;
        reversalSettings.value.yReversed = !allReversed;
        reversalSettings.value.zReversed = !allReversed;
      };

      return {
        currentPosition,
        reversalSettings,
        presetPositions,
        moveToPosition,
        toggleAllReversals,
      };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="padding: 16px; border: 1px solid #eee; border-radius: 8px;">
          <h3 style="margin: 0 0 16px 0;">位置反转功能控制面板</h3>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
            <div>
              <h4 style="margin: 0 0 12px 0;">位置控制</h4>
              <div style="display: flex; flex-direction: column; gap: 8px;">
                <label>X轴位置: {{ currentPosition.x }}mm</label>
                <input type="range" v-model.number="currentPosition.x" min="0" max="500" step="10" style="width: 100%;" />

                <label>Y轴位置: {{ currentPosition.y }}mm</label>
                <input type="range" v-model.number="currentPosition.y" min="0" max="500" step="10" style="width: 100%;" />

                <label>Z轴位置: {{ currentPosition.z }}mm</label>
                <input type="range" v-model.number="currentPosition.z" min="0" max="500" step="10" style="width: 100%;" />

                <label style="display: flex; align-items: center; gap: 8px;">
                  <input type="checkbox" v-model="currentPosition.sucker" />
                  吸盘激活
                </label>
              </div>
            </div>

            <div>
              <h4 style="margin: 0 0 12px 0;">反转控制</h4>
              <div style="display: flex; flex-direction: column; gap: 8px;">
                <label style="display: flex; align-items: center; gap: 8px;">
                  <input type="checkbox" v-model="reversalSettings.xReversed" />
                  X轴反转 ({{ reversalSettings.xReversed ? '0↔500' : '正常' }})
                </label>

                <label style="display: flex; align-items: center; gap: 8px;">
                  <input type="checkbox" v-model="reversalSettings.yReversed" />
                  Y轴反转 ({{ reversalSettings.yReversed ? '0↔500' : '正常' }})
                </label>

                <label style="display: flex; align-items: center; gap: 8px;">
                  <input type="checkbox" v-model="reversalSettings.zReversed" />
                  Z轴反转 ({{ reversalSettings.zReversed ? '0↔500' : '正常' }})
                </label>

                <button
                  @click="toggleAllReversals"
                  style="padding: 8px 12px; background: #1677ff; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 8px;"
                >
                  {{ (reversalSettings.xReversed && reversalSettings.yReversed && reversalSettings.zReversed) ? '全部取消反转' : '全部反转' }}
                </button>
              </div>
            </div>
          </div>

          <div style="margin-bottom: 16px;">
            <div style="margin-bottom: 8px; font-weight: bold;">预设位置快速测试：</div>
            <div style="display: flex; flex-wrap: wrap; gap: 8px;">
              <button
                v-for="(pos, index) in presetPositions"
                :key="index"
                @click="moveToPosition(pos)"
                style="padding: 6px 12px; background: #52c41a; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;"
              >
                {{ pos.name }}
              </button>
            </div>
          </div>

          <div style="font-size: 14px; color: #666; background: #f9f9f9; padding: 12px; border-radius: 4px;">
            <div><strong>当前状态：</strong></div>
            <div>位置: X={{ currentPosition.x }}mm, Y={{ currentPosition.y }}mm, Z={{ currentPosition.z }}mm</div>
            <div>反转: X={{ reversalSettings.xReversed ? '是' : '否' }}, Y={{ reversalSettings.yReversed ? '是' : '否' }}, Z={{ reversalSettings.zReversed ? '是' : '否' }}</div>
            <div style="margin-top: 8px; font-size: 12px; color: #999;">
              💡 提示：开启反转后，原来的0位置会变成最大值位置，最大值位置会变成0位置。试试调整位置滑块或点击预设位置按钮来观察反转效果！
            </div>
          </div>
        </div>

        <div style="border: 1px solid #eee; border-radius: 8px; padding: 16px; display: flex; justify-content: center; background: #fafafa;">
          <ThreeAxisGantryLinearRobot
            :width="500"
            :height="450"
            :frame-width="150"
            :frame-height="100"
            :frame-depth="100"
            :x-position="currentPosition.x"
            :y-position="currentPosition.y"
            :z-position="currentPosition.z"
            :sucker-active="currentPosition.sucker"
            :x-reversed="reversalSettings.xReversed"
            :y-reversed="reversalSettings.yReversed"
            :z-reversed="reversalSettings.zReversed"
            :animation-duration="600"
            :enable-animation="true"
          />
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story:
          '这个示例演示了三轴龙门机器人的位置反转功能。您可以独立控制每个轴的反转状态，观察机器人在不同反转设置下的位置变化。反转功能对于适应不同的机械配置和坐标系统非常有用。',
      },
    },
  },
};
