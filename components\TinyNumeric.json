{"name": {"zh_CN": "数字输入框"}, "component": "TinyNumeric", "icon": "numeric", "description": "通过鼠标或键盘输入字符", "docUrl": "", "screenshot": "", "tags": "", "keywords": "", "devMode": "proCode", "npm": {"package": "@opentiny/vue", "exportName": "Numeric"}, "group": "component", "priority": 1, "schema": {"properties": [{"label": {"zh_CN": "基础信息"}, "description": {"zh_CN": "基础信息"}, "content": [{"property": "modelValue", "label": {"text": {"zh_CN": "绑定值"}}, "required": true, "readOnly": false, "disabled": false, "widget": {"component": "I18nConfigurator", "props": {}}, "description": {"zh_CN": "双向绑定值"}, "labelPosition": "left"}, {"property": "placeholder", "label": {"text": {"zh_CN": "占位文本"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "I18nConfigurator", "props": {}}, "description": {"zh_CN": "输入框占位文本"}, "labelPosition": "left"}, {"property": "allow-empty", "label": {"text": {"zh_CN": "内容可清空"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否内容可清空"}, "labelPosition": "left"}, {"property": "disabled", "label": {"text": {"zh_CN": "禁用"}}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否禁用"}, "labelPosition": "left"}, {"property": "size", "label": {"text": {"zh_CN": "尺寸"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "medium", "value": "medium"}, {"label": "small", "value": "small"}, {"label": "mini", "value": "mini"}]}}, "description": {"zh_CN": "输入框尺寸。该属性的可选值为 medium / small / mini"}, "labelPosition": "left"}, {"property": "controls", "label": {"text": {"zh_CN": "加减按钮"}}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否使用加减按钮"}, "labelPosition": "left"}, {"property": "controls-position", "label": {"text": {"zh_CN": "加减按钮位置"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "左右两侧", "value": ""}, {"label": "只在右侧", "value": "right"}]}}, "description": {"zh_CN": "加减按钮位置"}}, {"property": "precision", "label": {"text": {"zh_CN": "精度"}}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "NumberConfigurator", "props": {"allowEmpty": true}}, "description": {"zh_CN": "数值精度"}, "labelPosition": "left"}, {"property": "step", "label": {"text": {"zh_CN": "步长"}}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "NumberConfigurator", "props": {"allowEmpty": true}}, "description": {"zh_CN": "步长"}, "labelPosition": "left"}, {"property": "max", "label": {"text": {"zh_CN": "最大数值"}}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "NumberConfigurator", "props": {"allowEmpty": true}}, "description": {"zh_CN": "可输入的最大数值"}, "labelPosition": "left"}, {"property": "min", "label": {"text": {"zh_CN": "最小数值"}}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "NumberConfigurator", "props": {"allowEmpty": true}}, "description": {"zh_CN": "可输入的最大数值"}, "labelPosition": "left"}]}], "events": {"onChange": {"label": {"zh_CN": "值改变时触发"}, "description": {"zh_CN": "在 Input 值改变时触发"}, "type": "event", "functionInfo": {"params": [{"name": "value", "type": "string", "defaultValue": "", "description": {"zh_CN": "输入框改变后的值"}}], "returns": {}}, "defaultValue": ""}, "onInput": {"label": {"zh_CN": "输入值改变时触发"}, "description": {"zh_CN": "在 Input 输入值改变时触发"}, "type": "event", "functionInfo": {"params": [{"name": "value", "type": "string", "defaultValue": "", "description": {"zh_CN": "输入框输入的值"}}], "returns": {}}, "defaultValue": ""}, "onUpdate:modelValue": {"label": {"zh_CN": "双向绑定的值改变时触发"}, "description": {"zh_CN": "在 Input 输入值改变时触发"}, "type": "event", "functionInfo": {"params": [{"name": "value", "type": "string", "defaultValue": "", "description": {"zh_CN": "双向绑定的值"}}], "returns": {}}, "defaultValue": ""}, "onBlur": {"label": {"zh_CN": "失去焦点时触发"}, "description": {"zh_CN": "在 Input 失去焦点时触发"}, "type": "event", "functionInfo": {"params": [{"name": "event", "type": "Object", "defaultValue": "", "description": {"zh_CN": "原生 event"}}], "returns": {}}, "defaultValue": ""}, "onFocus": {"label": {"zh_CN": "获取焦点时触发"}, "description": {"zh_CN": "在 Input 获取焦点时触发"}, "type": "event", "functionInfo": {"params": [{"name": "event", "type": "Object", "defaultValue": "", "description": {"zh_CN": "原生 event"}}], "returns": {}}, "defaultValue": ""}, "onClear": {"label": {"zh_CN": "点击清空按钮时触发"}, "description": {"zh_CN": "点击清空按钮时触发"}, "type": "event", "functionInfo": {"params": [], "returns": {}}, "defaultValue": ""}}}, "configure": {"loop": true, "condition": true, "styles": true, "isContainer": true, "isModal": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["value", "disabled"]}, "contextMenu": {"actions": ["create symbol"], "disable": ["copy", "remove"]}}, "snippets": [{"name": {"zh_CN": "数字输入框"}, "icon": "numeric", "screenshot": "", "snippetName": "TinyNumeric", "schema": {"componentName": "TinyNumeric", "props": {"allow-empty": true, "placeholder": "请输入", "controlsPosition": "right", "step": 1}}, "category": "form"}]}