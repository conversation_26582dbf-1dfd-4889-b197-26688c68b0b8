{"component": "StorageBox", "name": {"zh_CN": "储藏盒"}, "icon": "StorageBox", "group": "DCP", "category": "DCP", "description": "", "tags": "", "keywords": "", "doc_url": "", "devMode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "StorageBox", "version": "0.0.48", "script": "http://*************:4874/@dcp/component-library@0.0.48/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "configure": {"shortcuts": {"properties": ["rows", "columns", "width", "height"]}}, "schema": {"properties": [{"name": "0", "label": {"zh_CN": "基础属性"}, "description": {"zh_CN": "收纳盒的基本配置参数"}, "content": [{"property": "title", "label": {"text": {"zh_CN": "标题"}}, "description": {"zh_CN": "收纳盒的显示标题"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "收纳盒", "widget": {"component": "InputConfigurator", "props": {"placeholder": "请输入标题"}}}, {"property": "showTitle", "label": {"text": {"zh_CN": "显示标题"}}, "description": {"zh_CN": "是否显示收纳盒标题"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": true, "widget": {"component": "SwitchConfigurator", "props": {}}}, {"property": "rows", "label": {"text": {"zh_CN": "行数"}}, "description": {"zh_CN": "收纳盒的行数（高度方向格子数）"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 4, "widget": {"component": "NumberConfigurator", "props": {"min": 1, "max": 10, "step": 1}}}, {"property": "columns", "label": {"text": {"zh_CN": "列数"}}, "description": {"zh_CN": "收纳盒的列数（宽度方向格子数）"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 6, "widget": {"component": "NumberConfigurator", "props": {"min": 1, "max": 12, "step": 1}}}, {"property": "itemStartPosition", "label": {"text": {"zh_CN": "物料起始位置"}}, "description": {"zh_CN": "物料排列起始位置"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "top-left", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "左上", "value": "top-left"}, {"label": "右上", "value": "top-right"}, {"label": "左下", "value": "bottom-left"}, {"label": "右下", "value": "bottom-right"}]}}}, {"property": "sortDirection", "label": {"text": {"zh_CN": "物料排序方式"}}, "description": {"zh_CN": "物料排序方式"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "vertical", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "竖向", "value": "vertical"}, {"label": "横向", "value": "horizontal"}]}}}, {"property": "items", "label": {"text": {"zh_CN": "物料数据"}}, "description": {"zh_CN": "各个格子的物品信息数组。GridItem接口包含：name(物品名称), imagePath(图片路径), status('empty'|'occupied'|'disabled')"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "array", "defaultValue": [], "widget": {"component": "CodeConfigurator", "props": {"language": "json", "height": 150}}}, {"property": "statusToColor", "label": {"text": {"zh_CN": "状态到颜色映射"}}, "description": {}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "object", "defaultValue": {"occupied": "#4a90e2", "empty": "#f0f0f0", "disabled": "#cccccc"}, "widget": {"component": "CodeConfigurator", "props": {"language": "json", "height": 150}}}]}, {"name": "1", "label": {"zh_CN": "样式属性"}, "description": {"zh_CN": "收纳盒的外观和尺寸配置"}, "content": [{"property": "width", "label": {"text": {"zh_CN": "宽度"}}, "description": {"zh_CN": "收纳盒的宽度，单位为像素"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 400, "widget": {"component": "NumberConfigurator", "props": {"min": 50, "max": 2000, "step": 10}}}, {"property": "height", "label": {"text": {"zh_CN": "高度"}}, "description": {"zh_CN": "收纳盒的高度，单位为像素"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 300, "widget": {"component": "NumberConfigurator", "props": {"min": 50, "max": 2000, "step": 10}}}, {"property": "boxColor", "label": {"text": {"zh_CN": "盒体颜色"}}, "description": {"zh_CN": "收纳盒的主体颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#f5f5f5", "widget": {"component": "ColorConfigurator", "props": {}}}, {"property": "titleColor", "label": {"text": {"zh_CN": "标题颜色"}}, "description": {"zh_CN": "标题文字的颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#333333", "widget": {"component": "ColorConfigurator", "props": {}}}, {"property": "titleFontSize", "label": {"text": {"zh_CN": "标题字体大小"}}, "description": {"zh_CN": "标题文字的字体大小，单位为像素"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 16, "widget": {"component": "NumberConfigurator", "props": {"min": 12, "max": 24, "step": 1}}}, {"property": "itemNameColor", "label": {"text": {"zh_CN": "物品名称颜色"}}, "description": {"zh_CN": "物品名称文字的颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#666666", "widget": {"component": "ColorConfigurator", "props": {}}}]}, {"name": "2", "label": {"zh_CN": "显示配置"}, "description": {"zh_CN": "控制各种显示选项的开关"}, "content": [{"property": "showItemNames", "label": {"text": {"zh_CN": "显示物品名称"}}, "description": {"zh_CN": "是否显示物品的名称标签"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": true, "widget": {"component": "SwitchConfigurator", "props": {}}}, {"property": "showGridNumbers", "label": {"text": {"zh_CN": "显示格子编号"}}, "description": {"zh_CN": "是否显示每个格子的编号"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator", "props": {}}}]}], "events": {"onGridClick": {"label": {"zh_CN": "格子点击"}, "description": {"zh_CN": "当用户点击格子时触发"}, "functionInfo": {"params": []}}, "onItemSelect": {"label": {"zh_CN": "物品选择"}, "description": {"zh_CN": "当用户选择物品时触发"}, "functionInfo": {"params": []}}, "onUpdateItems": {"label": {"zh_CN": "物品更新"}, "description": {"zh_CN": "当物品数据更新时触发"}, "functionInfo": {"params": []}}}, "slots": {}}, "snippets": [{"name": {"zh_CN": "储藏盒"}, "icon": "StorageBox", "snippetName": "StorageBox", "schema": {"props": {"title": "收纳盒", "showTitle": false, "boxColor": "#f5f5f5", "width": 500, "height": 350, "rows": 4, "columns": 6, "items": [{"status": "Completed", "name": "物品1"}], "statusToColor": {"Idled": "#909399", "Processing": "#409EFF", "Completed": "#67C23A", "Aborted": "#E6A23C", "Failed": "#DC3545", "Returning": "#9370DB", "Returned": "#B0C4DE", "CompleteReturning": "#48D1CC"}, "showGridNumbers": false, "showItemNames": true, "titleColor": "#333333", "titleFontSize": 16, "itemNameColor": "#666666"}}}]}