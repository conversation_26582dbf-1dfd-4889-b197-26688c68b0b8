{"component": "ThreeAxisGantryLinearRobot", "name": {"zh_CN": "三轴龙门直线机器人"}, "icon": "ThreeAxisGantryLinearRobot", "group": "DCP", "category": "DCP", "description": "三轴龙门直线机器人组件，支持X、Y、Z三轴位置控制和吸盘操作，具有3D等轴投影视觉效果", "tags": "", "keywords": "", "doc_url": "", "devMode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "ThreeAxisGantryLinearRobot", "version": "0.0.49", "script": "http://*************:4874/@dcp/component-library@0.0.49/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "isNullNode": false, "isLayout": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "rootSelector": "", "shortcuts": {"properties": ["xPosition", "yPosition", "zPosition"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}, "clickCapture": false, "framework": "<PERSON><PERSON>"}, "schema": {"properties": [{"name": "0", "label": {"zh_CN": "基础属性"}, "description": {"zh_CN": "机器人的位置控制和状态配置"}, "content": [{"property": "xMaxStroke", "label": {"text": {"zh_CN": "X轴最大行程(mm)"}}, "description": "X轴最大行程(mm)", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 500, "widget": {"component": "NumberConfigurator", "props": {"step": 1}}}, {"property": "yMaxStroke", "label": {"text": {"zh_CN": "Y轴最大行程(mm)"}}, "description": "Y轴最大行程(mm)", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 500, "widget": {"component": "NumberConfigurator", "props": {"step": 1}}}, {"property": "zMaxStroke", "label": {"text": {"zh_CN": "Z轴最大行程(mm)"}}, "description": "Z轴最大行程(mm)", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 500, "widget": {"component": "NumberConfigurator", "props": {"step": 1}}}, {"property": "xReversed", "label": {"text": {"zh_CN": "X轴位置反转"}}, "description": "X轴位置反转：true时0位置变为最大值位置，最大值位置变为0位置", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator", "props": {}}}, {"property": "yReversed", "label": {"text": {"zh_CN": "Y轴位置反转"}}, "description": "Y轴位置反转：true时0位置变为最大值位置，最大值位置变为0位置", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator", "props": {}}}, {"property": "zReversed", "label": {"text": {"zh_CN": "Z轴位置反转"}}, "description": "Z轴位置反转：true时0位置变为最大值位置，最大值位置变为0位置", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator", "props": {}}}, {"property": "xPosition", "label": {"text": {"zh_CN": "X轴位置"}}, "description": "X轴位置", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 0, "widget": {"component": "NumberConfigurator", "props": {"step": 1}}}, {"property": "yPosition", "label": {"text": {"zh_CN": "Y轴位置"}}, "description": "Y轴位置", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 0, "widget": {"component": "NumberConfigurator", "props": {"step": 1}}}, {"property": "zPosition", "label": {"text": {"zh_CN": "Z轴位置"}}, "description": "Z轴位置", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 0, "widget": {"component": "NumberConfigurator", "props": {"step": 1}}}, {"property": "material", "label": {"text": {"zh_CN": "物料"}}, "description": "当前抓手上的物料", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "block", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "无物料", "value": null}, {"label": "木块", "value": "block"}, {"label": "魔方", "value": "cube"}, {"label": "球体", "value": "sphere"}, {"label": "圆柱体", "value": "cylinder"}, {"label": "托盘", "value": "tray"}]}}}, {"property": "suckerActive", "label": {"text": {"zh_CN": "吸盘状态"}}, "description": "吸盘状态：true为吸附状态，false为释放状态", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator", "props": {}}}]}, {"name": "1", "label": {"zh_CN": "样式属性"}, "description": {"zh_CN": "组件的外观和尺寸配置"}, "content": [{"property": "width", "label": {"text": {"zh_CN": "画布宽度"}}, "description": "组件画布宽度（像素）", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 250, "widget": {"component": "NumberConfigurator", "props": {"min": 200, "max": 600, "step": 10}}}, {"property": "height", "label": {"text": {"zh_CN": "画布高度"}}, "description": "组件画布高度（像素）", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 300, "widget": {"component": "NumberConfigurator", "props": {"min": 200, "max": 600, "step": 10}}}, {"property": "frameWidth", "label": {"text": {"zh_CN": "框架宽度"}}, "description": "龙门框架宽度（X方向）", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 150, "widget": {"component": "NumberConfigurator", "props": {"min": 80, "max": 300, "step": 10}}}, {"property": "frameHeight", "label": {"text": {"zh_CN": "框架高度"}}, "description": "龙门框架高度（Z<PERSON>向）", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 100, "widget": {"component": "NumberConfigurator", "props": {"min": 60, "max": 200, "step": 10}}}, {"property": "<PERSON><PERSON><PERSON><PERSON>", "label": {"text": {"zh_CN": "框架深度"}}, "description": "龙门框架深度（Y方向）", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 100, "widget": {"component": "NumberConfigurator", "props": {"min": 60, "max": 200, "step": 10}}}]}, {"name": "2", "label": {"zh_CN": "行为属性"}, "description": {"zh_CN": "动画和交互行为配置"}, "content": [{"property": "animationDuration", "label": {"text": {"zh_CN": "动画持续时间"}}, "description": "动画持续时间（毫秒）", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 800, "widget": {"component": "NumberConfigurator", "props": {"min": 100, "max": 2000, "step": 100}}}, {"property": "enableAnimation", "label": {"text": {"zh_CN": "启用动画"}}, "description": "是否启用平滑动画过渡", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": true, "widget": {"component": "SwitchConfigurator", "props": {}}}]}], "events": {}, "slots": {}}, "snippets": [{"name": {"zh_CN": "三轴龙门直线机器人"}, "icon": "ThreeAxisGantryLinearRobot", "snippetName": "ThreeAxisGantryLinearRobot", "schema": {"props": {"width": 400, "height": 350, "frameWidth": 150, "frameHeight": 100, "frameDepth": 100, "xPosition": 0, "yPosition": 0, "zPosition": 0, "suckerActive": false, "animationDuration": 800, "enableAnimation": true}}}]}