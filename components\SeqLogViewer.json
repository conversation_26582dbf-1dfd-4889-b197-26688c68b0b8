{"id": 1, "version": "0.0.45", "name": {"zh_CN": "SEQ日志查看器"}, "component": "SeqLogViewer", "icon": "document-text", "description": "功能完整的SEQ日志查看器组件，支持搜索、筛选、分页、导出和详情查看功能", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "SeqLogViewer", "version": "0.0.45", "script": "http://*************:4874/@dcp/component-library@0.0.45/js/web-component.mjs", "destructuring": true}, "group": "DCP", "category": "DCP", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["baseUrl", "<PERSON><PERSON><PERSON><PERSON>", "autoConnect", "autoRefreshInterval", "showExportButtons", "enableAutoRefresh", "tableHeight"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}}, "schema": {"properties": [{"label": {"zh_CN": "基础配置"}, "description": {"zh_CN": "SEQ日志查看器的基础配置属性"}, "content": [{"property": "baseUrl", "label": {"text": {"zh_CN": "SEQ服务地址"}}, "description": {"zh_CN": "SEQ服务的连接地址"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "http://*************:8880", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "<PERSON><PERSON><PERSON><PERSON>", "label": {"text": {"zh_CN": "SEQ服务API密钥"}}, "description": {"zh_CN": "SEQ服务的API密钥"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "vIAV2wnkd0EdkyGz46qZ", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "autoConnect", "label": {"text": {"zh_CN": "自动连接"}}, "description": {"zh_CN": "是否自动连接SEQ服务"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": true, "widget": {"component": "SwitchConfigurator", "props": {}}}, {"property": "autoRefreshInterval", "label": {"text": {"zh_CN": "自动刷新间隔"}}, "description": {"zh_CN": "自动刷新的时间间隔（毫秒）"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 30000, "widget": {"component": "NumberConfigurator", "props": {"min": 1000, "max": 300000, "step": 1000}}}, {"property": "tableHeight", "label": {"text": {"zh_CN": "表格高度"}}, "description": {"zh_CN": "日志表格的显示高度（像素）"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 650, "widget": {"component": "NumberConfigurator", "props": {"min": 300, "max": 1200, "step": 50}}}]}, {"label": {"zh_CN": "功能配置"}, "description": {"zh_CN": "日志查看器的功能开关配置"}, "content": [{"property": "showExportButtons", "label": {"text": {"zh_CN": "显示导出按钮"}}, "description": {"zh_CN": "是否显示JSON和CSV导出按钮"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": true, "widget": {"component": "SwitchConfigurator", "props": {}}}, {"property": "enableAutoRefresh", "label": {"text": {"zh_CN": "启用自动刷新"}}, "description": {"zh_CN": "是否启用日志数据的自动刷新功能"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": true, "widget": {"component": "SwitchConfigurator", "props": {}}}, {"property": "enableAdvancedSettings", "label": {"text": {"zh_CN": "显示高级设置"}}, "description": {"zh_CN": "是否在搜索工具栏中显示高级设置选项"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator", "props": {}}}]}], "events": [], "methods": []}, "snippets": [{"name": {"zh_CN": "基础SEQ日志查看器"}, "screenshot": "", "snippetName": "SeqLogViewer", "schema": {"props": {"baseUrl": "http://*************:8880", "apiKey": "vIAV2wnkd0EdkyGz46qZ", "autoConnect": true, "enableAutoRefresh": true, "showExportButtons": true, "autoRefreshInterval": 30000, "tableHeight": 650, "enableAdvancedSettings": false}}}]}