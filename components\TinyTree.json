{"icon": "tree", "name": {"zh_CN": "树"}, "component": "TinyTree", "description": "可进行展示有父子层级的数据，支持选择，异步加载等功能。但不推荐用它来展示菜单，展示菜单推荐使用树菜单", "docUrl": "", "screenshot": "", "tags": "", "keywords": "", "devMode": "proCode", "npm": {"package": "@opentiny/vue", "exportName": "Tree"}, "group": "component", "priority": 12, "schema": {"properties": [{"label": {"zh_CN": "基础信息"}, "description": {"zh_CN": "基础信息"}, "content": [{"property": "show-checkbox", "label": {"text": {"zh_CN": "多选"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "设置接口是否可以多选"}, "labelPosition": "left"}, {"property": "data", "label": {"text": {"zh_CN": "数据源"}}, "required": true, "readOnly": false, "disabled": false, "defaultValue": [{"label": "一级 1", "children": [{"label": "二级 1-1"}]}], "cols": 12, "widget": {"component": "CodeConfigurator", "props": {}}, "description": {"zh_CN": "可配置静态数据源和动态数据源"}, "labelPosition": "top"}, {"property": "node-key", "label": {"text": {"zh_CN": "唯一标识"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {}}, "description": {"zh_CN": "节点唯一标识属性名称"}, "labelPosition": "left"}, {"property": "render-content", "label": {"text": {"zh_CN": "渲染函数"}}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {"disabled": true, "placeholder": "请使用变量绑定来绑定函数"}}, "description": {"zh_CN": "树节点的内容区的渲染函数"}}, {"property": "icon-trigger-click-node", "label": {"text": {"zh_CN": "触发NodeClick事件"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "点击图标展开节点时是否触发 node-click 事件"}, "labelPosition": "left"}, {"property": "expand-icon", "label": {"text": {"zh_CN": "展开图标"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CodeConfigurator", "props": {}}, "description": {"zh_CN": "节点展开图标"}, "labelPosition": "top"}, {"property": "shrink-icon", "label": {"text": {"zh_CN": "收缩图标"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CodeConfigurator", "props": {}}, "description": {"zh_CN": "节点收缩的图标"}, "labelPosition": "top"}]}, {"name": "1", "label": {"zh_CN": "其他"}, "content": [{"property": "check-on-click-node", "label": {"text": {"zh_CN": "点击节点选中"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否在点击节点的时候选中节点，默认值为 false，即只有在点击复选框时才会选中节点"}, "labelPosition": "left"}, {"property": "filter-node-method", "label": {"text": {"zh_CN": "筛选函数"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CodeConfigurator", "props": {}}, "description": {"zh_CN": "节点筛选函数"}, "labelPosition": "top"}], "description": {"zh_CN": ""}}], "events": {"onCheck": {"label": {"zh_CN": "勾选节点后的事件"}, "description": {"zh_CN": "勾选节点后的事件"}, "type": "event", "functionInfo": {"params": [{"name": "data", "type": "object", "defaultValue": "", "description": {"zh_CN": "当前选中节点信息"}}, {"name": "currentNode", "type": "object", "defaultValue": "", "description": {"zh_CN": "树组件目前的选中状态信息，包含 checkedNodes、checkedKeys、halfCheckedNodes、halfCheckedKeys 四个属性"}}], "returns": {}}, "defaultValue": ""}, "onNodeClick": {"label": {"zh_CN": "点击节点后的事件"}, "description": {"zh_CN": "点击节点后的事件"}, "type": "event", "functionInfo": {"params": [{"name": "data", "type": "Object", "defaultValue": "", "description": {"zh_CN": "当前选中节点信息"}}, {"name": "node", "type": "Object", "defaultValue": "", "description": {"zh_CN": "树组件目前的选中状态信息，包含 checkedNodes、checkedKeys、halfCheckedNodes、halfCheckedKeys 四个属性"}}, {"name": "vm", "type": "Object", "defaultValue": "", "description": {"zh_CN": "树组件实例"}}], "returns": {}}, "defaultValue": ""}}}, "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["data", "show-checkbox"]}, "contextMenu": {"actions": ["create symbol"], "disable": ["copy", "remove"]}}, "snippets": [{"name": {"zh_CN": "树"}, "icon": "tree", "screenshot": "", "snippetName": "TinyTree", "schema": {"componentName": "TinyTree", "props": {"data": [{"label": "一级 1", "children": [{"label": "二级 1-1", "children": [{"label": "三级 1-1-1"}]}]}, {"label": "一级 2", "children": [{"label": "二级 2-1", "children": [{"label": "三级 2-1-1"}]}, {"label": "二级 2-2", "children": [{"label": "三级 2-2-1"}]}]}]}}, "category": "data-display"}]}