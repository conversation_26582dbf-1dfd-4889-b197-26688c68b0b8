import type { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import Wafer from './index.vue';

const meta = {
  title: 'Equipment/Wafer',
  component: Wafer,
  tags: ['autodocs'],
  argTypes: {
    size: { control: 'number', description: '晶圆尺寸（像素）' },
    color: { control: 'color', description: '晶圆颜色' },
    rotate: { control: 'number', description: '旋转角度' },
    name: { control: 'text', description: '晶圆名称' },
    scale: { control: 'number', description: '缩放比例' },
    waferShape: {
      control: 'select',
      options: ['circle', 'square'],
      description: '晶圆形状',
    },
    showOcr: { control: 'boolean', description: '是否显示OCR信息' },
    ocrIo: { control: 'text', description: 'OCR识别信息' },
    fontSize: { control: 'number', description: '字体大小' },
  },
  args: {
    size: 60,
    color: '#000',
    rotate: 0,
    name: 'A01',
    scale: 1,
    waferShape: 'circle',
    showOcr: false,
    ocrIo: '',
    fontSize: 14,
  },
  parameters: {
    docs: {
      description: {
        component:
          'Wafer组件用于显示晶圆，支持不同形状、颜色和尺寸的配置，可以显示名称和OCR信息。',
      },
    },
  },
} satisfies Meta<typeof Wafer>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  name: '基础示例',
  args: {},
};

// 方形晶圆
export const SquareWafer: Story = {
  name: '方形晶圆',
  args: {
    waferShape: 'square',
    color: '#1677ff',
  },
};

// 带OCR信息
export const WithOcr: Story = {
  name: '显示OCR信息',
  args: {
    showOcr: true,
    ocrIo: 'O-12',
    color: '#2196F3',
  },
};

// 不同尺寸
export const DifferentSizes: Story = {
  name: '不同尺寸',
  render: (args) => ({
    components: { Wafer },
    setup() {
      return { args };
    },
    template: `
      <div style="display: flex; gap: 20px; align-items: center;">
        <div>
          <Wafer v-bind="args" :size="40" name="小" />
          <div style="text-align: center; margin-top: 10px;">40px</div>
        </div>
        <div>
          <Wafer v-bind="args" :size="60" name="中" />
          <div style="text-align: center; margin-top: 10px;">60px</div>
        </div>
        <div>
          <Wafer v-bind="args" :size="80" name="大" />
          <div style="text-align: center; margin-top: 10px;">80px</div>
        </div>
        <div>
          <Wafer v-bind="args" :size="100" name="特大" />
          <div style="text-align: center; margin-top: 10px;">100px</div>
        </div>
      </div>
    `,
  }),
};

// 不同颜色
export const DifferentColors: Story = {
  name: '不同颜色',
  render: (args) => ({
    components: { Wafer },
    setup() {
      const colors = [
        { name: '默认', value: '#000000' },
        { name: '蓝色', value: '#1677ff' },
        { name: '绿色', value: '#52c41a' },
        { name: '红色', value: '#f5222d' },
        { name: '橙色', value: '#fa8c16' },
        { name: '紫色', value: '#722ed1' },
      ];

      return { args, colors };
    },
    template: `
      <div style="display: flex; gap: 20px; flex-wrap: wrap;">
        <div v-for="color in colors" :key="color.value" style="text-align: center;">
          <Wafer v-bind="args" :color="color.value" :name="color.name" />
          <div style="margin-top: 10px;">{{ color.name }}</div>
        </div>
      </div>
    `,
  }),
};

// 旋转和缩放
export const RotateAndScale: Story = {
  name: '旋转和缩放',
  render: (args) => ({
    components: { Wafer },
    setup() {
      const rotate = ref(0);
      const scale = ref(1);

      return { args, rotate, scale };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="display: flex; gap: 20px; align-items: center;">
          <div style="width: 150px;">
            <Wafer v-bind="args" :rotate="rotate" :scale="scale" />
          </div>
          <div style="flex: 1;">
            <div style="margin-bottom: 10px;">
              <label>旋转角度: {{ rotate }}°</label>
              <input type="range" v-model.number="rotate" min="0" max="360" style="width: 100%;" />
            </div>
            <div>
              <label>缩放比例: {{ scale.toFixed(1) }}</label>
              <input type="range" v-model.number="scale" min="0.5" max="2" step="0.1" style="width: 100%;" />
            </div>
          </div>
        </div>
      </div>
    `,
  }),
};
