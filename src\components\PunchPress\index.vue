<template>
  <div>
    <svg
      xmlns="http://www.w3.org/2000/svg"
      :width="width"
      :height="height"
      viewBox="0 0 833 999"
    >
      <path
        :style="`fill: ${colors.neutral}; stroke: ${colors.stroke}; stroke-width: 2`"
        d="M705,401 l-52,14 -236,0 0,-415 236,0 52,14 48,28 37,37 29,48 14,52 0,56 -14,52 -29,48 -37,38 -48,28z"
      />
      <circle
        :style="`fill: ${colors.light}; stroke: ${colors.stroke}; stroke-width: 2`"
        cx="425"
        cy="34"
        r="34"
      />
      <!-- 连杆 -->
      <path
        :style="`fill: ${colors.darker}; stroke: ${colors.darker}; stroke-width: 2`"
        :d="linkPath"
      />
      <!-- 冲头开始 -->
      <path :style="`fill: ${colors.neutral}`" :d="punchHeadMainPath" />
      <path :style="`fill: ${colors.lighter}`" :d="punchHeadStripePath1" />
      <path :style="`fill: ${colors.dark}`" :d="punchHeadShadowPath" />
      <path :style="`fill: ${colors.light}`" :d="punchHeadStripePath2" />
      <path
        :style="`fill: none; stroke: ${colors.stroke}; stroke-width: 2`"
        :d="punchHeadOutlinePath"
      />
      <path
        :style="`fill: none; stroke: ${colors.highlight}; stroke-width: 2`"
        :d="punchHeadHighlightPath1"
      />
      <path
        :style="`fill: none; stroke: ${colors.highlight}; stroke-width: 2`"
        :d="punchHeadHighlightPath2"
      />
      <!-- 冲头结束 -->

      <path
        :style="`fill: ${colors.light}`"
        d="M126,936 l0,-563 41,-62 0,-249 332,0 0,395 -40,0 -22,20 0,272 188,0 0,104 -42,83 -457,0z"
      />
      <path
        :style="`fill: ${colors.neutral}`"
        d="M126,936 l0,-291 311,-138 0,242 188,0 0,104 -42,83 -457,0z"
      />
      <path
        :style="`fill: ${colors.light}`"
        d="M126,936 l0,-259 311,-136 0,208 188,0 0,104 -42,83 -457,0z"
      />
      <path
        :style="`fill: ${colors.dark}`"
        d="M126,936 l0,-133 311,-138 0,84 188,0 0,104 -42,83 -457,0z"
      />
      <path
        :style="`fill: none; stroke: ${colors.stroke}; stroke-width: 2`"
        d="M126,936 l0,-563 41,-62 0,-249 332,0 0,395 -40,0 -22,20 0,272 188,0 0,104 -42,83 -457,0"
      />
      <path
        :style="`fill: ${colors.light}; stroke: ${colors.stroke}; stroke-width: 2`"
        d="M625,999 l-84,-166 -332,0 -64,-126 -40,0 -105,292 625,0z"
      />
      <path
        style="fill: none; stroke: #fff; stroke-width: 2"
        d="M479,311 l0,126 -20,0"
      />
      <path
        style="fill: none; stroke: #7f7f7f; stroke-width: 2"
        d="M479,311 l-46,0 0,146 -10,20 0,272"
      />
      <path
        style="fill: none; stroke: #fff; stroke-width: 2"
        d="M0,999 l21,-23 62,0 22,23"
      />
      <path
        style="fill: none; stroke: #fff; stroke-width: 2"
        d="M521,999 l20,-23 62,0 22,23"
      />
      <path
        style="fill: none; stroke: #7f7f7f; stroke-width: 2"
        d="M531,838 l10,19 -10,15 -20,0"
      />
      <path
        style="fill: none; stroke: #7f7f7f; stroke-width: 2"
        d="M136,713 l9,18 -9,18 -20,0"
      />
      <path
        style="fill: none; stroke: #fff; stroke-width: 2"
        d="M511,872 l-12,-15 12,-19 20,0"
      />
      <path
        style="fill: none; stroke: #fff; stroke-width: 2"
        d="M116,749 l-11,-18 11,-18 20,0"
      />
      <path
        :style="`fill: ${colors.light}; stroke: ${colors.stroke}; stroke-width: 2`"
        d="M625,833 l42,-22 0,-62 -42,-22 0,106z"
      />
      <path
        :style="`fill: ${colors.neutral}; stroke: ${colors.stroke}; stroke-width: 2`"
        d="M459,0 l124,0 0,83 64,0 0,208 -188,0 0,-291z"
      />
      <path
        :style="`fill: ${colors.light}`"
        d="M437,333 l0,-178 6,-22 12,-17 14,-17 20,-10 20,-6 22,0 22,4 18,12 16,17 12,17 4,22 0,178 -166,0z"
      />
      <circle :style="`fill: ${colors.dark}`" cx="521" cy="166" r="20" />
      <path
        :style="`fill: ${colors.neutral}; stroke: ${colors.stroke}; stroke-width: 2`"
        d="M499,333 l22,0 0,124 -22,0 0,-124z"
      />
      <path
        :style="`fill: ${colors.light}; stroke: ${colors.stroke}; stroke-width: 2`"
        d="M521,333 l0,124 10,0 0,-62 22,0 0,-42 -22,0 0,-20 -10,0z"
      />
      <path
        :style="`fill: ${colors.dark}`"
        d="M437,749 l188,0 0,10 -188,0 0,-10z"
      />
      <path
        :style="`fill: ${colors.lighter}`"
        d="M343,477 l68,0 0,42 -68,0 0,-42z"
      />
      <path
        :style="`fill: ${colors.darker}`"
        d="M136,477 l207,0 0,42 -207,0 0,-42z"
      />
      <path
        :style="`fill: ${colors.neutral}`"
        d="M437,333 l0,-84 166,-74 0,158 -166,0z"
      />
      <path
        :style="`fill: none; stroke: ${colors.stroke}; stroke-width: 2`"
        d="M437,333 l0,-178 6,-22 12,-17 14,-17 20,-10 20,-6 22,0 22,4 18,12 16,17 12,17 4,22 0,178 -166,0"
      />
      <path
        :style="`fill: ${colors.neutral}; stroke: ${colors.stroke}; stroke-width: 2`"
        d="M437,727 l188,0 0,22 -188,0 0,-22z"
      />
      <path
        :style="`fill: ${colors.darker}`"
        d="M579,727 l4,0 0,22 -4,0 0,-22z"
      />
      <path
        :style="`fill: ${colors.darker}`"
        d="M479,727 l6,0 0,22 -6,0 0,-22z"
      />
      <path
        :style="`fill: ${colors.light}; stroke: ${colors.stroke}; stroke-width: 2`"
        d="M437,687 l188,0 0,42 -188,0 0,-42z"
      />
      <path
        :style="`fill: ${colors.darker}`"
        d="M443,405 l26,0 0,26 -26,0 0,-26z"
      />
      <path
        :style="`fill: ${colors.dark}`"
        d="M443,373 l26,0 0,26 -26,0 0,-26z"
      />
      <path
        :style="`fill: ${colors.neutral}`"
        d="M443,343 l26,0 0,26 -26,0 0,-26z"
      />
      <path
        :style="`fill: ${colors.lighter}`"
        d="M453,405 l16,0 0,16 -16,0 0,-16z"
      />
      <path
        style="fill: none; stroke: #7f7f7f; stroke-width: 2"
        d="M539,379 l8,-8 -8,-8 -8,8 8,8"
      />
      <path
        style="fill: #999; stroke: #7f7f7f; stroke-width: 2"
        d="M489,451 l1,0 2,-1 2,-1 1,-2 -16,-2 -15,-2 -14,-3 -12,-3 -11,-3 -10,-4 -9,-4 -8,-4 -8,-4 -6,-5 -6,-5 -6,-5 -5,-5 -4,-5 -5,-5 -4,-5 -4,-5 -4,-4 -5,-5 -4,-4 -5,-4 -5,-4 -5,-3 -6,-4 -7,-2 -7,-3 -8,-1 -10,-2 -10,0 -11,-1 -12,1 -14,1 0,3 0,2 0,3 0,2 11,-1 9,-1 10,0 8,1 7,1 7,2 7,2 6,3 6,4 6,3 5,4 6,4 4,5 6,5 5,5 5,5 5,5 6,5 5,5 6,5 7,5 6,5 8,4 8,4 8,4 10,3 10,3 10,3 12,2 13,2 13,1 15,0z"
      />
      <path
        :style="`fill: ${colors.dark}`"
        d="M229,327 l20,0 0,58 -20,0 0,-58z"
      />
      <path
        :style="`fill: ${colors.dark}`"
        d="M271,343 l52,0 0,46 -52,0 0,-46z"
      />
      <path
        :style="`fill: ${colors.dark}; stroke: ${colors.darker}; stroke-width: 2`"
        d="M333,359 l52,0 0,46 -52,0 0,-46z"
      />
      <path
        :style="`fill: ${colors.neutral}`"
        d="M231,327 l16,0 0,56 -16,0 0,-56z"
      />
      <path
        :style="`fill: ${colors.neutral}`"
        d="M277,343 l40,0 0,46 -40,0 0,-46z"
      />
      <path
        :style="`fill: ${colors.neutral}`"
        d="M339,359 l42,0 0,46 -42,0 0,-46z"
      />
      <path
        :style="`fill: ${colors.light}`"
        d="M343,359 l32,0 0,46 -32,0 0,-46z"
      />
      <path
        :style="`fill: ${colors.light}`"
        d="M281,343 l32,0 0,46 -32,0 0,-46z"
      />
      <path
        :style="`fill: ${colors.light}`"
        d="M233,327 l12,0 0,56 -12,0 0,-56z"
      />
      <path
        :style="`fill: ${colors.lighter}`"
        d="M349,359 l20,0 0,46 -20,0 0,-46z"
      />
      <path
        :style="`fill: ${colors.lighter}`"
        d="M287,343 l20,0 0,46 -20,0 0,-46z"
      />
      <path
        :style="`fill: ${colors.lighter}`"
        d="M235,327 l8,0 0,56 -8,0 0,-56z"
      />
      <path
        :style="`fill: ${colors.highlight}`"
        d="M353,359 l12,0 0,46 -12,0 0,-46z"
      />
      <path
        :style="`fill: ${colors.highlight}`"
        d="M291,343 l12,0 0,46 -12,0 0,-46z"
      />
      <path
        :style="`fill: ${colors.highlight}`"
        d="M237,327 l4,0 0,58 -4,0 0,-58z"
      />
      <path
        :style="`fill: none; stroke: ${colors.darker}; stroke-width: 2`"
        d="M271,343 l52,0 0,46 -52,0 0,-46"
      />
      <path
        :style="`fill: none; stroke: ${colors.darker}; stroke-width: 2`"
        d="M229,327 l20,0 0,58 -20,0 0,-58"
      />
      <path
        :style="`fill: ${colors.light}`"
        d="M645,351 l118,-118 46,44 -118,118 -46,-44z"
      />
      <circle
        :style="`fill: none; stroke: ${colors.stroke}; stroke-width: 2`"
        cx="521"
        cy="40"
        r="20"
      />
    </svg>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref, watch, onMounted, toRefs } from 'vue';
import Color from 'color';
import { gsap } from 'gsap';
import { useComponentState } from '@/communication/hooks/useComponentState';
import { createComponentProps } from '@/context/ComponentContext';

export default defineComponent({
  name: 'PunchPress',
  props: createComponentProps({
    width: {
      type: Number,
      default: 64,
    },
    height: {
      type: Number,
      default: 64,
    },
    color: {
      type: String,
      default: '#000',
    },
    state: {
      type: String,
      default: 'collapse',
      validator: (value: string) => {
        return ['collapse', 'expand'].includes(value);
      },
    },
    animate: {
      type: Boolean,
      default: false,
    },
  }),
  setup(props) {
    const { state: compState, animate, color } = toRefs(props);

    // 使用组件状态Hook（响应式工厂函数版本）
    const { state } = useComponentState(
      props.partId,
      () => ({
        state: compState.value,
        animate: animate.value,
        color: color.value,
      }),
      props.dynamicPartInfo,
    );

    // 动画偏移量状态
    const animatedOffset = ref(0);

    // 目标偏移量
    const targetOffset = computed(() => {
      return state.state === 'expand' ? 140 : 0;
    });

    // 动画时间线
    let tl: gsap.core.Timeline;

    // 颜色系统
    const colors = computed(() => {
      const baseColor = Color(state.color);
      return {
        // 主色调
        primary: baseColor.hex(),
        // 较暗的阴影色
        dark: baseColor.darken(0.2).hex(),
        // 更暗的阴影色
        darker: baseColor.darken(0.4).hex(),
        // 较亮的高光色
        light: baseColor.lighten(0.2).hex(),
        // 更亮的高光色
        lighter: baseColor.lighten(0.4).hex(),
        // 去饱和的中性色
        neutral: baseColor.desaturate(0.3).hex(),
        // 高光白色（基于亮度调整）
        highlight:
          baseColor.lightness() > 50 ? '#ffffff' : baseColor.lighten(0.6).hex(),
        // 描边色
        stroke: baseColor.darken(0.6).hex(),
      };
    });

    const linkPath = computed(() => {
      const baseHeight = 40;
      const newHeight = baseHeight + animatedOffset.value;
      return `M531,333 l52,0 0,${newHeight} -52,0 0,-${newHeight}z`;
    });

    // 冲头主体路径 - 向下移动140px
    const punchHeadMainPath = computed(() => {
      const baseY = 373;
      const newY = baseY + animatedOffset.value;
      return `M479,${newY} l0,168 146,0 0,-84 -20,-20 0,-64 -126,0z`;
    });

    // 冲头条纹路径1
    const punchHeadStripePath1 = computed(() => {
      const baseY = 519;
      const newY = baseY + animatedOffset.value;
      return `M479,${newY} l146,0 0,22 -146,0 0,-22z`;
    });

    // 冲头阴影路径
    const punchHeadShadowPath = computed(() => {
      const baseY = 373;
      const newY = baseY + animatedOffset.value;
      return `M479,${newY} l0,94 126,-42 0,-52 -126,0z`;
    });

    // 冲头条纹路径2
    const punchHeadStripePath2 = computed(() => {
      const baseY = 499;
      const newY = baseY + animatedOffset.value;
      return `M479,${newY} l146,0 0,20 -146,0 0,-20z`;
    });

    // 冲头轮廓路径
    const punchHeadOutlinePath = computed(() => {
      const baseY = 373;
      const newY = baseY + animatedOffset.value;
      return `M479,${newY} l0,168 146,0 0,-84 -20,-20 0,-64 -126,0`;
    });

    // 冲头高光路径1
    const punchHeadHighlightPath1 = computed(() => {
      const baseY = 519;
      const newY = baseY + animatedOffset.value;
      return `M485,${newY} l134,0`;
    });

    // 冲头高光路径2
    const punchHeadHighlightPath2 = computed(() => {
      const baseY = 499;
      const newY = baseY + animatedOffset.value;
      return `M485,${newY} l134,0`;
    });

    // 动画函数
    const animateToTarget = () => {
      if (tl) {
        tl.kill();
      }

      if (props.animate) {
        tl = gsap.timeline();
        tl.to(animatedOffset, {
          value: targetOffset.value,
          duration: 0.8,
          ease: 'power2.inOut',
        });
      } else {
        animatedOffset.value = targetOffset.value;
      }
    };

    // 监听状态变化
    watch(() => state.state, animateToTarget, { immediate: false });

    // 监听animate prop变化
    watch(
      () => state.animate,
      () => {
        if (!state.animate) {
          if (tl) {
            tl.kill();
          }
          animatedOffset.value = targetOffset.value;
        }
      },
    );

    // 组件挂载时初始化
    onMounted(() => {
      animatedOffset.value = targetOffset.value;
    });

    return {
      colors,
      linkPath,
      punchHeadMainPath,
      punchHeadStripePath1,
      punchHeadShadowPath,
      punchHeadStripePath2,
      punchHeadOutlinePath,
      punchHeadHighlightPath1,
      punchHeadHighlightPath2,
    };
  },
});
</script>

<style scoped></style>
