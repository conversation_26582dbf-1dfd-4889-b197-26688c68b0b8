<template>
  <div class="storage-box-container">
    <!-- 标题显示 -->
    <div
      v-if="showTitle"
      class="storage-box-title"
      :style="{
        color: titleColor,
        fontSize: titleFontSize + 'px',
      }"
    >
      {{ title }}
    </div>

    <!-- Pixi.js 画布容器 -->
    <div
      ref="pixiContainer"
      class="pixi-canvas-container"
      :style="{
        width: width + 'px',
        height: height + 'px',
        position: 'relative',
      }"
    ></div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  onMounted,
  onUnmounted,
  watch,
  nextTick,
  PropType,
  toRefs,
} from 'vue';
import * as PIXI from 'pixi.js';
import { useComponentState } from '@/communication/hooks/useComponentState';
import { createComponentProps } from '@/context/ComponentContext';

// 格子物品信息接口
export interface GridItem {
  /** 物品名称 */
  name?: string;
  /** 物品图片路径 */
  imagePath?: string;
  /** 物品状态：empty-空置, occupied-有物品, disabled-禁用 */
  status: string;
}

// 收纳盒规格接口
export interface BoxSize {
  /** 行数（高度方向格子数） */
  rows: number;
  /** 列数（宽度方向格子数） */
  columns: number;
}

// 斜二测投影坐标转换
interface ObliquePoint {
  x: number;
  y: number;
  z: number;
}

interface ScreenPoint {
  x: number;
  y: number;
}

// 2.5D斜二测收纳盒渲染器
class ObliqueStorageBoxRenderer {
  private app: PIXI.Application;
  private container: PIXI.Container;
  private boxContainer: PIXI.Container;
  private gridContainer: PIXI.Container;
  private itemContainer: PIXI.Container;
  private uiContainer: PIXI.Container;

  private readonly BOX_HEIGHT = 20; // 盒子高度
  private readonly OBLIQUE_ANGLE = Math.PI / 4; // 45度角
  private readonly DEPTH_SCALE = 0.5; // 深度缩放系数
  private gridSize: number = 40; // 格子大小，动态计算

  constructor(containerElement: HTMLElement, width: number, height: number) {
    // 创建Pixi应用
    this.app = new PIXI.Application({
      width,
      height,
      backgroundColor: 0xf3f000,
      backgroundAlpha: 0,
      antialias: true,
      resolution: window.devicePixelRatio || 1,
      autoDensity: true,
    });

    containerElement.appendChild(this.app.view as HTMLCanvasElement);

    // 创建容器层次
    this.container = new PIXI.Container();
    this.boxContainer = new PIXI.Container();
    this.gridContainer = new PIXI.Container();
    this.itemContainer = new PIXI.Container();
    this.uiContainer = new PIXI.Container();

    // 设置容器层次
    this.container.addChild(this.boxContainer);
    this.container.addChild(this.gridContainer);
    this.container.addChild(this.itemContainer);
    this.container.addChild(this.uiContainer);
    this.app.stage.addChild(this.container);

    // 初始化时暂时设置位置，稍后会在render时重新计算居中
    this.container.x = 0;
    this.container.y = 0;
  }

  // 计算动态格子大小
  private calculateGridSize(
    width: number,
    height: number,
    rows: number,
    columns: number,
  ): number {
    // 预留边距，使用80%的可用空间
    const availableWidth = width * 0.98;
    const availableHeight = height * 0.95;

    // 基于宽度和列数计算（考虑斜二测投影的深度影响）
    const sizeByWidth = availableWidth / (columns + rows * this.DEPTH_SCALE);
    // 基于高度和行数计算
    const sizeByHeight = availableHeight / (rows * 0.96);

    // 取较小值确保不超出边界
    return Math.min(sizeByWidth, sizeByHeight, 60); // 调整最大尺寸为30px
  }

  // 斜二测投影坐标转换
  private obliqueToScreen(point: ObliquePoint): ScreenPoint {
    return {
      x: point.x + point.z * Math.cos(this.OBLIQUE_ANGLE) * this.DEPTH_SCALE,
      y: point.y - point.z * Math.sin(this.OBLIQUE_ANGLE) * this.DEPTH_SCALE,
    };
  }

  // 渲染盒子结构
  private renderBox(rows: number, columns: number): void {
    this.boxContainer.removeChildren();

    const boxWidth = columns * this.gridSize;
    const boxDepth = rows * this.gridSize;
    const boxColor = '#f5f5f5';

    // 盒子右侧壁
    const rightWall = new PIXI.Graphics();
    rightWall.beginFill(this.darkenColor(boxColor, 0.2));

    const rightPoints = [
      this.obliqueToScreen({ x: boxWidth, y: 0, z: 0 }),
      this.obliqueToScreen({ x: boxWidth, y: this.BOX_HEIGHT, z: 0 }),
      this.obliqueToScreen({ x: boxWidth, y: this.BOX_HEIGHT, z: boxDepth }),
      this.obliqueToScreen({ x: boxWidth, y: 0, z: boxDepth }),
    ];

    rightWall.moveTo(rightPoints[0].x, rightPoints[0].y);
    rightPoints.slice(1).forEach((point) => rightWall.lineTo(point.x, point.y));
    rightWall.closePath();
    rightWall.endFill();

    // 添加右侧壁边框
    rightWall.lineStyle(1, 0x666666, 0.5);
    rightWall.moveTo(rightPoints[0].x, rightPoints[0].y);
    rightPoints.slice(1).forEach((point) => rightWall.lineTo(point.x, point.y));
    rightWall.closePath();

    this.boxContainer.addChild(rightWall);

    // 盒子左侧壁
    const leftWall = new PIXI.Graphics();
    leftWall.beginFill(this.darkenColor(boxColor, 0.15));

    const leftPoints = [
      this.obliqueToScreen({ x: 0, y: 0, z: 0 }),
      this.obliqueToScreen({ x: 0, y: this.BOX_HEIGHT, z: 0 }),
      this.obliqueToScreen({ x: 0, y: this.BOX_HEIGHT, z: boxDepth }),
      this.obliqueToScreen({ x: 0, y: 0, z: boxDepth }),
    ];

    leftWall.moveTo(leftPoints[0].x, leftPoints[0].y);
    leftPoints.slice(1).forEach((point) => leftWall.lineTo(point.x, point.y));
    leftWall.closePath();
    leftWall.endFill();

    // 添加左侧壁边框
    leftWall.lineStyle(1, 0x666666, 0.5);
    leftWall.moveTo(leftPoints[0].x, leftPoints[0].y);
    leftPoints.slice(1).forEach((point) => leftWall.lineTo(point.x, point.y));
    leftWall.closePath();

    // this.boxContainer.addChild(leftWall);

    // 盒子后壁（最后渲染）
    const backWall = new PIXI.Graphics();
    backWall.beginFill(this.darkenColor(boxColor, 0.1));

    const backPoints = [
      this.obliqueToScreen({ x: 0, y: 0, z: boxDepth }),
      this.obliqueToScreen({ x: boxWidth, y: 0, z: boxDepth }),
      this.obliqueToScreen({ x: boxWidth, y: this.BOX_HEIGHT, z: boxDepth }),
      this.obliqueToScreen({ x: 0, y: this.BOX_HEIGHT, z: boxDepth }),
    ];

    backWall.moveTo(backPoints[0].x, backPoints[0].y);
    backPoints.slice(1).forEach((point) => backWall.lineTo(point.x, point.y));
    backWall.closePath();
    backWall.endFill();

    // 添加后壁边框
    backWall.lineStyle(1, 0x666666, 0.5);
    backWall.moveTo(backPoints[0].x, backPoints[0].y);
    backPoints.slice(1).forEach((point) => backWall.lineTo(point.x, point.y));
    backWall.closePath();

    this.boxContainer.addChild(backWall);

    // 盒子前壁
    const frontWall = new PIXI.Graphics();
    frontWall.beginFill(this.darkenColor(boxColor, 0.3));

    const frontPoints = [
      this.obliqueToScreen({ x: 0, y: 0, z: 0 }),
      this.obliqueToScreen({ x: boxWidth, y: 0, z: 0 }),
      this.obliqueToScreen({ x: boxWidth, y: this.BOX_HEIGHT, z: 0 }),
      this.obliqueToScreen({ x: 0, y: this.BOX_HEIGHT, z: 0 }),
    ];

    frontWall.moveTo(frontPoints[0].x, frontPoints[0].y);
    frontPoints.slice(1).forEach((point) => frontWall.lineTo(point.x, point.y));
    frontWall.closePath();
    frontWall.endFill();

    // 添加前壁边框
    frontWall.lineStyle(1, 0x666666, 0.5);
    frontWall.moveTo(frontPoints[0].x, frontPoints[0].y);
    frontPoints.slice(1).forEach((point) => frontWall.lineTo(point.x, point.y));
    frontWall.closePath();

    this.boxContainer.addChild(frontWall);
  }

  // 渲染网格和格子
  private renderGrid(
    rows: number,
    columns: number,
    items: GridItem[],
    showGridNumbers: boolean,
    statusToColor: Record<string, string>,
    itemStartPosition: string,
    sortDirection: string,
  ): void {
    this.gridContainer.removeChildren();
    this.itemContainer.removeChildren();
    this.uiContainer.removeChildren(); // 清除UI容器，包括格子编号

    // 首先渲染所有空格子
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < columns; col++) {
        // 格子底面坐标
        const x = col * this.gridSize;
        const z = row * this.gridSize;

        // 绘制空格子底面
        this.renderGridCell(
          x,
          z,
          this.gridSize,
          { status: 'empty' },
          statusToColor,
        );

        // 绘制格子编号（如果需要）
        if (showGridNumbers) {
          const gridNumber = this.calculateGridNumber(
            row,
            col,
            itemStartPosition,
            sortDirection,
            rows,
            columns,
          );
          this.renderGridNumber(x, z, this.gridSize, gridNumber);
        }
      }
    }

    // 然后根据起始位置渲染物品
    items.forEach((item, index) => {
      if (!item || !item.status || item.status === 'empty') {
        return;
      }

      // 根据起始位置配置和数组索引计算行列位置
      const { row, column } = this.calculateItemPosition(
        index,
        itemStartPosition,
        sortDirection,
        rows,
        columns,
      );

      // 验证位置合理性
      if (!this.validateItemPosition(row, column, rows, columns)) {
        console.warn(
          `物品 "${item.name}" 的位置超出收纳盒范围: index=${index}, row=${row}, column=${column}`,
        );
        return;
      }

      // 格子底面坐标
      const x = column * this.gridSize;
      const z = row * this.gridSize;

      // 重新绘制该格子（覆盖空格子）
      this.renderGridCell(x, z, this.gridSize, item, statusToColor);
    });
  }

  // 渲染单个格子
  private renderGridCell(
    x: number,
    z: number,
    size: number,
    item: GridItem,
    statusToColor: Record<string, string>,
  ): void {
    const cellGraphics = new PIXI.Graphics();

    // 格子底面
    const bottomColor = this.getGridCellColor(item);
    cellGraphics.beginFill(bottomColor);

    const cellPoints = [
      this.obliqueToScreen({ x, y: 0, z }),
      this.obliqueToScreen({ x: x + size, y: 0, z }),
      this.obliqueToScreen({ x: x + size, y: 0, z: z + size }),
      this.obliqueToScreen({ x, y: 0, z: z + size }),
    ];

    cellGraphics.moveTo(cellPoints[0].x, cellPoints[0].y);
    cellPoints
      .slice(1)
      .forEach((point) => cellGraphics.lineTo(point.x, point.y));
    cellGraphics.closePath();
    cellGraphics.endFill();

    // 格子边框
    cellGraphics.lineStyle(1, 0x999999, 0.7);
    cellGraphics.moveTo(cellPoints[0].x, cellPoints[0].y);
    cellPoints
      .slice(1)
      .forEach((point) => cellGraphics.lineTo(point.x, point.y));
    cellGraphics.closePath();

    // 如果有物品，渲染物品
    this.renderFlatItem(x, z, size, item, statusToColor);

    this.gridContainer.addChild(cellGraphics);
  }

  // 渲染扁平的物品（替代阴影和悬浮效果）
  private renderFlatItem(
    x: number,
    z: number,
    size: number,
    item: GridItem,
    statusToColor: Record<string, string>,
  ): void {
    if (!item || !item.status || item.status === 'empty') {
      return;
    }

    const itemSize = size * 0.9; // 物品尺寸略小于格子
    const offset = (size - itemSize) / 2; // 居中偏移量

    const itemGraphics = new PIXI.Graphics();

    // 使用状态对应的颜色，完全不透明
    const colorHex = statusToColor[item.status] || '#909399';
    const itemColor = parseInt(colorHex.replace('#', ''), 16);
    itemGraphics.beginFill(itemColor, 1); // alpha = 1

    const itemPoints = [
      this.obliqueToScreen({ x: x + offset, y: 1, z: z + offset }), // y=1，贴近底部
      this.obliqueToScreen({ x: x + offset + itemSize, y: 1, z: z + offset }),
      this.obliqueToScreen({
        x: x + offset + itemSize,
        y: 1,
        z: z + offset + itemSize,
      }),
      this.obliqueToScreen({ x: x + offset, y: 1, z: z + offset + itemSize }),
    ];

    itemGraphics.moveTo(itemPoints[0].x, itemPoints[0].y);
    itemPoints
      .slice(1)
      .forEach((point) => itemGraphics.lineTo(point.x, point.y));
    itemGraphics.closePath();
    itemGraphics.endFill();

    this.itemContainer.addChild(itemGraphics);
  }

  // 渲染格子编号
  private renderGridNumber(
    x: number,
    z: number,
    size: number,
    index: number,
  ): void {
    const center = this.obliqueToScreen({
      x: x + size / 2,
      y: 1,
      z: z + size / 2,
    });

    const numberText = new PIXI.Text(index.toString(), {
      fontFamily: 'Arial',
      fontSize: 12,
      fill: 0x666666,
      align: 'center',
    });

    numberText.anchor.set(0.5);
    numberText.x = center.x;
    numberText.y = center.y;

    this.uiContainer.addChild(numberText);
  }

  // 获取格子颜色
  private getGridCellColor(item: GridItem): number {
    switch (item.status) {
      case 'occupied':
        return 0xe8f4f8;
      case 'disabled':
        return 0xf0f0f0;
      default:
        return 0xfafafa;
    }
  }

  // 颜色变暗工具函数
  private darkenColor(color: string, factor: number): number {
    const hex = color.replace('#', '');
    const r = Math.max(
      0,
      Math.floor(parseInt(hex.substring(0, 2), 16) * (1 - factor)),
    );
    const g = Math.max(
      0,
      Math.floor(parseInt(hex.substring(2, 4), 16) * (1 - factor)),
    );
    const b = Math.max(
      0,
      Math.floor(parseInt(hex.substring(4, 6), 16) * (1 - factor)),
    );
    return (r << 16) | (g << 8) | b;
  }

  // 根据起始位置、排列方向和索引计算行列位置
  private calculateItemPosition(
    index: number,
    startPosition: string,
    sortDirection: string,
    rows: number,
    columns: number,
  ): { row: number; column: number } {
    if (sortDirection === 'vertical') {
      // 竖向排列（按列排序）
      switch (startPosition) {
        case 'top-left':
          // 从左上开始，从上到下、从左到右
          return {
            row: rows - 1 - (index % rows),
            column: Math.floor(index / rows),
          };

        case 'top-right':
          // 从右上开始，从上到下、从右到左
          return {
            row: rows - 1 - (index % rows),
            column: columns - 1 - Math.floor(index / rows),
          };

        case 'bottom-left':
          // 从左下开始，从下到上、从左到右
          return {
            row: index % rows,
            column: Math.floor(index / rows),
          };

        case 'bottom-right':
          // 从右下开始，从下到上、从右到左
          return {
            row: index % rows,
            column: columns - 1 - Math.floor(index / rows),
          };

        default:
          // 默认使用左上角模式
          return {
            row: rows - 1 - (index % rows),
            column: Math.floor(index / rows),
          };
      }
    } else {
      // 横向排列（按行排序）- 原有逻辑
      switch (startPosition) {
        case 'top-left':
          // 从左上开始，从左到右、从上到下
          return {
            row: rows - 1 - Math.floor(index / columns),
            column: index % columns,
          };

        case 'top-right':
          // 从右上开始，从右到左、从上到下
          return {
            row: rows - 1 - Math.floor(index / columns),
            column: columns - 1 - (index % columns),
          };

        case 'bottom-left':
          // 从左下开始，从左到右、从下到上
          return {
            row: Math.floor(index / columns),
            column: index % columns,
          };

        case 'bottom-right':
          // 从右下开始，从右到左、从下到上
          return {
            row: Math.floor(index / columns),
            column: columns - 1 - (index % columns),
          };

        default:
          // 默认使用左上角模式
          return {
            row: rows - 1 - Math.floor(index / columns),
            column: index % columns,
          };
      }
    }
  }

  // 验证物品位置的合理性
  private validateItemPosition(
    row: number,
    column: number,
    rows: number,
    columns: number,
  ): boolean {
    return row >= 0 && row < rows && column >= 0 && column < columns;
  }

  // 根据起始位置、排列方向和行列位置计算格子编号
  private calculateGridNumber(
    row: number,
    column: number,
    startPosition: string,
    sortDirection: string,
    rows: number,
    columns: number,
  ): number {
    if (sortDirection === 'vertical') {
      // 竖向排列的编号计算
      switch (startPosition) {
        case 'top-left':
          // 从左上开始，从上到下、从左到右编号
          return rows - 1 - row + column * rows + 1;

        case 'top-right':
          // 从右上开始，从上到下、从右到左编号
          return rows - 1 - row + (columns - 1 - column) * rows + 1;

        case 'bottom-left':
          // 从左下开始，从下到上、从左到右编号
          return row + column * rows + 1;

        case 'bottom-right':
          // 从右下开始，从下到上、从右到左编号
          return row + (columns - 1 - column) * rows + 1;

        default:
          // 默认使用左上角模式
          return rows - 1 - row + column * rows + 1;
      }
    } else {
      // 横向排列的编号计算（原有逻辑）
      switch (startPosition) {
        case 'top-left':
          // 从左上开始，从左到右、从上到下编号
          return (rows - 1 - row) * columns + column + 1;

        case 'top-right':
          // 从右上开始，从右到左、从上到下编号
          return (rows - 1 - row) * columns + (columns - 1 - column) + 1;

        case 'bottom-left':
          // 从左下开始，从左到右、从下到上编号
          return row * columns + column + 1;

        case 'bottom-right':
          // 从右下开始，从右到左、从下到上编号
          return row * columns + (columns - 1 - column) + 1;

        default:
          // 默认使用左上角模式
          return (rows - 1 - row) * columns + column + 1;
      }
    }
  }

  // 渲染整个场景
  public render(
    rows: number,
    columns: number,
    items: GridItem[],
    showGridNumbers: boolean,
    canvasWidth: number,
    canvasHeight: number,
    statusToColor: Record<string, string>,
    itemStartPosition: string,
    sortDirection: string,
  ): void {
    // 计算动态格子大小
    this.gridSize = this.calculateGridSize(
      canvasWidth,
      canvasHeight,
      rows,
      columns,
    );

    // 计算并设置居中位置
    const centerPosition = this.calculateCenterPosition(
      canvasWidth,
      canvasHeight,
      rows,
      columns,
    );
    this.container.x = centerPosition.x;
    this.container.y = centerPosition.y;

    this.renderBox(rows, columns);
    this.renderGrid(
      rows,
      columns,
      items,
      showGridNumbers,
      statusToColor,
      itemStartPosition,
      sortDirection,
    );
  }

  // 计算储藏盒的实际占用尺寸
  private calculateBoxBounds(
    rows: number,
    columns: number,
  ): { minX: number; minY: number; maxX: number; maxY: number } {
    const boxWidth = columns * this.gridSize;
    const boxDepth = rows * this.gridSize;

    // 定义盒子的8个3D顶点
    const vertices: ObliquePoint[] = [
      { x: 0, y: 0, z: 0 },
      { x: boxWidth, y: 0, z: 0 },
      { x: 0, y: this.BOX_HEIGHT, z: 0 },
      { x: boxWidth, y: this.BOX_HEIGHT, z: 0 },
      { x: 0, y: 0, z: boxDepth },
      { x: boxWidth, y: 0, z: boxDepth },
      { x: 0, y: this.BOX_HEIGHT, z: boxDepth },
      { x: boxWidth, y: this.BOX_HEIGHT, z: boxDepth },
    ];

    // 将它们全部投影到2D屏幕
    const projectedVertices = vertices.map((v) => this.obliqueToScreen(v));

    // 找到最小和最大的x, y坐标
    const minX = Math.min(...projectedVertices.map((v) => v.x));
    const maxX = Math.max(...projectedVertices.map((v) => v.x));
    const minY = Math.min(...projectedVertices.map((v) => v.y));
    const maxY = Math.max(...projectedVertices.map((v) => v.y));

    return { minX, minY, maxX, maxY };
  }

  // 计算居中位置
  private calculateCenterPosition(
    canvasWidth: number,
    canvasHeight: number,
    rows: number,
    columns: number,
  ): { x: number; y: number } {
    const bounds = this.calculateBoxBounds(rows, columns);
    const boxScreenWidth = bounds.maxX - bounds.minX;
    const boxScreenHeight = bounds.maxY - bounds.minY;

    return {
      x: (canvasWidth - boxScreenWidth) / 2 - bounds.minX,
      y: (canvasHeight - boxScreenHeight) / 2 - bounds.minY,
    };
  }

  // 更新画布大小
  public resize(width: number, height: number): void {
    this.app.renderer.resize(width, height);
    // 居中位置会在render时重新计算
  }

  // 销毁渲染器
  public destroy(): void {
    this.app.destroy(true, {
      children: true,
      texture: true,
      baseTexture: true,
    });
  }
}

const StorageBoxProps = {
  // === 盒体基础属性 ===
  /** 收纳盒标题 */
  title: {
    type: String,
    default: '收纳盒',
  },
  /** 是否显示标题 */
  showTitle: {
    type: Boolean,
    default: true,
  },
  /** 盒体颜色 */
  boxColor: {
    type: String,
    default: '#f5f5f5',
  },
  /** 盒体宽度（像素） */
  width: {
    type: Number,
    default: 400,
  },
  /** 盒体高度（像素） */
  height: {
    type: Number,
    default: 300,
  },

  // === 格子规格配置 ===
  /** 收纳盒行数（高度方向格子数） */
  rows: {
    type: Number,
    default: 4,
  },
  /** 收纳盒列数（宽度方向格子数） */
  columns: {
    type: Number,
    default: 6,
  },

  // === 格子物品信息 ===
  /** 各个格子的物品信息数组 */
  items: {
    type: Array as PropType<GridItem[]>,
    default: () => [],
  },
  /** 状态到颜色的映射 */
  statusToColor: {
    type: Object as PropType<Record<string, string>>,
    default: () => ({
      Idled: '#909399',
      Processing: '#409EFF',
      Completed: '#67C23A',
      Aborted: '#E6A23C',
      Failed: '#DC3545',
      Returning: '#9370DB',
      Returned: '#B0C4DE',
      CompleteReturning: '#48D1CC',
    }),
  },

  // === 显示配置 ===
  /** 是否显示物品名称 */
  showItemNames: {
    type: Boolean,
    default: true,
  },
  /** 是否显示格子编号 */
  showGridNumbers: {
    type: Boolean,
    default: false,
  },

  // === 样式配置 ===
  /** 标题颜色 */
  titleColor: {
    type: String,
    default: '#333333',
  },
  /** 标题字体大小 */
  titleFontSize: {
    type: Number,
    default: 16,
  },
  /** 物品名称颜色 */
  itemNameColor: {
    type: String,
    default: '#666666',
  },
  /** 物品排列的起始位置 */
  itemStartPosition: {
    type: String,
    default: 'top-left',
    validator: (value: string) => {
      return ['top-left', 'top-right', 'bottom-left', 'bottom-right'].includes(
        value,
      );
    },
    description: '物品排列的起始位置：左上、右上、左下、右下',
  },
  /** 物品排列方向 */
  sortDirection: {
    type: String,
    default: 'horizontal',
    validator: (value: string) => {
      return ['horizontal', 'vertical'].includes(value);
    },
    description:
      '物品排列方向：horizontal-横向排列（按行），vertical-竖向排列（按列）',
  },
};

export default defineComponent({
  name: 'StorageBox',
  props: createComponentProps(StorageBoxProps),
  setup(props) {
    const { items } = toRefs(props);

    // 使用组件状态Hook（响应式工厂函数版本）
    const { state } = useComponentState(
      props.partId,
      () => ({
        items: items.value,
      }),
      props.dynamicPartInfo,
      props, // 传递组件props，用于MQTT自定义解析函数
    );

    const pixiContainer = ref<HTMLElement>();
    let renderer: ObliqueStorageBoxRenderer | null = null;

    // 初始化渲染器
    const initRenderer = async () => {
      if (!pixiContainer.value) return;

      renderer = new ObliqueStorageBoxRenderer(
        pixiContainer.value,
        props.width,
        props.height,
      );

      // 初始渲染
      updateRenderer();
    };

    // 更新渲染器
    const updateRenderer = () => {
      if (!renderer) return;

      renderer.render(
        props.rows,
        props.columns,
        state.items,
        props.showGridNumbers,
        props.width,
        props.height,
        props.statusToColor,
        props.itemStartPosition,
        props.sortDirection,
      );
    };

    // 监听属性变化
    watch(
      () => [
        props.rows,
        props.columns,
        state.items,
        props.showGridNumbers,
        props.itemStartPosition,
        props.sortDirection,
      ],
      () => {
        updateRenderer();
      },
      { deep: true },
    );

    watch(
      () => [props.width, props.height],
      () => {
        if (renderer) {
          renderer.resize(props.width, props.height);
          updateRenderer();
        }
      },
    );

    // 组件挂载
    onMounted(async () => {
      await nextTick();
      initRenderer();
    });

    // 组件卸载
    onUnmounted(() => {
      if (renderer) {
        renderer.destroy();
        renderer = null;
      }
    });

    return {
      pixiContainer,
    };
  },
});
</script>

<style scoped>
.storage-box-container {
  display: inline-block;
  user-select: none;
}

.storage-box-container {
  position: relative;
}

.storage-box-title {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  text-align: 'center';
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.pixi-canvas-container {
  overflow: hidden;
}

.pixi-canvas-container canvas {
  display: block;
}
</style>
