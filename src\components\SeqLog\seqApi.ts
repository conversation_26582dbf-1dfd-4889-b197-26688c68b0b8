// Seq 属性接口
interface SeqProperty {
  Name: string;
  Value: any;
}

// Seq 消息模板令牌接口
interface MessageTemplateToken {
  Text: string;
}

// Seq 链接接口
interface SeqLinks {
  Self: string;
  Group: string;
}

// Seq 日志事件接口
interface SeqEvent {
  Id: string;
  Timestamp: string;
  Level: string;
  Properties: SeqProperty[];
  MessageTemplateTokens: MessageTemplateToken[];
  EventType: string;
  TraceId?: string;
  SpanId?: string;
  SpanKind?: string;
  Links: SeqLinks;
  Exception?: string;
}

// Seq 统计信息接口
interface SeqStatistics {
  TotalMatches: number;
  PageSize: number;
  PageNumber: number;
}

// Seq API 响应接口
interface SeqEventsResponse {
  Events: SeqEvent[];
  Statistics: SeqStatistics;
}

// HTTP 请求结果接口
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  status: number;
  error?: string;
}

// 查询参数接口
interface QueryEventsParams {
  filter?: string;
  count?: number;
  start?: string;
  end?: string;
}

// 内部查询参数接口
interface InternalQueryParams {
  count: number;
  filter?: string;
  fromDateUtc?: string;
  toDateUtc?: string;
}

// 查询结果接口
interface QueryEventsResult {
  success: boolean;
  data: SeqEvent[];
  events: SeqEvent[];
  statistics?: SeqStatistics;
  error?: string;
}

// 连接检查结果接口
interface ConnectionCheckResult {
  success: boolean;
  connected: boolean;
  message: string;
}

// HTTP 请求选项接口
interface RequestOptions extends Omit<RequestInit, 'method' | 'headers'> {
  headers?: Record<string, string>;
}

class SeqService {
  private readonly baseUrl: string;
  private readonly apiKey: string | null;
  private readonly defaultHeaders: Record<string, string>;

  constructor(baseUrl: string, apiKey: string | null = null) {
    this.baseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
    this.apiKey = apiKey;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...(apiKey && { 'X-Seq-ApiKey': apiKey }),
    };
  }

  /**
   * 创建 HTTP 请求
   * @param endpoint - API 端点
   * @param options - 请求选项
   * @returns 请求结果
   */
  private async makeRequest<T = any>(
    endpoint: string,
    options: RequestOptions = {},
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          ...this.defaultHeaders,
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(
          `HTTP 错误! 状态: ${response.status} - ${response.statusText}`,
        );
      }

      const data: T = await response.json();
      return {
        success: true,
        data,
        status: response.status,
      };
    } catch (error) {
      console.error('请求失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      return {
        success: false,
        error: errorMessage,
        status: 0,
      };
    }
  }

  /**
   * 构建查询参数字符串
   * @param params - 参数对象
   * @returns 查询参数字符串
   */
  private buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        searchParams.append(key, String(value));
      }
    });

    return searchParams.toString();
  }

  /**
   * 查询日志事件
   * @param params - 查询参数
   * @returns 查询结果
   */
  async queryEvents(
    params: QueryEventsParams = {},
  ): Promise<QueryEventsResult> {
    try {
      console.log('查询 params', params);
      const queryParams: InternalQueryParams = {
        count: params.count || 100,
        ...(params.filter && { filter: params.filter }),
        ...(params.start && { fromDateUtc: params.start }),
        ...(params.end && { toDateUtc: params.end }),
      };

      console.log('queryParams', queryParams);

      const queryString = this.buildQueryString(queryParams);
      const endpoint = `/api/events${queryString ? `?${queryString}` : ''}`;

      console.log('SEQ API调用:', endpoint, queryParams);

      const result = await this.makeRequest<SeqEvent[]>(endpoint);

      if (result.success && result.data) {
        return {
          success: true,
          data: result.data,
          events: result.data,
          statistics: undefined,
        };
      } else {
        return {
          success: false,
          error: result.error,
          data: [],
          events: [],
        };
      }
    } catch (error) {
      console.error('查询 Seq 日志失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      return {
        success: false,
        error: errorMessage,
        data: [],
        events: [],
      };
    }
  }

  /**
   * 检查 Seq 服务连接状态
   */
  async checkConnection(): Promise<ConnectionCheckResult> {
    try {
      const result = await this.makeRequest<SeqEvent[]>('/api/events?count=1');
      return {
        success: result.success,
        connected: result.success,
        message: result.success ? '连接成功' : `连接失败: ${result.error}`,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      return {
        success: false,
        connected: false,
        message: `连接失败: ${errorMessage}`,
      };
    }
  }
}

export default SeqService;

// 导出类型定义供其他模块使用
export type {
  SeqEvent,
  SeqProperty,
  MessageTemplateToken,
  SeqLinks,
  SeqStatistics,
  SeqEventsResponse,
  QueryEventsParams,
  QueryEventsResult,
  ConnectionCheckResult,
  ApiResponse,
};
