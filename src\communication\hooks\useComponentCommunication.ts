/**
 * 组件通信Hook
 * 为Vue组件提供简单易用的通信接口
 */
import { onMounted, onUnmounted, ref } from 'vue';
import { v4 as uuidv4 } from 'uuid';
import {
  ComponentCommunicationManager,
  ComponentMessageHandler,
} from '../manager/ComponentCommunicationManager';
import { DynamicPartConfig, DynamicPartInfo, MqttInfo } from '@/types';

/**
 * 组件通信Hook
 *
 * @param partId 部件ID，用于标识订阅的具体实例
 * @param messageHandler 消息处理函数，接收解析后的消息
 * @returns 通信相关的状态和方法
 */
export function useComponentCommunication(
  partId: string | undefined,
  dynamicPartInfo: DynamicPartInfo | undefined,
  messageHandler: (data: any, messagePartId?: string) => void,
) {
  // 组件实例ID，用于在管理器中标识此组件
  const componentId = uuidv4();

  // 通信状态
  const isConnected = ref(false);
  const lastMessage = ref<any>(null);
  const lastError = ref<Error | null>(null);

  // 获取通信管理器实例
  const manager = ComponentCommunicationManager.getInstance();

  // 检查通信适配器连接状态
  const checkConnectionStatus = () => {
    const adapter = manager.getAdapter();
    isConnected.value = adapter ? adapter.isConnected() : false;
  };

  // 创建主partId的消息处理器实现
  const mainHandler: ComponentMessageHandler = {
    handleMessage(message: any) {
      try {
        // 更新最后接收的消息
        lastMessage.value = message;

        // 调用用户提供的处理函数，传递主partId
        messageHandler(message, partId);
      } catch (error) {
        // 记录错误
        lastError.value =
          error instanceof Error ? error : new Error(String(error));
        console.error('处理主组件消息时出错:', error);
      }
    },
  };

  // 创建动态部件的消息处理器工厂
  const createDynamicHandler = (
    targetPartId: string,
  ): ComponentMessageHandler => {
    return {
      handleMessage(message: any) {
        try {
          // 更新最后接收的消息
          lastMessage.value = message;

          // 调用用户提供的处理函数，传递动态部件的partId
          messageHandler(message, targetPartId);
        } catch (error) {
          // 记录错误
          lastError.value =
            error instanceof Error ? error : new Error(String(error));
          console.error(`处理动态部件 ${targetPartId} 消息时出错:`, error);
        }
      },
    };
  };

  const registerPartInfoSubscriptions = (config: DynamicPartConfig) => {
    const [targetPartId, partAttributeName] = config.attributeName.split('.');

    if (!targetPartId || !partAttributeName) {
      console.warn(`无效的attributeName格式: ${config.attributeName}`);
      return;
    }

    // 为每个动态部件创建独立的消息处理器
    const dynamicHandler = createDynamicHandler(targetPartId);

    manager.registerComponent(
      `${componentId}-${targetPartId}`,
      targetPartId,
      dynamicHandler,
    );
  };

  // 注册 MQTT 订阅
  const registerMqttSubscriptions = (
    componentProp: string,
    config: MqttInfo,
  ) => {
    console.log('注册 MQTT 订阅:', componentProp, config);

    // 检查是否为多主题配置（逗号分隔）
    const topics = config.topic
      .split(',')
      .map((t) => t.trim())
      .filter((t) => t.length > 0);

    if (topics.length === 1) {
      // 单主题订阅（保持原有逻辑）
      const mqttHandler: ComponentMessageHandler = {
        handleMessage(message: any) {
          try {
            // 更新最后接收的消息
            lastMessage.value = message;

            // 调用用户提供的处理函数，传递特殊标识表示这是 MQTT 消息
            // 使用 componentProp 作为标识，这样在 useComponentState 中可以识别
            messageHandler(message, `mqtt:${componentProp}`);
          } catch (error) {
            // 记录错误
            lastError.value =
              error instanceof Error ? error : new Error(String(error));
            console.error(`处理 MQTT 消息时出错:`, error);
          }
        },
      };

      // 注册 MQTT 组件订阅
      manager.registerMqttComponent(
        `${componentId}-mqtt-${componentProp}`,
        config,
        mqttHandler,
      );
    } else {
      // 多主题订阅
      console.log(`检测到多主题配置，共 ${topics.length} 个主题:`, topics);

      topics.forEach((topic, index) => {
        // 为每个主题创建独立的配置
        const topicConfig: MqttInfo = {
          ...config,
          topic: topic, // 使用单个主题
        };

        // 创建包含主题索引的消息处理器
        const mqttHandler: ComponentMessageHandler = {
          handleMessage(message: any) {
            try {
              // 更新最后接收的消息
              lastMessage.value = message;

              // 调用用户提供的处理函数，传递包含主题索引的标识
              // 格式：mqtt:componentProp:topicIndex
              messageHandler(message, `mqtt:${componentProp}:${index}`);
            } catch (error) {
              // 记录错误
              lastError.value =
                error instanceof Error ? error : new Error(String(error));
              console.error(`处理 MQTT 消息时出错 (主题 ${index}):`, error);
            }
          },
        };

        // 注册每个主题的 MQTT 组件订阅
        manager.registerMqttComponent(
          `${componentId}-mqtt-${componentProp}-${index}`,
          topicConfig,
          mqttHandler,
        );
      });
    }
  };

  // 注册动态部件订阅
  const registerDynamicSubscriptions = () => {
    if (!dynamicPartInfo) return;

    Object.entries(dynamicPartInfo).forEach(([componentProp, config]) => {
      const configType = config.type;

      if (configType === 'partInfo') {
        registerPartInfoSubscriptions(config);
      } else if (configType === 'mqtt') {
        // 处理 MQTT 配置
        registerMqttSubscriptions(componentProp, config);
      }
    });
  };

  // 发送消息到当前组件类型的特定实例
  const sendMessage = (targetPartId: string, message: any) => {
    manager.publishToComponent(targetPartId, message);
  };

  onMounted(() => {
    // 只有当partId存在时才注册
    if (partId) {
      // 注册组件订阅
      console.log('注册主组件订阅:', partId);
      manager.registerComponent(componentId, partId, mainHandler);
    }

    registerDynamicSubscriptions();

    // 检查连接状态
    checkConnectionStatus();
  });

  onUnmounted(() => {
    // 注销组件订阅
    manager.unregisterComponent(componentId);
  });

  return {
    isConnected,
    lastMessage,
    lastError,
    sendMessage,
  };
}
