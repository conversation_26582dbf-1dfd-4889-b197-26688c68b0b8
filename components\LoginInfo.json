{"id": 1, "version": "0.0.17", "name": {"zh_CN": "用户登录信息"}, "component": "LoginInfo", "icon": "userinfo", "description": "可配置的用户登录信息组件，支持头像、用户名、用户组和下拉菜单", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "LoginInfo", "version": "0.0.17", "script": "http://*************:4873/@dcp/component-library@0.0.17/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "group": "DCP", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["username", "userGroup", "showAvatar", "avatarSrc", "menuItems"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}}, "schema": {"properties": [{"label": {"zh_CN": "基础属性"}, "description": {"zh_CN": "用户登录信息的基础配置属性"}, "content": [{"property": "username", "label": {"text": {"zh_CN": "用户名"}}, "description": {"zh_CN": "显示的用户名"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "User", "widget": {"component": "InputConfigurator", "props": {"placeholder": "请输入用户名"}}}, {"property": "userGroup", "label": {"text": {"zh_CN": "用户组"}}, "description": {"zh_CN": "显示的用户组"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "Default Group", "widget": {"component": "InputConfigurator", "props": {"placeholder": "请输入用户组"}}}, {"property": "showAvatar", "label": {"text": {"zh_CN": "显示头像"}}, "description": {"zh_CN": "是否显示头像"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": true, "widget": {"component": "SwitchConfigurator"}}, {"property": "avatarSrc", "label": {"text": {"zh_CN": "头像地址"}}, "description": {"zh_CN": "头像图片的URL地址，为空时显示用户名首字母"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "", "widget": {"component": "InputConfigurator", "props": {"placeholder": "请输入头像URL地址"}}}, {"property": "menuItems", "label": {"text": {"zh_CN": "菜单项"}}, "description": {"zh_CN": "自定义菜单项列表，每项包含label和可选的icon"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "object", "defaultValue": [], "widget": {"component": "CodeConfigurator", "props": {"language": "json", "height": 150}}}]}, {"label": {"zh_CN": "高级属性"}, "description": {"zh_CN": "用户登录信息的高级配置属性"}, "content": [{"property": "showLogout", "label": {"text": {"zh_CN": "显示退出登录"}}, "description": {"zh_CN": "是否显示退出登录按钮"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": true, "widget": {"component": "SwitchConfigurator"}}, {"property": "logoutLabel", "label": {"text": {"zh_CN": "退出登录文本"}}, "description": {"zh_CN": "退出登录按钮的显示文本"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "Logout", "widget": {"component": "InputConfigurator", "props": {"placeholder": "请输入退出登录文本"}}}, {"property": "showLanguageSwitch", "label": {"text": {"zh_CN": "显示语言切换"}}, "description": {"zh_CN": "是否显示语言切换按钮"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": true, "widget": {"component": "SwitchConfigurator"}}, {"property": "languageSwitchLabel", "label": {"text": {"zh_CN": "语言切换文本"}}, "description": {"zh_CN": "语言切换按钮的显示文本"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "Switch Language", "widget": {"component": "InputConfigurator", "props": {"placeholder": "请输入语言切换文本"}}}, {"property": "dropdownIcon", "label": {"text": {"zh_CN": "下拉图标"}}, "description": {"zh_CN": "下拉菜单的图标名称，使用Iconify图标"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "mdi:chevron-down", "widget": {"component": "InputConfigurator", "props": {"placeholder": "请输入图标名称"}}}]}], "events": {"onMenuItemClick": {"label": {"zh_CN": "菜单项点击"}, "description": {"zh_CN": "当点击自定义菜单项时触发的事件"}, "type": "event", "functionInfo": {"params": [{"name": "item", "type": "object", "description": "被点击的菜单项"}], "returns": {}}, "defaultValue": ""}, "onLogout": {"label": {"zh_CN": "退出登录"}, "description": {"zh_CN": "当点击退出登录按钮时触发的事件"}, "type": "event", "functionInfo": {"params": [], "returns": {}}, "defaultValue": ""}, "onLanguageSwitch": {"label": {"zh_CN": "切换语言"}, "description": {"zh_CN": "当点击切换语言按钮时触发的事件"}, "type": "event", "functionInfo": {"params": [], "returns": {}}, "defaultValue": ""}}}, "snippets": [{"name": {"zh_CN": "登录信息"}, "icon": "userinfo", "screenshot": "", "snippetName": "LoginInfo", "schema": {"props": {"username": "Admin", "userGroup": "Equipment Dept", "showAvatar": true, "avatarSrc": "", "menuItems": [], "showLogout": true, "logoutLabel": "Logout", "showLanguageSwitch": true, "languageSwitchLabel": "Switch Language", "dropdownIcon": "mdi:chevron-down"}}}], "category": "DCP"}