import { ref, onMounted, watch, Ref } from 'vue';
import * as PIXI from 'pixi.js';
import { MaterialConfig, MaterialInput } from '../index.vue';
import { DEFAULT_MATERIAL_SIZES, DEFAULT_MATERIAL_COLORS } from '../config';

// 标准化物料配置：将字符串转换为完整的MaterialConfig对象
function normalizeMaterialConfig(input: MaterialInput): MaterialConfig | null {
  if (!input) return null;

  if (typeof input === 'string') {
    // 字符串转换为标准对象，使用默认配置
    const materialType = input as MaterialConfig['type'];
    return {
      type: materialType,
      color: DEFAULT_MATERIAL_COLORS[materialType],
      size: DEFAULT_MATERIAL_SIZES[materialType],
    };
  }

  // 已经是对象，直接返回
  return input;
}

export const useMaterial = (
  materialConfig: Ref<MaterialInput> | MaterialInput,
) => {
  const materialContainer = ref<PIXI.Container>();

  const drawMaterial = (input: MaterialInput) => {
    const config = normalizeMaterialConfig(input);
    if (!config || !materialContainer.value) return;

    // 清除之前的物料图形
    materialContainer.value.removeChildren();

    // 获取物料配置
    const { type, color, size, label } = config;
    const materialColor = color || DEFAULT_MATERIAL_COLORS[type];
    const materialSize = {
      width: size?.width || DEFAULT_MATERIAL_SIZES[type].width,
      height: size?.height || DEFAULT_MATERIAL_SIZES[type].height,
    };

    // 创建物料图形
    const materialGraphics = new PIXI.Graphics();

    switch (type) {
      case 'block':
        // 绘制木块 - 矩形
        materialGraphics.beginFill(materialColor);
        materialGraphics.drawRect(
          -materialSize.width / 2,
          -materialSize.height / 2,
          materialSize.width,
          materialSize.height,
        );
        materialGraphics.endFill();

        // 添加木纹效果 - 简单的线条
        materialGraphics.lineStyle(1, 0x654321, 0.5);
        for (let i = 0; i < 3; i++) {
          const y =
            -materialSize.height / 2 + (i + 1) * (materialSize.height / 4);
          materialGraphics.moveTo(-materialSize.width / 2, y);
          materialGraphics.lineTo(materialSize.width / 2, y);
        }
        break;

      case 'cube':
        // 绘制魔方 - 正方形带网格
        materialGraphics.beginFill(materialColor);
        materialGraphics.drawRect(
          -materialSize.width / 2,
          -materialSize.height / 2,
          materialSize.width,
          materialSize.height,
        );
        materialGraphics.endFill();

        // 添加网格线
        materialGraphics.lineStyle(1, 0x000000, 0.8);
        const gridSize = materialSize.width / 3;
        // 垂直线
        for (let i = 1; i < 3; i++) {
          const x = -materialSize.width / 2 + i * gridSize;
          materialGraphics.moveTo(x, -materialSize.height / 2);
          materialGraphics.lineTo(x, materialSize.height / 2);
        }
        // 水平线
        for (let i = 1; i < 3; i++) {
          const y = -materialSize.height / 2 + i * gridSize;
          materialGraphics.moveTo(-materialSize.width / 2, y);
          materialGraphics.lineTo(materialSize.width / 2, y);
        }
        break;

      case 'sphere':
        // 绘制球体 - 圆形
        materialGraphics.beginFill(materialColor);
        materialGraphics.drawCircle(0, 0, materialSize.width / 2);
        materialGraphics.endFill();

        // 添加高光效果
        materialGraphics.beginFill(0xffffff, 0.3);
        materialGraphics.drawCircle(
          -materialSize.width / 6,
          -materialSize.height / 6,
          materialSize.width / 6,
        );
        materialGraphics.endFill();
        break;

      case 'cylinder':
        // 绘制圆柱体 - 椭圆形
        materialGraphics.beginFill(materialColor);
        materialGraphics.drawEllipse(
          0,
          0,
          materialSize.width / 2,
          materialSize.height / 2,
        );
        materialGraphics.endFill();

        // 添加渐变效果 - 简单的阴影线
        materialGraphics.lineStyle(2, 0x000000, 0.2);
        materialGraphics.drawEllipse(
          0,
          0,
          materialSize.width / 2 - 2,
          materialSize.height / 2 - 2,
        );
        break;

      case 'tray':
        // 绘制托盘 - 矩形带边框和分隔线
        materialGraphics.beginFill(materialColor);
        materialGraphics.drawRect(
          -materialSize.width / 2,
          -materialSize.height / 2,
          materialSize.width,
          materialSize.height,
        );
        materialGraphics.endFill();

        // 添加边框
        materialGraphics.lineStyle(1, 0x999999, 0.8);
        materialGraphics.drawRect(
          -materialSize.width / 2,
          -materialSize.height / 2,
          materialSize.width,
          materialSize.height,
        );

        // 添加内部分隔线（2x2格子）
        materialGraphics.lineStyle(1, 0xcccccc, 0.6);
        // 垂直分隔线
        materialGraphics.moveTo(0, -materialSize.height / 2);
        materialGraphics.lineTo(0, materialSize.height / 2);
        // 水平分隔线
        materialGraphics.moveTo(-materialSize.width / 2, 0);
        materialGraphics.lineTo(materialSize.width / 2, 0);
        break;
    }

    // 添加物料标签（如果有）
    if (label) {
      const labelText = new PIXI.Text(label, {
        fontSize: 8,
        fill: 0x000000,
        align: 'center',
      });
      labelText.anchor.set(0.5);
      labelText.position.set(0, materialSize.height / 2 + 8);
      materialContainer.value.addChild(labelText);
    }

    // 将物料图形添加到容器
    materialContainer.value.addChild(materialGraphics);

    // 设置物料位置 - 相对于抓手的抓取点
    materialContainer.value.position.set(0, -10); // 稍微向上偏移，模拟被抓取的状态
  };

  const clearMaterial = () => {
    if (materialContainer.value) {
      materialContainer.value.removeChildren();
    }
  };

  onMounted(() => {
    materialContainer.value = new PIXI.Container();

    // 处理初始物料配置
    const initialConfig =
      materialConfig &&
      typeof materialConfig === 'object' &&
      'value' in materialConfig
        ? materialConfig.value
        : (materialConfig as MaterialInput);

    if (initialConfig) {
      drawMaterial(initialConfig);
    }
  });

  // 监听物料配置变化
  if (
    materialConfig &&
    typeof materialConfig === 'object' &&
    'value' in materialConfig
  ) {
    // 响应式引用
    watch(
      materialConfig,
      (newConfig) => {
        if (newConfig) {
          drawMaterial(newConfig);
        } else {
          clearMaterial();
        }
      },
      { deep: true },
    );
  }

  return {
    materialContainer,
    drawMaterial,
    clearMaterial,
  };
};
