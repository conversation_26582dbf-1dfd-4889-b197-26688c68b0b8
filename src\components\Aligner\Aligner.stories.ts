import type { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import <PERSON><PERSON><PERSON> from './index.vue';

const meta = {
  title: 'Equipment/Aligner',
  component: Aligner,
  tags: ['autodocs'],
  argTypes: {
    rotate: {
      control: 'number',
      description: '旋转角度',
    },
    scale: {
      control: 'number',
      description: '整体缩放比例',
    },
    waferState: {
      control: 'boolean',
      description: '晶圆是否显示',
    },
    waferColor: {
      control: 'color',
      description: '晶圆颜色',
    },
    waferName: {
      control: 'text',
      description: '晶圆名称',
    },
    waferSize: {
      control: 'number',
      description: '晶圆大小',
    },
    waferShape: {
      control: 'select',
      options: ['circle', 'square'],
      description: '晶圆形状',
    },
    alignerPic: {
      control: 'text',
      description: '自定义对位机图片路径',
    },
    showOcr: {
      control: 'boolean',
      description: '是否显示OCR信息',
    },
    ocrIo: {
      control: 'text',
      description: 'OCR识别信息',
    },
  },
  args: {
    rotate: 0,
    scale: 1,
    waferState: false,
    waferColor: '#000000',
    waferName: 'A01',
    waferSize: 60,
    waferShape: 'circle',
    alignerPic: '',
    showOcr: false,
    ocrIo: '',
  },
  parameters: {
    docs: {
      description: {
        component:
          'Aligner组件用于显示对位机，支持晶圆的显示/隐藏、旋转、缩放等功能，并提供平滑的动画效果。',
      },
    },
  },
} satisfies Meta<typeof Aligner>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  name: '基础示例',
  render: (args) => ({
    components: { Aligner },
    setup() {
      return { args };
    },
    template: `
      <div style="width: 200px; height: 110px;">
        <Aligner v-bind="args" />
      </div>
    `,
  }),
};

// 晶圆动画示例
export const WaferAnimation: Story = {
  name: '晶圆展示',
  render: (args) => ({
    components: { Aligner },
    setup() {
      const waferState = ref(false);

      const toggleWafer = () => {
        waferState.value = !waferState.value;
      };

      return { args, waferState, toggleWafer };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="width: 200px; height: 110px;">
          <Aligner
            v-bind="args"
            :wafer-state="waferState"
          />
        </div>
        <button 
          @click="toggleWafer"
          style="padding: 8px 16px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;"
        >
          {{ waferState ? '隐藏晶圆' : '显示晶圆' }}
        </button>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: '这个示例展示了晶圆的淡入淡出动画效果。',
      },
    },
  },
};

// 晶圆形状示例
export const WaferShapes: Story = {
  name: '晶圆形状',
  render: (args) => ({
    components: { Aligner },
    setup() {
      const shapes = [
        { name: '圆形', value: 'circle' },
        { name: '方形', value: 'square' },
      ];

      return { args, shapes };
    },
    template: `
      <div style="display: flex; gap: 20px; flex-wrap: wrap;">
        <div v-for="shape in shapes" :key="shape.name" style="text-align: center;">
          <div style="width: 200px; height: 110px;">
            <Aligner
              v-bind="args"
              :wafer-shape="shape.value"
              :wafer-state="true"
            />
          </div>
          <div style="margin-top: 10px;">{{ shape.name }}</div>
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: '这个示例展示了不同形状的晶圆效果。',
      },
    },
  },
};

// OCR信息示例
export const WithOcr: Story = {
  name: 'OCR信息',
  render: (args) => ({
    components: { Aligner },
    setup() {
      const ocrStates = [
        { name: '无OCR', showOcr: false, ocrIo: '' },
        { name: '有OCR', showOcr: true, ocrIo: 'O-12' },
      ];

      return { args, ocrStates };
    },
    template: `
      <div style="display: flex; gap: 20px; flex-wrap: wrap;">
        <div v-for="state in ocrStates" :key="state.name" style="text-align: center;">
          <div style="width: 200px; height: 110px;">
            <Aligner
              v-bind="args"
              :wafer-state="true"
              :show-ocr="state.showOcr"
              :ocr-io="state.ocrIo"
            />
          </div>
          <div style="margin-top: 10px;">{{ state.name }}</div>
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: '这个示例展示了晶圆OCR信息的显示效果。',
      },
    },
  },
};

// 缩放示例
export const Scaling: Story = {
  name: '缩放控制',
  render: (args) => ({
    components: { Aligner },
    setup() {
      const scale = ref(1);
      const waferSize = ref(60);
      const waferState = ref(true);

      return { args, scale, waferSize, waferState };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="width: 200px; height: 110px;">
          <Aligner
            v-bind="args"
            :scale="scale"
            :wafer-size="waferSize"
            :wafer-state="waferState"
          />
        </div>
        <div>
          <label>整体缩放: {{ scale.toFixed(1) }}</label>
          <input 
            type="range" 
            v-model.number="scale" 
            min="0.5" 
            max="2" 
            step="0.1" 
            style="width: 100%;" 
          />
        </div>
        <div>
          <label>晶圆大小: {{ waferSize }}</label>
          <input 
            type="range" 
            v-model.number="waferSize" 
            min="10" 
            max="100" 
            step="1" 
            style="width: 100%;" 
          />
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: '这个示例允许您分别控制对位机和晶圆的缩放比例。',
      },
    },
  },
};
