{"component": "PunchPress", "name": {"zh_CN": "桌面冲床"}, "icon": "PunchPress", "group": "DCP", "category": "DCP", "description": "", "tags": "", "keywords": "", "doc_url": "", "devMode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "PunchPress", "version": "0.0.48", "script": "http://*************:4874/@dcp/component-library@0.0.48/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "configure": {"shortcuts": {"properties": ["state", "color", "animate", "width"]}}, "schema": {"properties": [{"name": "0", "label": {"zh_CN": "基础属性"}, "description": {"zh_CN": "组件的核心功能配置"}, "content": [{"property": "state", "label": {"text": {"zh_CN": "冲头状态"}}, "description": {"zh_CN": "冲头的当前状态，可选择收起或展开"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "collapse", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "收起", "value": "collapse"}, {"label": "工作", "value": "expand"}]}}}, {"property": "animate", "label": {"text": {"zh_CN": "动画效果"}}, "description": {"zh_CN": "是否启用状态切换时的动画过渡效果"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator", "props": {}}}]}, {"name": "1", "label": {"zh_CN": "样式属性"}, "description": {"zh_CN": "组件的外观和尺寸配置"}, "content": [{"property": "width", "label": {"text": {"zh_CN": "宽度"}}, "description": {"zh_CN": "SVG图形的宽度，单位为像素"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 64, "widget": {"component": "NumberConfigurator", "props": {"min": 50, "max": 2000, "step": 10}}}, {"property": "height", "label": {"text": {"zh_CN": "高度"}}, "description": {"zh_CN": "SVG图形的高度，单位为像素"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 64, "widget": {"component": "NumberConfigurator", "props": {"min": 50, "max": 2000, "step": 10}}}, {"property": "color", "label": {"text": {"zh_CN": "主色调"}}, "description": {"zh_CN": "组件的基础颜色，系统将自动生成对应的色系方案"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#000", "widget": {"component": "ColorConfigurator", "props": {}}}]}], "events": {}, "slots": {}}, "snippets": [{"name": {"zh_CN": "桌面冲床"}, "icon": "PunchPress", "snippetName": "PunchPress", "schema": {"props": {"width": 200, "height": 240, "color": "#4a90e2", "state": "collapse", "animate": true}}}]}