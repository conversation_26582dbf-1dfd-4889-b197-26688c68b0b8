<template>
  <div class="content" :style="stateStyle">
    <div class="no">{{ no }}</div>
    <div class="cds-name">{{ name }}</div>
    <div class="status">{{ tankState }}</div>
  </div>
</template>

<script lang="ts">
import { computed, toRefs, defineComponent } from 'vue';

export default defineComponent({
  name: 'CdsState',
  props: {
    width: {
      type: Number,
      default: 320,
    },
    height: {
      type: Number,
      default: 30,
    },
    name: {
      type: String,
      required: true,
    },
    no: {
      type: String,
      required: true,
    },
    tankState: {
      type: String,
      required: true,
    },
    backgroundColor: {
      type: String,
      default: '#ffffff',
    },
    textColor: {
      type: String,
      default: '#131313',
    },
  },

  setup(props: {
    name: string;
    width: number;
    height: number;
    no: string;
    backgroundColor: string;
    textColor: string;
    tankState: string;
  }) {
    const { width, height, backgroundColor, textColor } = toRefs(props);

    const stateStyle = computed(() => {
      return {
        width: width.value + 'px',
        height: height.value + 'px',
        backgroundColor: backgroundColor.value,
        color: textColor.value,
      };
    });

    return {
      stateStyle,
    };
  },
});
</script>

<style lang="scss" scoped>
.content {
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  border-radius: 5px;
  .no {
    min-width: 70px;
    max-width: 70px;
  }
  .cds-name {
    flex: 1;
    min-width: 140px;
  }
  .status {
    min-width: 80px;
    max-width: 80px;
  }
}
</style>
