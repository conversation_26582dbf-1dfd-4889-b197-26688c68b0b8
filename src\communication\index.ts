/**
 * 组件通信系统入口文件
 * 导出所有公共API
 */

// 通信适配器接口
export type {
  ICommunicationAdapter,
  ICommunicationAdapterFactory,
} from './adapter/CommunicationAdapter';

// 组件通信管理器
export type {
  ComponentCommunicationManager,
  ComponentMessageHandler,
} from './manager/ComponentCommunicationManager';

// 组件通信Hook
export { useComponentCommunication } from './hooks/useComponentCommunication';

// 组件状态管理
export { ComponentStateManager } from './state/ComponentStateManager';
export { useComponentState } from './hooks/useComponentState';

// 组件库对外接口
export { ComponentLibrary } from './ComponentLibrary';
