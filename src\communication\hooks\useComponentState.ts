/**
 * 组件状态Hook
 * 结合通信系统和状态管理，提供响应式的组件状态
 */
import { onUnmounted, computed, watchEffect } from 'vue';
import { v4 as uuidv4 } from 'uuid';
import { ComponentStateManager } from '../state/ComponentStateManager';
import { useComponentCommunication } from './useComponentCommunication';
import { ComponentLibrary } from '../ComponentLibrary';
import { parseMqttMessage } from '../utils/messageParser';
import { DynamicPartConfig, DynamicPartInfo, MqttInfo } from '@/types';

// ✅ 已实现：MQTT 多主题订阅功能
// - 支持逗号分隔的多个主题订阅
// - 按主题顺序更新数组对应位置的元素
// - 自动处理数组扩展和null填充
/**
 * 组件状态Hook（响应式工厂函数版本）
 *
 * @param partId 部件ID，用于订阅对应的消息
 * @param stateFactory 状态工厂函数，自动响应式追踪依赖
 * @param dynamicPartInfo 单独零件属性信息，用于动态连接策略
 * @param componentProps 组件props（用于MQTT自定义解析函数）
 * @param customMessageMapper 可选的自定义消息映射函数，优先级高于全局转换器
 * @returns 响应式状态和通信相关方法
 */
export function useComponentState<T extends object>(
  partId: string | undefined,
  stateFactory: () => T,
  dynamicPartInfo?: DynamicPartInfo,
  componentProps?: Record<string, any>,
  customMessageMapper?: (message: any) => Partial<T>,
) {
  // 生成组件实例ID
  const componentId = uuidv4();

  // 获取状态管理器
  const stateManager = ComponentStateManager.getInstance();

  // 使用 computed 自动追踪依赖
  const computedState = computed(stateFactory);

  // 初始化组件状态
  const { state, updateState } = stateManager.initComponentState<T>(
    componentId,
    computedState.value,
  );

  // 自动同步计算属性的变化
  watchEffect(() => {
    updateState(computedState.value);
  });

  const handlePartInfoConfig = (data: {
    config: DynamicPartConfig;
    componentProp: string;
    message: any;
    messagePartId?: string;
    stateUpdate: Partial<T>;
  }) => {
    const { config, componentProp, message, messagePartId, stateUpdate } = data;
    // 解析 attributeName，格式为 "部件ID.属性名"
    const [targetPartId, attributeName] = config.attributeName.split('.');

    // 检查当前消息是否来自目标部件
    if (targetPartId === messagePartId) {
      // 使用 ComponentLibrary.transformMessage 转换消息
      const transformedMessage = ComponentLibrary.transformMessage(
        message,
        targetPartId,
      );

      // 从转换后的消息中提取指定的属性
      if (
        transformedMessage &&
        typeof transformedMessage === 'object' &&
        attributeName in transformedMessage
      ) {
        const attributeValue = transformedMessage[attributeName];
        // 将属性值设置到组件状态中
        stateUpdate[componentProp as keyof T] = attributeValue as T[keyof T];
      } else {
        console.warn(
          `在转换后的消息中未找到属性: ${attributeName}`,
          transformedMessage,
        );
      }
    }
  };

  const handleMqttConfig = (data: {
    config: MqttInfo;
    componentProp: string;
    message: any;
    messagePartId?: string;
    stateUpdate: Partial<T>;
  }) => {
    const { config, componentProp, message, messagePartId, stateUpdate } = data;

    // 检查是否是对应的 MQTT 消息
    // 支持两种格式：
    // 1. 单主题：mqtt:componentProp
    // 2. 多主题：mqtt:componentProp:topicIndex
    if (messagePartId?.startsWith(`mqtt:${componentProp}`)) {
      // 解析 messagePartId 获取主题索引（如果存在）
      const parts = messagePartId.split(':');
      const topicIndex = parts.length === 3 ? parseInt(parts[2], 10) : -1;
      const isMultiTopic = topicIndex >= 0;

      // 过滤组件props，排除dynamicPartInfo
      const filteredProps = componentProps ? { ...componentProps } : {};
      if ('dynamicPartInfo' in filteredProps) {
        delete filteredProps.dynamicPartInfo;
      }

      // 使用消息解析工具解析 MQTT 消息，传递组件props和索引信息
      const parsedValue = parseMqttMessage(
        message,
        config,
        filteredProps,
        isMultiTopic ? topicIndex : undefined,
      );

      if (parsedValue !== undefined) {
        if (isMultiTopic) {
          // 多主题场景：更新数组中对应索引的元素
          const currentValue =
            stateUpdate[componentProp as keyof T] ||
            (computedState.value as any)[componentProp] ||
            [];

          // 确保数组有足够的长度
          const newArray = Array.isArray(currentValue) ? [...currentValue] : [];
          while (newArray.length <= topicIndex) {
            newArray.push(null); // 用null填充空位
          }

          // 更新指定索引的值
          newArray[topicIndex] = parsedValue;

          // 将更新后的数组设置到组件状态中
          stateUpdate[componentProp as keyof T] = newArray as T[keyof T];
        } else {
          // 单主题场景：直接设置值（保持原有逻辑）
          stateUpdate[componentProp as keyof T] = parsedValue as T[keyof T];
        }
      } else {
        console.warn(`MQTT 消息解析失败: ${componentProp}`, message, config);
      }
    }
  };

  // 定义消息处理函数
  const processMessage = (message: any, messagePartId?: string) => {
    try {
      let stateUpdate: Partial<T> = {};

      // 收集在dynamicPartInfo中声明的属性名，这些属性将被排除在全局订阅处理之外
      const dynamicProps: string[] = [];
      if (dynamicPartInfo) {
        dynamicProps.push(...Object.keys(dynamicPartInfo));
      }

      // 优先使用自定义映射函数（如果提供）
      if (customMessageMapper) {
        stateUpdate = customMessageMapper(message);

        // * 按理来说，自定义的级别应该是最高的，可以不需要移除动态属性声明
        // 如果有动态属性声明，从自定义映射结果中移除这些属性
        if (dynamicProps.length > 0) {
          dynamicProps.forEach((prop) => {
            if (prop in stateUpdate) {
              delete stateUpdate[prop as keyof T];
            }
          });
        }
      } else if (partId && (!messagePartId || messagePartId === partId)) {
        // 处理来自主partId的消息，否则使用全局注册的消息转换器
        const fullUpdate = ComponentLibrary.transformMessage(
          message,
          partId,
        ) as Partial<T>;

        // 过滤掉在dynamicPartInfo中已声明的属性
        for (const key in fullUpdate) {
          if (!dynamicProps.includes(key)) {
            stateUpdate[key as keyof T] = fullUpdate[key as keyof T];
          }
        }
      }

      // 处理动态部件信息
      if (dynamicPartInfo && messagePartId) {
        // 遍历所有动态部件属性
        Object.entries(dynamicPartInfo).forEach(([componentProp, config]) => {
          if (config.type === 'partInfo') {
            handlePartInfoConfig({
              config,
              componentProp,
              message,
              messagePartId,
              stateUpdate,
            });
          } else if (config.type === 'mqtt') {
            handleMqttConfig({
              config,
              componentProp,
              message,
              messagePartId,
              stateUpdate,
            });
          }
        });
      }

      // 更新状态
      if (stateUpdate && Object.keys(stateUpdate).length > 0) {
        updateState(stateUpdate);
      }
    } catch (error) {
      console.error('处理组件消息时出错:', error);
    }
  };

  const { isConnected, lastMessage, sendMessage } = useComponentCommunication(
    partId,
    dynamicPartInfo,
    processMessage,
  );

  // 清理函数
  onUnmounted(() => {
    stateManager.cleanupComponentState(componentId);
  });

  return {
    // 返回响应式状态，确保类型正确
    state: state as any as T,

    // 手动更新状态的方法
    updateState,

    // 通信相关状态和方法
    isConnected,
    lastMessage,
    sendMessage,

    // 组件ID，用于调试
    componentId,
  };
}
