<template>
  <div class="test-container">
    <h2>StorageBox 排列方向测试</h2>
    
    <div class="controls">
      <label>
        排列方向：
        <select v-model="sortDirection">
          <option value="horizontal">横向排列（按行）</option>
          <option value="vertical">竖向排列（按列）</option>
        </select>
      </label>
      
      <label>
        起始位置：
        <select v-model="itemStartPosition">
          <option value="top-left">左上角</option>
          <option value="top-right">右上角</option>
          <option value="bottom-left">左下角</option>
          <option value="bottom-right">右下角</option>
        </select>
      </label>
      
      <label>
        <input type="checkbox" v-model="showGridNumbers" />
        显示格子编号
      </label>
    </div>

    <div class="storage-boxes">
      <div class="box-section">
        <h3>当前配置：{{ sortDirection === 'horizontal' ? '横向排列' : '竖向排列' }} - {{ getPositionText(itemStartPosition) }}</h3>
        <StorageBox
          :rows="4"
          :columns="6"
          :width="400"
          :height="300"
          :items="testItems"
          :showGridNumbers="showGridNumbers"
          :itemStartPosition="itemStartPosition"
          :sortDirection="sortDirection"
          title="测试储藏盒"
        />
      </div>
    </div>

    <div class="item-list">
      <h3>物品列表（按数组索引顺序）：</h3>
      <div class="items">
        <div 
          v-for="(item, index) in testItems" 
          :key="index"
          class="item-info"
          :style="{ backgroundColor: statusToColor[item.status] || '#f0f0f0' }"
        >
          <span class="item-index">{{ index + 1 }}</span>
          <span class="item-name">{{ item.name }}</span>
          <span class="item-status">{{ item.status }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import StorageBox from './index.vue';

export default defineComponent({
  name: 'TestSortDirection',
  components: {
    StorageBox,
  },
  setup() {
    const sortDirection = ref('horizontal');
    const itemStartPosition = ref('top-left');
    const showGridNumbers = ref(true);

    const statusToColor = {
      'Idled': '#909399',
      'Processing': '#409EFF',
      'Completed': '#67C23A',
      'Aborted': '#E6A23C',
      'Failed': '#DC3545',
      'Returning': '#9370DB',
      'Returned': '#B0C4DE',
      'CompleteReturning': '#48D1CC',
    };

    // 创建测试物品数据
    const testItems = ref([
      { name: '物品1', status: 'Processing' },
      { name: '物品2', status: 'Completed' },
      { name: '物品3', status: 'Idled' },
      { name: '物品4', status: 'Failed' },
      { name: '物品5', status: 'Aborted' },
      { name: '物品6', status: 'Returning' },
      { name: '物品7', status: 'Returned' },
      { name: '物品8', status: 'CompleteReturning' },
      { name: '物品9', status: 'Processing' },
      { name: '物品10', status: 'Completed' },
      { name: '物品11', status: 'Idled' },
      { name: '物品12', status: 'Failed' },
    ]);

    const getPositionText = (position: string) => {
      const positionMap = {
        'top-left': '左上角',
        'top-right': '右上角',
        'bottom-left': '左下角',
        'bottom-right': '右下角',
      };
      return positionMap[position] || position;
    };

    return {
      sortDirection,
      itemStartPosition,
      showGridNumbers,
      testItems,
      statusToColor,
      getPositionText,
    };
  },
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.controls {
  margin-bottom: 20px;
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.controls label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.controls select {
  padding: 4px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.storage-boxes {
  margin-bottom: 30px;
}

.box-section {
  margin-bottom: 30px;
}

.box-section h3 {
  margin-bottom: 10px;
  color: #333;
}

.item-list {
  margin-top: 30px;
}

.items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 8px;
  margin-top: 10px;
}

.item-info {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.item-index {
  font-weight: bold;
  background: rgba(255, 255, 255, 0.8);
  padding: 2px 6px;
  border-radius: 3px;
  min-width: 20px;
  text-align: center;
}

.item-name {
  flex: 1;
  font-weight: 500;
}

.item-status {
  font-size: 10px;
  opacity: 0.8;
}
</style>
