/**
 * 组件库对外接口
 * 提供宿主应用注册通信适配器和消息转换器的机制
 */
import { ICommunicationAdapter } from './adapter/CommunicationAdapter';
import { ComponentCommunicationManager } from './manager/ComponentCommunicationManager';
import {
  DefaultMessageTransformer,
  IMessageTransformer,
} from './adapter/MessageTransformer';

/**
 * 组件库类
 * 提供注册通信适配器和消息转换器的静态方法
 */
export class ComponentLibrary {
  // 消息转换器实例
  private static messageTransformer: IMessageTransformer =
    new DefaultMessageTransformer();

  /**
   * 注册通信适配器
   * 宿主应用通过此方法注册自己的通信适配器实现
   *
   * @param adapter 通信适配器实例
   */
  public static registerCommunicationAdapter(
    adapter: ICommunicationAdapter,
  ): void {
    const manager = ComponentCommunicationManager.getInstance();
    manager.setAdapter(adapter);
    console.info('通信适配器已注册到组件库');
  }

  /**
   * 注册消息转换器
   * 宿主应用通过此方法注册自己的消息转换逻辑
   *
   * @param transformer 消息转换器实例
   */
  public static registerMessageTransformer(
    transformer: IMessageTransformer,
  ): void {
    ComponentLibrary.messageTransformer = transformer;
    console.info('消息转换器已注册到组件库');
  }

  /**
   * 获取消息转换器
   * 组件库内部使用此方法获取当前的消息转换器
   *
   * @returns 消息转换器实例
   */
  public static getMessageTransformer(): IMessageTransformer {
    return ComponentLibrary.messageTransformer;
  }

  /**
   * 转换消息为状态更新
   * 使用注册的转换器将消息转换为状态更新
   *
   * @param message 接收到的消息
   * @param partId 部件ID
   * @returns 状态更新对象
   */
  public static transformMessage(
    message: any,
    partId: string,
  ): Record<string, any> {
    return ComponentLibrary.messageTransformer.transformMessage(
      message,
      partId,
    );
  }

  /**
   * 检查通信适配器是否已注册
   * @returns 是否已注册通信适配器
   */
  public static hasCommunicationAdapter(): boolean {
    const manager = ComponentCommunicationManager.getInstance();
    return manager.getAdapter() !== null;
  }

  /**
   * 获取通信适配器连接状态
   * @returns 通信适配器是否已连接
   */
  public static isCommunicationConnected(): boolean {
    const manager = ComponentCommunicationManager.getInstance();
    const adapter = manager.getAdapter();
    return adapter ? adapter.isConnected() : false;
  }
}
