# DCP 组件库接入指南

## 目录

1. [简介](#简介)
2. [安装与配置](#安装与配置)
3. [通信适配器](#通信适配器)
4. [消息转换器](#消息转换器)
5. [使用组件](#使用组件)
6. [最佳实践](#最佳实践)
7. [常见问题](#常见问题)

## 简介

DCP 组件库是一个基于 Vue 3 的组件库，提供了一系列可视化组件，如 SixAxisRobot（六轴机器人）等。这些组件支持通过通信系统（如 MQTT）接收消息并更新状态。本指南将详细说明宿主项目如何接入 DCP 组件库，包括如何注册通信适配器和消息转换器。

## 安装与配置

### 安装

```bash
# 使用npm安装
npm install @dcp/component-library

# 或使用yarn安装
yarn add @dcp/component-library
```

### 基本配置

在宿主项目的入口文件（如`main.ts`）中注册组件库：

```typescript
import { createApp } from 'vue';
import App from './App.vue';
import DCPComponentLibrary from '@dcp/component-library';

const app = createApp(App);
app.use(DCPComponentLibrary);
app.mount('#app');
```

## 通信适配器

通信适配器负责处理组件库与外部通信系统（如 MQTT）之间的交互。宿主项目需要实现并注册通信适配器。

### 实现 MQTT 适配器

```typescript
// src/adapters/MqttAdapter.ts
import { ICommunicationAdapter } from '@dcp/component-library';
import mqtt from 'mqtt';

export class MqttAdapter implements ICommunicationAdapter {
  private client: mqtt.MqttClient;
  private subscriptions: Map<
    string,
    Array<(message: any, topic: string) => void>
  > = new Map();
  private connected: boolean = false;

  constructor(brokerUrl: string, options?: mqtt.IClientOptions) {
    // 创建MQTT客户端
    this.client = mqtt.connect(brokerUrl, options);

    // 设置事件处理器
    this.client.on('connect', () => {
      console.log('MQTT已连接到', brokerUrl);
      this.connected = true;
    });

    this.client.on('disconnect', () => {
      console.log('MQTT已断开连接');
      this.connected = false;
    });

    this.client.on('error', (error) => {
      console.error('MQTT错误:', error);
    });

    // 处理接收到的消息
    this.client.on('message', (topic, message) => {
      const callbacks = this.subscriptions.get(topic);
      if (callbacks) {
        try {
          // 尝试解析JSON
          const messageStr = message.toString();
          const messageObj = JSON.parse(messageStr);
          callbacks.forEach((callback) => callback(messageObj, topic));
        } catch (e) {
          // 如果解析失败，传递原始字符串
          const messageStr = message.toString();
          callbacks.forEach((callback) => callback(messageStr, topic));
        }
      }
    });
  }

  /**
   * 订阅主题
   */
  subscribe(
    topic: string,
    callback: (message: any, topic: string) => void,
  ): () => void {
    if (!this.subscriptions.has(topic)) {
      this.subscriptions.set(topic, []);
      this.client.subscribe(topic);
    }

    const callbacks = this.subscriptions.get(topic)!;
    callbacks.push(callback);

    // 返回取消订阅的函数
    return () => this.unsubscribe(topic, callback);
  }

  /**
   * 取消订阅
   */
  private unsubscribe(
    topic: string,
    callback: (message: any, topic: string) => void,
  ) {
    const callbacks = this.subscriptions.get(topic);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index !== -1) {
        callbacks.splice(index, 1);
      }

      if (callbacks.length === 0) {
        this.subscriptions.delete(topic);
        this.client.unsubscribe(topic);
      }
    }
  }

  /**
   * 发布消息
   */
  publish(topic: string, message: any): void {
    const messageStr =
      typeof message === 'string' ? message : JSON.stringify(message);

    this.client.publish(topic, messageStr);
  }

  /**
   * 检查连接状态
   */
  isConnected(): boolean {
    return this.connected;
  }
}
```

### 注册通信适配器

在应用初始化时注册通信适配器：

```typescript
// src/main.ts
import { createApp } from 'vue';
import App from './App.vue';
import DCPComponentLibrary, { ComponentLibrary } from '@dcp/component-library';
import { MqttAdapter } from './adapters/MqttAdapter';

const app = createApp(App);
app.use(DCPComponentLibrary);

// 创建并注册MQTT适配器
const mqttAdapter = new MqttAdapter('mqtt://broker.example.com:1883', {
  username: 'user',
  password: 'password',
  clientId: `dcp-client-${Math.random().toString(16).substr(2, 8)}`,
});

// 注册到组件库
ComponentLibrary.registerCommunicationAdapter(mqttAdapter);

app.mount('#app');
```

## 消息转换器

消息转换器负责将接收到的消息转换为组件状态更新。宿主项目可以实现自定义的消息转换器，以适应特定的消息格式。

### 实现自定义消息转换器

```typescript
// src/adapters/CustomMessageTransformer.ts
import { IMessageTransformer } from '@dcp/component-library';

export class CustomMessageTransformer implements IMessageTransformer {
  /**
   * 转换消息为状态更新
   * @param message 接收到的消息
   * @param partId 部件ID
   */
  transformMessage(message: any, partId: string): Record<string, any> {
    // 示例：处理不同的消息格式
    if (message && typeof message === 'object') {
      // 示例1：标准格式 { props: { left: 100, top: 200, ... } }
      if (message.props) {
        return message.props;
      }

      // 示例2：自定义格式 { type: 'update', data: { left: 100, top: 200, ... } }
      if (message.type === 'update' && message.data) {
        return message.data;
      }

      // 示例3：设备数据格式 { deviceId: 'xxx', values: { position: 'position1', ... } }
      if (message.deviceId && message.deviceId === partId && message.values) {
        // 将设备数据格式转换为组件需要的格式
        const result: Record<string, any> = {};

        if (message.values.position) {
          result.currentPosition = message.values.position;
        }

        if (message.values.x !== undefined && message.values.y !== undefined) {
          result.left = message.values.x;
          result.top = message.values.y;
        }

        return result;
      }
    }

    // 如果无法识别消息格式，返回空对象
    return {};
  }
}
```

### 注册消息转换器

在应用初始化时注册消息转换器：

```typescript
// src/main.ts
import { ComponentLibrary } from '@dcp/component-library';
import { CustomMessageTransformer } from './adapters/CustomMessageTransformer';

// 创建并注册自定义消息转换器
const messageTransformer = new CustomMessageTransformer();
ComponentLibrary.registerMessageTransformer(messageTransformer);
```

## 使用组件

注册通信适配器和消息转换器后，就可以在宿主项目中使用组件库的组件了。

### 使用 SixAxisRobot 组件

```vue
<template>
  <div class="robot-container">
    <h2>六轴机器人演示</h2>
    <SixAxisRobot
      partId="JakaRobot"
      :left="100"
      :top="200"
      currentPosition="position1"
    />
  </div>
</template>

<script setup>
import { SixAxisRobot } from '@dcp/component-library';
</script>
```

### 发送消息更新组件

宿主项目可以通过 MQTT 客户端发送消息来更新组件状态：

```typescript
// 使用MQTT客户端发送消息
mqttClient.publish(
  'guiMessage/component/JakaRobot',
  JSON.stringify({
    props: {
      left: 150,
      top: 250,
      currentPosition: 'position2',
    },
  }),
);
```

## 最佳实践

### 1. 消息格式规范

建议使用以下标准消息格式：

```json
{
  "props": {
    "left": 100,
    "top": 200,
    "currentPosition": "position1",
    "otherProp": "value"
  }
}
```

### 2. 主题命名规范

建议使用以下主题命名规范：

```
guiMessage/component/{partId}
```

例如，对于 partId 为"JakaRobot"的组件，主题应为：

```
guiMessage/component/JakaRobot
```

### 3. 错误处理

在通信适配器和消息转换器中添加适当的错误处理，避免因消息格式错误导致组件无法正常工作：

```typescript
try {
  // 尝试解析消息
  const messageObj = JSON.parse(messageStr);
  // 处理消息...
} catch (error) {
  console.error('消息解析错误:', error);
  // 返回空对象或默认值
  return {};
}
```

### 4. 状态持久化

对于重要的组件状态，考虑在宿主应用中实现持久化，以便在页面刷新后恢复状态：

```typescript
// 保存状态到localStorage
function saveComponentState(partId: string, state: any) {
  localStorage.setItem(`component_${partId}`, JSON.stringify(state));
}

// 从localStorage恢复状态
function loadComponentState(partId: string): any {
  const savedState = localStorage.getItem(`component_${partId}`);
  return savedState ? JSON.parse(savedState) : null;
}
```

## 常见问题

### 1. 组件没有接收到消息

检查以下几点：

- 确认通信适配器已正确注册
- 确认 MQTT 连接状态
- 确认主题格式正确（`guiMessage/component/{partId}`）
- 检查消息格式是否符合预期

### 2. 组件状态没有更新

检查以下几点：

- 确认消息转换器已正确注册
- 检查消息转换器是否正确处理了消息
- 查看控制台是否有错误信息

### 3. 如何调试组件通信

可以在消息转换器中添加日志，查看接收到的消息和转换后的状态：

```typescript
transformMessage(message: any, partId: string): Record<string, any> {
  console.log(`收到消息(${partId}):`, message);

  // 处理消息...
  const result = { /* 转换后的状态 */ };

  console.log(`转换结果(${partId}):`, result);
  return result;
}
```

通过以上步骤，宿主项目可以轻松接入 DCP 组件库，实现组件的动态更新。如有更多问题，请参考组件库的 API 文档或联系技术支持。
