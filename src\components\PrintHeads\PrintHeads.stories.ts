import type { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import PrintHeads from './index.vue';

const meta = {
  title: 'Equipment/PrintHeads',
  component: PrintHeads,
  tags: ['autodocs'],
  argTypes: {
    type: {
      control: 'select',
      options: ['liquid', 'gas'],
      description: '打印头类型',
    },
    color: {
      control: 'color',
      description: '打印头颜色',
    },
    stateColor: {
      control: 'color',
      description: '状态指示器颜色',
    },
    rotate: {
      control: 'number',
      description: '旋转角度',
    },
  },
  args: {
    type: 'liquid',
    color: '#4d4d4d',
    stateColor: '#4d4d4d',
    rotate: 0,
  },
  parameters: {
    docs: {
      description: {
        component:
          'PrintHeads组件用于显示打印头，支持液体和气体两种类型，可以自定义颜色和旋转角度。',
      },
    },
  },
} satisfies Meta<typeof PrintHeads>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  name: '基础示例',
  args: {},
  render: (args) => ({
    components: { PrintHeads },
    setup() {
      return { args };
    },
    template: `
      <div style="width: 100px; height: 160px;">
        <PrintHeads v-bind="args" />
      </div>
    `,
  }),
};

// 气体类型示例
export const GasType: Story = {
  name: '气体类型',
  args: {
    type: 'gas',
    color: '#1677ff',
    stateColor: '#52c41a',
  },
  render: (args) => ({
    components: { PrintHeads },
    setup() {
      return { args };
    },
    template: `
      <div style="width: 100px; height: 160px;">
        <PrintHeads v-bind="args" />
      </div>
    `,
  }),
};

// 不同颜色示例
export const DifferentColors: Story = {
  name: '不同颜色',
  render: (args) => ({
    components: { PrintHeads },
    setup() {
      const colors = [
        { name: '默认', value: '#4d4d4d' },
        { name: '蓝色', value: '#1677ff' },
        { name: '绿色', value: '#52c41a' },
        { name: '红色', value: '#f5222d' },
        { name: '橙色', value: '#fa8c16' },
        { name: '紫色', value: '#722ed1' },
      ];

      return { args, colors };
    },
    template: `
      <div style="display: flex; gap: 20px; flex-wrap: wrap;">
        <div v-for="color in colors" :key="color.value" style="width: 100px; height: 160px; text-align: center;">
          <PrintHeads 
            v-bind="args" 
            :color="color.value"
            :stateColor="color.value"
          />
          <div style="margin-top: 10px;">{{ color.name }}</div>
        </div>
      </div>
    `,
  }),
};

// 旋转和缩放示例
export const RotateAndScale: Story = {
  name: '旋转和缩放',
  render: (args) => ({
    components: { PrintHeads },
    setup() {
      const rotate = ref(0);
      const scale = ref(1);

      return { args, rotate, scale };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="display: flex; gap: 20px; align-items: center;">
          <div style="width: 100px; height: 160px;">
            <PrintHeads 
              v-bind="args" 
              :rotate="rotate"
              :width="80"
              :height="160"
            />
          </div>
          <div style="flex: 1;">
            <div style="margin-bottom: 10px;">
              <label>旋转角度: {{ rotate }}°</label>
              <input type="range" v-model.number="rotate" min="0" max="360" style="width: 100%;" />
            </div>
          </div>
        </div>
      </div>
    `,
  }),
};
