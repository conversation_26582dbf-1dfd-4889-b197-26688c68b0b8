import type { Meta, StoryObj } from '@storybook/vue3';

import DcpStatus from './index';

const meta = {
  title: 'Common/DcpStatus',
  component: DcpStatus,
  // This component will have an automatically generated docsPage entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  argTypes: {
    status: { control: 'select', options: ['online', 'offline'] },
  },
  args: {
    status: 'online',
  },
} satisfies Meta<typeof DcpStatus>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const Offline: Story = {
  args: {
    status: 'offline',
  },
};
