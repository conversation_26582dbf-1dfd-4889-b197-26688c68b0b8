import type { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import Pump from './index.vue';

const meta = {
  title: 'Equipment/Pump',
  component: Pump,
  tags: ['autodocs'],
  argTypes: {
    color: {
      control: 'color',
      description: '泵体主体颜色',
    },
    rotorColor: {
      control: 'color',
      description: '转子颜色',
    },
    rotate: {
      control: 'number',
      description: '旋转角度',
    },
  },
  args: {
    color: '#e0e0e0',
    rotorColor: '#c0c0c0',
    rotate: 0,
  },
  parameters: {
    docs: {
      description: {
        component:
          'Pump组件用于显示真空泵的图形表示，支持自定义泵体颜色、转子颜色和旋转角度。',
      },
    },
  },
} satisfies Meta<typeof Pump>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  name: '基础示例',
  args: {},
};

// 不同颜色示例
export const DifferentColors: Story = {
  name: '不同颜色',
  render: (args) => ({
    components: { Pump },
    setup() {
      const colors = [
        { name: '默认', body: '#e0e0e0', rotor: '#c0c0c0' },
        { name: '蓝色', body: '#1677ff', rotor: '#4096ff' },
        { name: '绿色', body: '#52c41a', rotor: '#73d13d' },
        { name: '红色', body: '#f5222d', rotor: '#ff4d4f' },
      ];

      return { args, colors };
    },
    template: `
      <div style="display: flex; gap: 20px; flex-wrap: wrap;">
        <div v-for="color in colors" :key="color.name" style="text-align: center; width: 150px;">
          <Pump
            v-bind="args"
            :color="color.body"
            :rotorColor="color.rotor"
          />
          <div style="margin-top: 10px;">{{ color.name }}</div>
        </div>
      </div>
    `,
  }),
};

// 基础示例
export const Rotate: Story = {
  name: '旋转',
  args: {
    rotate: 90,
  },
  render: (args) => ({
    components: { Pump },
    setup() {
      return { args };
    },
    template: `
      <div style="width: 160px; height: 160px;">
        <Pump
          v-bind="args"
        />
      </div>
    `,
  }),
};
