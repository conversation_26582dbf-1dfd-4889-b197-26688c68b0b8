import type { Meta, StoryObj } from '@storybook/vue3';
import Shelf from './index.vue';

const meta: Meta<typeof Shelf> = {
  title: 'Equipment/Shelf',
  component: Shelf,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: '3D等轴测视角的货架组件，支持多行多列布局和物品状态展示',
      },
    },
  },
  argTypes: {
    width: {
      control: { type: 'range', min: 100, max: 400, step: 10 },
      description: '货架宽度',
    },
    height: {
      control: { type: 'range', min: 100, max: 300, step: 10 },
      description: '货架高度',
    },
    depth: {
      control: { type: 'range', min: 50, max: 200, step: 10 },
      description: '货架深度',
    },
    borderColor: {
      control: { type: 'color' },
      description: '边框颜色',
    },
    columns: {
      control: { type: 'range', min: 1, max: 8, step: 1 },
      description: '列数',
    },
    rows: {
      control: { type: 'range', min: 1, max: 6, step: 1 },
      description: '行数',
    },
    items: {
      control: { type: 'object' },
      description: '货架中的物品数组',
    },
    statusToColor: {
      control: { type: 'object' },
      description: '状态到颜色的映射',
    },
    showBackPanel: {
      control: { type: 'boolean' },
      description: '是否显示背板',
    },
    shelfColor: {
      control: { type: 'color' },
      description: '货架板颜色',
    },
    showDebugInfo: {
      control: { type: 'boolean' },
      description: '显示调试信息',
    },
    itemStartPosition: {
      control: { type: 'select' },
      options: ['top-left', 'top-right', 'bottom-left', 'bottom-right'],
      description: '物品排列的起始位置：左上、右上、左下、右下',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 基础货架
export const Default: Story = {
  args: {
    width: 200,
    height: 150,
    depth: 100,
    borderColor: '#444444',
    columns: 3,
    rows: 2,
    items: [],
    showBackPanel: true,
    shelfColor: '#F0F0F0',
    statusToColor: {
      Idled: '#909399',
      Processing: '#409EFF',
      Completed: '#67C23A',
      Aborted: '#E6A23C',
      Failed: '#F56C6C',
      Returning: '#9370DB',
      Returned: '#B0C4DE',
      CompleteReturning: '#48D1CC',
    },
  },
};

// 带物品的货架
export const WithItems: Story = {
  args: {
    width: 240,
    height: 180,
    depth: 120,
    columns: 4,
    rows: 2,
    items: [
      { name: '待处理', status: 'Idled' },
      { name: '处理中', status: 'Processing' },
      { name: '已完成', status: 'Completed' },
      { name: '终止', status: 'Aborted' },
      { name: '失败', status: 'Failed' },
      { name: '正在返回', status: 'Returning' },
      undefined as any,
      { name: '已完成返回', status: 'CompleteReturning' },
    ],
    statusToColor: {
      Idled: '#909399',
      Processing: '#409EFF',
      Completed: '#67C23A',
      Aborted: '#E6A23C',
      Failed: '#DC3545',
      Returning: '#9370DB',
      Returned: '#B0C4DE',
      CompleteReturning: '#48D1CC',
    },
  },
};

// 不同起始位置演示
export const DifferentStartPositions: Story = {
  name: '不同起始位置演示',
  args: {
    width: 240,
    height: 180,
    depth: 120,
    columns: 3,
    rows: 2,
    items: [
      { name: '1', status: 'Processing' },
      { name: '2', status: 'Completed' },
      { name: '3', status: 'Idled' },
      { name: '4', status: 'Aborted' },
      { name: '5', status: 'Failed' },
      { name: '6', status: 'Returning' },
    ],
    itemStartPosition: 'bottom-right', // 演示从右下开始
    statusToColor: {
      Idled: '#909399',
      Processing: '#409EFF',
      Completed: '#67C23A',
      Aborted: '#E6A23C',
      Failed: '#DC3545',
      Returning: '#9370DB',
      Returned: '#B0C4DE',
      CompleteReturning: '#48D1CC',
    },
  },
};

// 左上角起始（默认）
export const TopLeftStart: Story = {
  name: '左上角起始',
  args: {
    width: 200,
    height: 150,
    depth: 100,
    columns: 3,
    rows: 2,
    items: [
      { name: '1', status: 'Processing' },
      { name: '2', status: 'Completed' },
      { name: '3', status: 'Idled' },
      { name: '4', status: 'Aborted' },
      { name: '5', status: 'Failed' },
    ],
    itemStartPosition: 'top-left',
  },
};

// 右上角起始
export const TopRightStart: Story = {
  name: '右上角起始',
  args: {
    width: 200,
    height: 150,
    depth: 100,
    columns: 3,
    rows: 2,
    items: [
      { name: '1', status: 'Processing' },
      { name: '2', status: 'Completed' },
      { name: '3', status: 'Idled' },
      { name: '4', status: 'Aborted' },
      { name: '5', status: 'Failed' },
    ],
    itemStartPosition: 'top-right',
  },
};

// 左下角起始
export const BottomLeftStart: Story = {
  name: '左下角起始',
  args: {
    width: 200,
    height: 150,
    depth: 100,
    columns: 3,
    rows: 2,
    items: [
      { name: '1', status: 'Processing' },
      { name: '2', status: 'Completed' },
      { name: '3', status: 'Idled' },
      { name: '4', status: 'Aborted' },
      { name: '5', status: 'Failed' },
    ],
    itemStartPosition: 'bottom-left',
  },
};

// 右下角起始
export const BottomRightStart: Story = {
  name: '右下角起始',
  args: {
    width: 200,
    height: 150,
    depth: 100,
    columns: 3,
    rows: 2,
    items: [
      { name: '1', status: 'Processing' },
      { name: '2', status: 'Completed' },
      { name: '3', status: 'Idled' },
      { name: '4', status: 'Aborted' },
      { name: '5', status: 'Failed' },
    ],
    itemStartPosition: 'bottom-right',
  },
};
