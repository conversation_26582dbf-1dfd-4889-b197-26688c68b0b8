import type { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import Plate from './index.vue';

const meta = {
  title: 'Equipment/Plate',
  component: Plate,
  tags: ['autodocs'],
  argTypes: {
    scale: {
      control: 'number',
      description: '整体缩放比例',
    },
    waferState: {
      control: 'boolean',
      description: '晶圆是否显示',
    },
    waferColor: {
      control: 'color',
      description: '晶圆颜色',
    },
    waferName: {
      control: 'text',
      description: '晶圆名称',
    },
    waferScale: {
      control: 'number',
      description: '晶圆缩放比例',
    },
  },
  args: {
    scale: 1,
    waferState: false,
    waferColor: '#000000',
    waferName: 'A01',
    waferScale: 1,
  },
  parameters: {
    docs: {
      description: {
        component:
          'Plate组件用于显示晶圆托盘，支持晶圆的显示/隐藏、旋转、缩放等功能，并提供平滑的动画效果。',
      },
    },
  },
} satisfies Meta<typeof Plate>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  name: '基础示例',
  render: (args) => ({
    components: { Plate },
    setup() {
      return { args };
    },
    template: `
      <div style="width: 200px; height: 200px;">
        <Plate v-bind="args" />
      </div>
    `,
  }),
};

// 晶圆动画示例
export const WaferAnimation: Story = {
  name: '晶圆展示',
  render: (args) => ({
    components: { Plate },
    setup() {
      const waferState = ref(false);

      const toggleWafer = () => {
        waferState.value = !waferState.value;
      };

      return { args, waferState, toggleWafer };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="width: 200px; height: 200px;">
          <Plate
            v-bind="args"
            :wafer-state="waferState"
          />
        </div>
        <button 
          @click="toggleWafer"
          style="padding: 8px 16px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;"
        >
          {{ waferState ? '隐藏晶圆' : '显示晶圆' }}
        </button>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: '这个示例展示了晶圆的淡入淡出动画效果。',
      },
    },
  },
};

// 缩放示例
export const Scaling: Story = {
  name: '缩放控制',
  render: (args) => ({
    components: { Plate },
    setup() {
      const scale = ref(1);
      const waferScale = ref(1);
      const waferState = ref(true);

      return { args, scale, waferScale, waferState };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="width: 200px; height: 200px;">
          <Plate
            v-bind="args"
            :scale="scale"
            :wafer-scale="waferScale"
            :wafer-state="waferState"
          />
        </div>
        <div>
          <label>整体缩放: {{ scale.toFixed(1) }}</label>
          <input 
            type="range" 
            v-model.number="scale" 
            min="0.5" 
            max="2" 
            step="0.1" 
            style="width: 100%;" 
          />
        </div>
        <div>
          <label>晶圆缩放: {{ waferScale.toFixed(1) }}</label>
          <input 
            type="range" 
            v-model.number="waferScale" 
            min="0.5" 
            max="2" 
            step="0.1" 
            style="width: 100%;" 
          />
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: '这个示例允许您分别控制托盘和晶圆的缩放比例。',
      },
    },
  },
};
