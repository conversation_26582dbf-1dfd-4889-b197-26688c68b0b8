import type { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import RobotStatic from './index.vue';

const meta = {
  title: 'Equipment/RobotStatic',
  component: RobotStatic,
  tags: ['autodocs'],
  argTypes: {
    type: {
      control: 'select',
      options: ['hand', 'robot'],
      description: '机器人类型，可选择机械手或机器人',
    },
    state: {
      control: 'select',
      options: ['normal', 'disabled'],
      description: '机器人状态',
    },
    rotate: {
      control: 'number',
      description: '旋转角度',
    },
    waferName: {
      control: 'text',
      description: '晶圆名称（仅在 type="hand" 时有效）',
    },
    waferColor: {
      control: 'color',
      description: '晶圆颜色（仅在 type="hand" 时有效）',
    },
    stateColor: {
      control: 'color',
      description: '机器人颜色',
    },
  },
  args: {
    type: 'robot',
    state: 'normal',
    rotate: 0,
    stateColor: '#000000',
  },
  parameters: {
    docs: {
      description: {
        component:
          'RobotStatic 组件用于显示静态的机器人图标，支持机械手和机器人两种类型，可以配置旋转角度、状态颜色等属性。当类型为机械手时，还可以显示晶圆信息。',
      },
    },
  },
} satisfies Meta<typeof RobotStatic>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础机器人示例
export const DefaultRobot: Story = {
  name: '基础机器人',
  args: {},
  render: (args) => ({
    components: { RobotStatic },
    setup() {
      return { args };
    },
    template: `
      <div style="width: 80px; height: 130px;">
        <RobotStatic v-bind="args" />
      </div>
    `,
  }),
};

// 机械手示例
export const RobotHand: Story = {
  name: '机械手',
  args: {
    type: 'hand',
    waferName: 'A01',
    waferColor: '#1677ff',
    stateColor: '#2196F3',
  },
  render: (args) => ({
    components: { RobotStatic },
    setup() {
      return { args };
    },
    template: `
      <div style="width: 160px; height: 160px;">
        <RobotStatic v-bind="args" />
      </div>
    `,
  }),
};

// 禁用状态示例
export const DisabledRobot: Story = {
  name: '禁用状态',
  args: {
    state: 'disabled',
    stateColor: '#999999',
  },
  render: (args) => ({
    components: { RobotStatic },
    setup() {
      return { args };
    },
    template: `
      <div style="width: 160px; height: 160px;">
        <RobotStatic v-bind="args" />
      </div>
    `,
  }),
};

// 旋转示例
export const RotateExample: Story = {
  name: '旋转控制',
  render: (args) => ({
    components: { RobotStatic },
    setup() {
      const rotate = ref(0);
      return { args, rotate };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="display: flex; gap: 20px; align-items: center;">
          <div style="width: 150px;">
            <RobotStatic v-bind="args" :rotate="rotate" />
          </div>
          <div style="flex: 1;">
            <div>
              <label>旋转角度: {{ rotate }}°</label>
              <input type="range" v-model.number="rotate" min="0" max="360" style="width: 100%;" />
            </div>
          </div>
        </div>
      </div>
    `,
  }),
};

// 机械手带晶圆示例
export const HandWithWafer: Story = {
  name: '机械手带晶圆',
  render: (args) => ({
    components: { RobotStatic },
    setup() {
      const waferColors = [
        { name: 'A01', color: '#1677ff' },
        { name: 'A02', color: '#52c41a' },
        { name: 'B03', color: '#f5222d' },
        { name: 'C04', color: '#fa8c16' },
      ];

      return {
        args,
        waferColors,
      };
    },
    template: `
      <div style="display: flex; gap: 20px; flex-wrap: wrap;">
        <div v-for="wafer in waferColors" :key="wafer.name" style="text-align: center; width: 100px; height: 100px;">
          <RobotStatic
            v-bind="args"
            type="hand"
            :waferName="wafer.name"
            :waferColor="wafer.color"
            :name="wafer.name"
          />
          <div style="margin-top: 10px;">{{ wafer.name }}</div>
        </div>
      </div>
    `,
  }),
};
