// 从 SixAxisRobot 导入物料相关类型
import type { MaterialConfig, MaterialInput } from '../SixAxisRobot/index.vue';
export type { MaterialConfig, MaterialInput };

// 扩展传送带物品接口，添加物料支持
export interface BeltItem {
  id: string | number;
  position: number; // 0-100, percentage of the belt's length
  content: string;
  material?: MaterialInput; // 可选的物料配置
}

// 支持的 items 输入类型
export type BeltItemInput =
  | BeltItem // 完整的 BeltItem 对象
  | string // 字符串，将作为物料类型
  | MaterialInput; // 物料配置，将自动生成 BeltItem

// items 参数类型
export type BeltItemsInput = BeltItemInput[];

// 物料尺寸配置（用于传送带适配）
export interface MaterialSize {
  width: number;
  height: number;
}

// 传送带物料配置
export interface TransferMaterialConfig {
  // 是否启用物料功能
  enabled: boolean;
  // 默认物料尺寸（相对于传送带高度的比例）
  defaultSizeRatio: number;
  // 物料与传送带边缘的间距
  margin: number;
}

// 物料渲染选项
export interface MaterialRenderOptions {
  // 物料尺寸
  size: MaterialSize;
  // 物料颜色
  color: string;
  // 是否显示标签
  showLabel: boolean;
  // 标签位置
  labelPosition: 'top' | 'bottom' | 'center';
}
