import type { <PERSON><PERSON>, StoryObj } from '@storybook/vue3';
import { ref, reactive } from 'vue';
import AgvRobot from './index.vue';
import { defaultConfig } from '../SixAxisRobot/config';

const meta = {
  title: 'Equipment/AgvRobot',
  component: AgvRobot,
  tags: ['autodocs'],
  argTypes: {
    width: {
      control: 'number',
      description: '组件宽度',
    },
    height: {
      control: 'number',
      description: '组件高度',
    },
    scale: {
      control: 'number',
      description: '缩放比例',
    },
    currentStation: {
      control: 'select',
      options: Object.keys(defaultConfig.positionList),
      description: '当前AGV位置',
    },
    stationList: {
      control: false,
      description: 'AGV位置配置列表',
    },
    animateDuration: {
      control: 'number',
      description: '动画持续时间',
    },
    currentPosition: {
      control: 'select',
      options: Object.keys(defaultConfig.positionList),
      description: '当前机械臂位置',
    },
    positionList: {
      control: false,
      description: '机械臂位置配置列表',
    },
    material: {
      control: 'object',
      description: '抓手上的物料配置，可以设置物料类型、颜色和尺寸',
    },
  },
  args: {
    width: defaultConfig.width,
    height: defaultConfig.height,
    scale: 1,
    currentStation: 'position1',
    stationList: {
      position1: { left: -140, top: 130 },
      position2: { left: -50, top: 130 },
      position3: { left: -100, top: 100 },
    },
    animateDuration: defaultConfig.animateDuration,
    currentPosition: defaultConfig.currentPosition,
    positionList: defaultConfig.positionList,
    material: defaultConfig.material,
  },
  parameters: {
    docs: {
      description: {
        component: 'AGV机器人组件，结合了SixAxisRobot的机械臂功能和AGV底座。',
      },
    },
  },
} satisfies Meta<typeof AgvRobot>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  name: '默认状态',
  args: {
    currentStation: 'position1',
    stationList: {
      position1: { left: -140, top: 130 },
      position2: { left: -50, top: 130 },
      position3: { left: -100, top: 100 },
    },
    currentPosition: 'position1',
    positionList: defaultConfig.positionList,
    material: {
      type: 'block',
      color: '#8B4513',
      label: '木块',
    },
  },
};

// 机械臂动作示例
export const ArmMovement: Story = {
  name: '机械臂动作控制',
  render: (args) => ({
    components: { AgvRobot },
    setup() {
      const currentPosition = ref('position1');
      const currentStation = ref('position1');

      const positionOptions = Object.keys(defaultConfig.positionList);
      const stationOptions = ['position1', 'position2', 'position3'];

      return {
        args,
        currentPosition,
        currentStation,
        positionOptions,
        stationOptions,
      };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="padding: 16px; border: 1px solid #eee; border-radius: 8px;">
          <h3 style="margin: 0 0 16px 0;">AGV机械臂控制面板</h3>
          
          <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
            <div>
              <h4>机械臂控制</h4>
              <div style="display: flex; flex-direction: column; gap: 8px;">
                <label>当前位置:</label>
                <select v-model="currentPosition" style="padding: 6px; border-radius: 4px; border: 1px solid #ccc;">
                  <option v-for="option in positionOptions" :key="option" :value="option">{{ option }}</option>
                </select>
              </div>
            </div>
            
            <div>
              <h4>AGV位置控制</h4>
              <div style="display: flex; flex-direction: column; gap: 8px;">
                <label>当前站点:</label>
                <select v-model="currentStation" style="padding: 6px; border-radius: 4px; border: 1px solid #ccc;">
                  <option v-for="option in stationOptions" :key="option" :value="option">{{ option }}</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        
        <div style="border: 1px solid #eee; border-radius: 8px; padding: 16px; display: flex; justify-content: center;">
          <AgvRobot
            v-bind="args"
            :currentPosition="currentPosition"
            :currentStation="currentStation"
          />
        </div>
      </div>
    `,
  }),
};

// 不同位置示例 - 改为单个组件切换，避免同时创建多个WebGL上下文
export const DifferentPositions: Story = {
  name: '不同机械臂位置',
  render: () => ({
    components: { AgvRobot },
    setup() {
      const selectedPosition = ref('position1');
      const positions = ['position1', 'position2', 'position3'];

      return {
        selectedPosition,
        positions,
      };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="padding: 16px; border: 1px solid #eee; border-radius: 8px;">
          <h3 style="margin: 0 0 16px 0;">机械臂位置对比</h3>
          
          <div style="display: flex; gap: 10px; margin-bottom: 16px;">
            <button 
              v-for="position in positions" 
              :key="position"
              @click="selectedPosition = position"
              :style="{
                padding: '8px 16px',
                background: selectedPosition === position ? '#1677ff' : '#f0f0f0',
                color: selectedPosition === position ? 'white' : '#333',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }"
            >
              {{ position }}
            </button>
          </div>
          
          <div style="text-align: center; margin-bottom: 10px;">
            <strong>当前位置: {{ selectedPosition }}</strong>
          </div>
        </div>
        
        <div style="border: 1px solid #eee; border-radius: 8px; padding: 16px; display: flex; justify-content: center;">
          <AgvRobot :key="selectedPosition" :currentPosition="selectedPosition" />
        </div>
      </div>
    `,
  }),
};

// 动态部件信息测试
export const DynamicPartInfoTest: Story = {
  name: '动态部件信息测试',
  render: () => ({
    components: { AgvRobot },
    setup() {
      const testMessage = ref('');
      const messageLog = ref<string[]>([]);

      // 模拟 MQTT 消息更新
      const simulateMqttUpdate = () => {
        const mockMessage = {
          partId: 'RobotAGV',
          Station: Math.random() > 0.5 ? 'position2' : 'position3',
          timestamp: new Date().toLocaleTimeString(),
        };

        testMessage.value = JSON.stringify(mockMessage, null, 2);
        messageLog.value.unshift(
          `${mockMessage.timestamp}: Station = ${mockMessage.Station}`,
        );

        // 保持日志不超过10条
        if (messageLog.value.length > 10) {
          messageLog.value = messageLog.value.slice(0, 10);
        }

        console.log('模拟MQTT消息:', mockMessage);
      };

      return {
        testMessage,
        messageLog,
        simulateMqttUpdate,
      };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="padding: 16px; border: 1px solid #eee; border-radius: 8px;">
          <h3 style="margin: 0 0 16px 0;">动态部件信息测试</h3>
          <p style="margin: 0 0 16px 0; color: #666; font-size: 14px;">
            此测试验证 dynamicPartInfo 功能是否正常工作，不会出现 readonly 错误
          </p>

          <button
            @click="simulateMqttUpdate"
            style="padding: 8px 16px; background: #52c41a; color: white; border: none; border-radius: 4px; cursor: pointer; margin-bottom: 16px;"
          >
            模拟MQTT消息更新
          </button>

          <div v-if="messageLog.length > 0" style="margin-bottom: 16px;">
            <h4 style="margin: 0 0 8px 0;">消息日志:</h4>
            <div style="background: #f5f5f5; padding: 8px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 150px; overflow-y: auto;">
              <div v-for="log in messageLog" :key="log" style="margin-bottom: 4px;">{{ log }}</div>
            </div>
          </div>
        </div>

        <div style="border: 1px solid #eee; border-radius: 8px; padding: 16px; display: flex; justify-content: center;">
          <AgvRobot
            partId="TestAGV"
            :dynamicPartInfo="{
              currentPosition: {
                connectStrategy: 'default',
                attributeName: 'RobotAGV.Station',
              },
            }"
            :width="800"
            :height="600"
          />
        </div>
      </div>
    `,
  }),
};

// AGV移动演示
export const AgvMovement: Story = {
  name: 'AGV移动演示',
  render: () => ({
    components: { AgvRobot },
    setup() {
      const isMoving = ref(false);
      const currentStep = ref(0);

      const movements = [
        { station: 'position1', position: 'position1', name: '起始位置' },
        { station: 'position2', position: 'position2', name: '移动到站点2' },
        { station: 'position2', position: 'position3', name: '抓取动作' },
        { station: 'position3', position: 'position2', name: '移动到站点3' },
        { station: 'position1', position: 'position1', name: '返回起始位置' },
      ];

      const currentMovement = reactive({ ...movements[0] });

      const startDemo = async () => {
        isMoving.value = true;

        for (let i = 0; i < movements.length; i++) {
          if (!isMoving.value) break;

          currentStep.value = i;

          // 使用Object.assign来确保响应性
          Object.assign(currentMovement, movements[i]);

          console.log(`Step ${i + 1}: Moving to`, movements[i]);

          // 等待2.5秒再执行下一步（给动画更多时间）
          await new Promise((resolve) => setTimeout(resolve, 2500));
        }

        isMoving.value = false;
        currentStep.value = 0;
        Object.assign(currentMovement, movements[0]);
      };

      const stopDemo = () => {
        isMoving.value = false;
        currentStep.value = 0;
        Object.assign(currentMovement, movements[0]);
      };

      return {
        isMoving,
        currentStep,
        movements,
        currentMovement,
        startDemo,
        stopDemo,
      };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="padding: 16px; border: 1px solid #eee; border-radius: 8px;">
          <h3 style="margin: 0 0 16px 0;">AGV移动演示</h3>
          
          <div style="display: flex; gap: 10px; margin-bottom: 16px;">
            <button 
              @click="startDemo" 
              :disabled="isMoving"
              style="padding: 8px 16px; background: #1677ff; color: white; border: none; border-radius: 4px; cursor: pointer;"
            >
              {{ isMoving ? '演示中...' : '开始演示' }}
            </button>
            
            <button 
              @click="stopDemo" 
              :disabled="!isMoving"
              style="padding: 8px 16px; background: #f5222d; color: white; border: none; border-radius: 4px; cursor: pointer;"
            >
              停止演示
            </button>
          </div>
          
          <div style="margin-bottom: 10px;">
            <strong>当前步骤 {{ currentStep + 1 }}/{{ movements.length }}:</strong> 
            {{ currentMovement.name }}
          </div>
          
          <div style="margin-bottom: 10px; font-size: 12px; color: #666;">
            AGV站点: {{ currentMovement.station }}, 机械臂位置: {{ currentMovement.position }}
          </div>
          
          <div style="display: flex; gap: 5px;">
            <div 
              v-for="(step, index) in movements" 
              :key="index"
              :style="{
                width: '40px',
                height: '8px',
                backgroundColor: index <= currentStep ? '#1677ff' : '#f0f0f0',
                borderRadius: '4px'
              }"
            ></div>
          </div>
        </div>
        
        <div style="border: 1px solid #eee; border-radius: 8px; padding: 16px; display: flex; justify-content: center;">
          <AgvRobot
            :currentStation="currentMovement.station"
            :currentPosition="currentMovement.position"
            :animateDuration="1.5"
            :width="800"
            :height="600"
          />
        </div>
      </div>
    `,
  }),
};

// 物料演示示例
export const WithBlockMaterial: Story = {
  name: 'AGV携带木块',
  args: {
    material: {
      type: 'block',
      color: '#8B4513',
      label: '木块',
    },
  },
};

export const WithCubeMaterial: Story = {
  name: 'AGV携带魔方',
  args: {
    material: {
      type: 'cube',
      color: '#FF6B6B',
      label: '魔方',
    },
  },
};

export const WithSphereMaterial: Story = {
  name: 'AGV携带球体',
  args: {
    material: {
      type: 'sphere',
      color: '#4ECDC4',
      size: { width: 20, height: 20 },
      label: '球体',
    },
  },
};

export const WithCylinderMaterial: Story = {
  name: 'AGV携带圆柱体',
  args: {
    material: {
      type: 'cylinder',
      color: '#95A5A6',
      size: { width: 16, height: 24 },
      label: '圆柱体',
    },
  },
};

export const WithTrayMaterial: Story = {
  name: 'AGV携带托盘',
  args: {
    material: {
      type: 'tray',
      color: '#E8E8E8',
      size: { width: 28, height: 12 },
      label: '托盘',
    },
  },
};

// 字符串形式的物料演示
export const WithStringMaterial: Story = {
  name: 'AGV字符串形式物料',
  args: {
    material: 'cube', // 字符串形式，等同于使用默认配置的魔方
  },
};

// 交互式物料切换示例
export const MaterialSwitcher: Story = {
  name: 'AGV物料切换器',
  render: (args: any) => ({
    components: { AgvRobot },
    setup() {
      const currentMaterial = ref(null);
      const currentPosition = ref('position1');

      const materialOptions = [
        { label: '无物料', value: null },
        {
          label: '木块',
          value: { type: 'block', color: '#8B4513', label: '木块' },
        },
        {
          label: '魔方',
          value: { type: 'cube', color: '#FF6B6B', label: '魔方' },
        },
        {
          label: '球体',
          value: { type: 'sphere', color: '#4ECDC4', label: '球体' },
        },
        {
          label: '圆柱体',
          value: { type: 'cylinder', color: '#95A5A6', label: '圆柱体' },
        },
        {
          label: '托盘',
          value: { type: 'tray', color: '#E8E8E8', label: '托盘' },
        },
        {
          label: '字符串球体',
          value: 'sphere', // 字符串形式
        },
        {
          label: '字符串圆柱体',
          value: 'cylinder', // 字符串形式
        },
      ];

      const positionOptions = Object.keys(defaultConfig.positionList);

      return {
        args,
        currentMaterial,
        currentPosition,
        materialOptions,
        positionOptions,
      };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="display: flex; gap: 20px; padding: 16px; border: 1px solid #eee; border-radius: 8px;">
          <div style="flex: 1;">
            <h4 style="margin: 0 0 10px 0;">物料控制</h4>
            <div style="display: flex; flex-direction: column; gap: 8px;">
              <label>选择物料:</label>
              <select v-model="currentMaterial" style="padding: 6px; border-radius: 4px; border: 1px solid #ccc;">
                <option v-for="option in materialOptions" :key="option.label" :value="option.value">
                  {{ option.label }}
                </option>
              </select>
            </div>
          </div>

          <div style="flex: 1;">
            <h4 style="margin: 0 0 10px 0;">机械臂控制</h4>
            <div style="display: flex; flex-direction: column; gap: 8px;">
              <label>机械臂位置:</label>
              <select v-model="currentPosition" style="padding: 6px; border-radius: 4px; border: 1px solid #ccc;">
                <option v-for="option in positionOptions" :key="option" :value="option">{{ option }}</option>
              </select>
            </div>
          </div>
        </div>

        <div style="border: 1px solid #eee; border-radius: 8px; padding: 16px; height: 600px;">
          <AgvRobot
            :width="args.width"
            :height="args.height"
            :scale="args.scale"
            :animateDuration="args.animateDuration"
            :currentPosition="currentPosition"
            :positionList="args.positionList"
            :material="currentMaterial"
          />
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story:
          '这个示例提供了一个交互式的AGV物料切换器，您可以选择不同的物料类型和机械臂位置来查看效果。',
      },
    },
  },
};
