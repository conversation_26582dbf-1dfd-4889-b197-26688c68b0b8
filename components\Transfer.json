{"id": 1, "version": "0.0.17", "name": {"zh_CN": "传送带"}, "component": "Transfer", "icon": "chevron-double-right", "description": "可自定义配置的传送带组件，支持物料传输视觉效果", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "Transfer", "version": "0.0.17", "script": "http://*************:4874/@dcp/component-library@0.0.17/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "group": "DCP", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["width", "height", "rotate", "running", "speed", "direction"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}}, "schema": {"properties": [{"label": {"zh_CN": "基本尺寸"}, "description": {"zh_CN": "传送带的基本尺寸和旋转角度配置"}, "content": [{"property": "width", "label": {"text": {"zh_CN": "宽度"}}, "description": {"zh_CN": "传送带的宽度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 500, "widget": {"component": "NumberConfigurator", "props": {"min": 100, "max": 2000, "step": 50}}}, {"property": "height", "label": {"text": {"zh_CN": "高度"}}, "description": {"zh_CN": "传送带的高度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 100, "widget": {"component": "NumberConfigurator", "props": {"min": 50, "max": 500, "step": 10}}}, {"property": "rotate", "label": {"text": {"zh_CN": "旋转角度"}}, "description": {"zh_CN": "传送带的旋转角度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 0, "widget": {"component": "SliderConfigurator", "props": {"min": 0, "max": 360, "step": 15}}}]}, {"label": {"zh_CN": "运行参数"}, "description": {"zh_CN": "传送带的运行参数配置"}, "content": [{"property": "running", "label": {"text": {"zh_CN": "运行状态"}}, "description": {"zh_CN": "传送带是否正在运行"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator"}}, {"property": "speed", "label": {"text": {"zh_CN": "运行速度"}}, "description": {"zh_CN": "传送带的运行速度（物料从一端到另一端所需的秒数）"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 10, "widget": {"component": "NumberConfigurator", "props": {"min": 1, "max": 30, "step": 1}}}, {"property": "direction", "label": {"text": {"zh_CN": "运行方向"}}, "description": {"zh_CN": "传送带的运行方向，1表示向右，-1表示向左"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 1, "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "向右", "value": 1}, {"label": "向左", "value": -1}]}}}]}, {"label": {"zh_CN": "传送带配置"}, "description": {"zh_CN": "传送带的节段配置"}, "content": [{"property": "segmentCount", "label": {"text": {"zh_CN": "传送带节段数"}}, "description": {"zh_CN": "传送带的节段数量"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 30, "widget": {"component": "SliderConfigurator", "props": {"min": 10, "max": 50, "step": 5}}}]}, {"label": {"zh_CN": "物料配置"}, "description": {"zh_CN": "传送带上物料的配置"}, "content": [{"property": "materialImgPath", "label": {"text": {"zh_CN": "物料图片路径"}}, "description": {"zh_CN": "传送带上物料的图片路径"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "", "widget": {"component": "ImageUploadConfigurator"}}, {"property": "materialWidth", "label": {"text": {"zh_CN": "物料宽度"}}, "description": {"zh_CN": "传送带上物料的宽度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 50, "widget": {"component": "NumberConfigurator", "props": {"min": 10, "max": 200, "step": 5}}}, {"property": "materialHeight", "label": {"text": {"zh_CN": "物料高度"}}, "description": {"zh_CN": "传送带上物料的高度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 50, "widget": {"component": "NumberConfigurator", "props": {"min": 10, "max": 200, "step": 5}}}, {"property": "listenMaterialCmd", "label": {"text": {"zh_CN": "添加物料命令"}}, "description": {"zh_CN": "监听添加物料的命令"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "", "widget": {"component": "InputConfigurator"}}, {"property": "listenMaterialRemoveCmd", "label": {"text": {"zh_CN": "移除物料命令"}}, "description": {"zh_CN": "监听移除物料的命令"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "", "widget": {"component": "InputConfigurator"}}]}], "events": {}}, "snippets": [{"name": {"zh_CN": "传送带"}, "icon": "transfer", "screenshot": "", "snippetName": "Transfer", "schema": {"props": {"width": 500, "height": 100, "rotate": 0, "running": false, "speed": 10, "direction": 1, "segmentCount": 30}}}], "category": "DCP"}