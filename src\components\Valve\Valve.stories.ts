import type { Meta, StoryObj } from '@storybook/vue3';
import Valve from './index.vue';

const meta = {
  title: 'Equipment/Valve',
  component: Valve,
  tags: ['autodocs'],
  argTypes: {
    color: {
      control: 'color',
      description: '阀门颜色',
    },
    isDisabled: {
      control: 'boolean',
      description: '是否禁用',
    },
    rotate: {
      control: 'number',
      description: '旋转角度',
    },
    pauseFlow: {
      control: 'boolean',
      description: '是否暂停流动',
    },
  },
  args: {
    color: '#000000',
    isDisabled: false,
    rotate: 0,
    pauseFlow: false,
  },
  parameters: {
    docs: {
      description: {
        component:
          'Valve组件用于显示阀门，支持自定义颜色、旋转角度，可以设置禁用状态和流动状态。',
      },
    },
  },
} satisfies Meta<typeof Valve>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  name: '基础示例',
  render: (args) => ({
    components: { Valve },
    setup() {
      return { args };
    },
    template: `
      <div style="width: 200px; height: 200px;">
        <Valve v-bind="args" />
      </div>
    `,
  }),
};

// 旋转示例
export const RotationAngles: Story = {
  name: '旋转角度',
  render: (args) => ({
    components: { Valve },
    setup() {
      const angles = [
        { name: '0°', value: 0 },
        { name: '90°', value: 90 },
      ];

      return { args, angles };
    },
    template: `
      <div style="display: flex; gap: 20px; flex-wrap: wrap;">
        <div v-for="angle in angles" :key="angle.name" style="text-align: center;">
          <div style="width: 200px; height: 200px;">
            <Valve
              v-bind="args"
              :rotate="angle.value"
            />
          </div>
          <div style="margin-top: 10px;">{{ angle.name }}</div>
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: '这个示例展示了阀门在不同旋转角度下的显示效果。',
      },
    },
  },
};

// 状态示例
export const States: Story = {
  name: '不同状态',
  render: (args) => ({
    components: { Valve },
    setup() {
      const states = [
        { name: '正常', disabled: false, color: '#1677ff' },
        { name: '禁用', disabled: true, color: '#1677ff' },
        { name: '暂停', disabled: false, pauseFlow: true, color: '#f5222d' },
        {
          name: '禁用且暂停',
          disabled: true,
          pauseFlow: true,
          color: '#f5222d',
        },
      ];

      return { args, states };
    },
    template: `
      <div style="display: flex; gap: 20px; flex-wrap: wrap;">
        <div v-for="state in states" :key="state.name" style="text-align: center;">
          <div style="width: 200px; height: 200px;">
            <Valve
              v-bind="args"
              :color="state.color"
              :is-disabled="state.disabled"
              :pause-flow="state.pauseFlow"
            />
          </div>
          <div style="margin-top: 10px;">{{ state.name }}</div>
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story:
          '这个示例展示了阀门在不同状态下的显示效果，包括正常、禁用、暂停流动等状态。',
      },
    },
  },
};
