# StorageBox 组件排列方向功能说明

## 功能概述

StorageBox 组件新增了 `sortDirection` 属性，支持两种物品排列方向：
- **横向排列（horizontal）**：按行排序，先填满第一行，再填第二行...（原有功能）
- **竖向排列（vertical）**：按列排序，先填满第一列，再填第二列...（新增功能）

## 属性说明

### sortDirection
- **类型**: `String`
- **默认值**: `'horizontal'`
- **可选值**: `'horizontal'` | `'vertical'`
- **描述**: 控制物品在储藏盒中的排列方向

### 与现有属性的配合

`sortDirection` 与 `itemStartPosition` 属性配合使用，支持8种排列组合：

#### 横向排列（horizontal）
- `top-left`: 从左上开始，从左到右、从上到下
- `top-right`: 从右上开始，从右到左、从上到下  
- `bottom-left`: 从左下开始，从左到右、从下到上
- `bottom-right`: 从右下开始，从右到左、从下到上

#### 竖向排列（vertical）
- `top-left`: 从左上开始，从上到下、从左到右
- `top-right`: 从右上开始，从上到下、从右到左
- `bottom-left`: 从左下开始，从下到上、从左到右
- `bottom-right`: 从右下开始，从下到上、从右到左

## 使用示例

```vue
<template>
  <!-- 横向排列（默认） -->
  <StorageBox
    :rows="4"
    :columns="6"
    :items="items"
    itemStartPosition="top-left"
    sortDirection="horizontal"
  />

  <!-- 竖向排列 -->
  <StorageBox
    :rows="4"
    :columns="6"
    :items="items"
    itemStartPosition="top-left"
    sortDirection="vertical"
  />
</template>
```

## 排列效果对比

假设有一个 3×4 的储藏盒（3行4列），物品数组为 [A, B, C, D, E, F, G, H, I, J, K, L]

### 横向排列 (horizontal) + top-left
```
A B C D
E F G H
I J K L
```

### 竖向排列 (vertical) + top-left
```
A D G J
B E H K
C F I L
```

## 格子编号

当启用 `showGridNumbers` 时，格子编号会根据排列方向自动调整：

### 横向排列编号
```
1  2  3  4
5  6  7  8
9  10 11 12
```

### 竖向排列编号
```
1  4  7  10
2  5  8  11
3  6  9  12
```

## 测试文件

可以使用 `test-sort-direction.vue` 文件来测试不同排列方向的效果：

```bash
# 在开发环境中引入测试组件
import TestSortDirection from '@/components/StorageBox/test-sort-direction.vue'
```

## 向后兼容性

- 新增的 `sortDirection` 属性默认值为 `'horizontal'`，保持与原有功能完全兼容
- 现有代码无需修改即可正常工作
- 所有原有的 `itemStartPosition` 选项继续有效

## 实现细节

新功能主要修改了以下方法：
1. `calculateItemPosition()`: 根据排列方向计算物品位置
2. `calculateGridNumber()`: 根据排列方向计算格子编号
3. `renderGrid()`: 传递排列方向参数
4. `render()`: 支持新的排列方向参数

所有修改都保持了原有代码结构和逻辑的完整性。
