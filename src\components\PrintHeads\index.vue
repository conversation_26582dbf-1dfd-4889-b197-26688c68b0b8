<template>
  <div class="content">
    <svg
      v-if="type === 'liquid'"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 50.904 112.5"
    >
      <g
        id="Group_Volute_Chamber"
        transform="matrix(0.9999999,3.019916E-07,3.019916E-07,-0.9999999,0,112)"
      >
        <linearGradient
          id="liquidPrintHeadsHand"
          gradientUnits="userSpaceOnUse"
          x1="0.0017"
          y1="82.0381"
          x2="50.9021"
          y2="82.0381"
          gradientTransform="matrix(1 0 0 -1 0 112.5)"
        >
          <stop offset="0.01" :style="'stop-color: ' + color" />
          <stop offset="0.51" :style="'stop-color: ' + colorLighten" />
          <stop offset="1" :style="'stop-color: ' + color" />
        </linearGradient>
        <path
          fill="url(#liquidPrintHeadsHand)"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M14.078,0l-1.352,5.631L8.447,7.095v4.167H0.002v5.743h2.815   v34.009H0.002v5.63h8.445v4.28h34.01v-4.28h8.445v-5.63h-2.815v-34.01h2.815v-5.743h-8.445V7.095l-4.278-1.464L36.826,0H14.078"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M8.447,11.261h34.01"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M2.817,17.004h45.27"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M2.817,51.014h45.27"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M8.447,56.644h34.01"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M12.726,5.631h25.45"
        />

        <linearGradient
          id="LIQUID_PRINT_2_"
          gradientUnits="userSpaceOnUse"
          x1="2.8171"
          y1="66.498"
          x2="48.0867"
          y2="66.498"
          gradientTransform="matrix(1 0 0 -1 0 112.5)"
        >
          <stop offset="0.01" style="stop-color: #4d4d4d" />
          <stop offset="0.51" style="stop-color: #f5f5f5" />
          <stop offset="1" style="stop-color: #4d4d4d" />
        </linearGradient>
        <path
          fill="url(#LIQUID_PRINT_2_)"
          d="M2.817,42.455h45.27v7.095H2.817V42.455z"
        />
      </g>
      <g
        id="Group_Level_Indicator"
        transform="matrix(0.9999999,3.019916E-07,3.019916E-07,-0.9999999,0,112)"
      >
        <path
          fill="#D9D9D9"
          stroke="#7F7F7F"
          stroke-width="0.25"
          d="M22.636,25.45h18.355v5.744H22.636V25.45z"
        />
        <path
          :fill="stateColor"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M15,6.5h6v4h-6V6.5z"
        />
      </g>
      <g
        id="Group_Upstream_Pipe_Flange"
        transform="matrix(0.9999999,3.019916E-07,3.019916E-07,-0.9999999,0,112)"
      >
        <path
          fill="#CCCCCC"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M36.938,110.135H13.966l-2.252-49.211h27.479L36.938,110.135z"
        />

        <linearGradient
          id="LIQUID_PRINT_3_"
          gradientUnits="userSpaceOnUse"
          x1="13.9656"
          y1="26.9707"
          x2="36.9392"
          y2="26.9707"
          gradientTransform="matrix(1 0 0 -1 0 112.5)"
        >
          <stop offset="0.01" style="stop-color: #4d4d4d" />
          <stop offset="0.51" style="stop-color: #f5f5f5" />
          <stop offset="1" style="stop-color: #4d4d4d" />
        </linearGradient>
        <path
          fill="url(#LIQUID_PRINT_3_)"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M13.966,110.135h22.974V60.924H13.966V110.135"
        />
        <path
          fill="#7F7F7F"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M8.673,110.135h33.446v2.365H8.673V110.135z"
        />
      </g>
      <g
        id="Group_Drivershaft_Flange_Float_Switch"
        transform="matrix(0.9999999,3.019916E-07,3.019916E-07,-0.9999999,0,112)"
      >
        <path
          fill="#BFBFBF"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M18.245,69.145h14.302v27.478H18.245V69.145z"
        />
        <path
          fill="#666666"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M19.371,70.158h2.252v5.518h-2.252V70.158z"
        />
        <path
          fill="#666666"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M22.636,70.158h2.253v5.518h-2.253V70.158z"
        />
        <path
          fill="#666666"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M26.015,70.158h2.14v5.518h-2.14V70.158z"
        />
        <path
          fill="#666666"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M29.281,70.158h2.252v5.518h-2.252V70.158z"
        />
        <path
          fill="#666666"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M19.371,76.802h2.252v5.519h-2.252V76.802z"
        />
        <path
          fill="#666666"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M22.636,76.802h2.253v5.519h-2.253V76.802z"
        />
        <path
          fill="#666666"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M26.015,76.802h2.14v5.519h-2.14V76.802z"
        />
        <path
          fill="#666666"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M29.281,76.802h2.252v5.519h-2.252V76.802z"
        />
        <path
          fill="#666666"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M19.371,83.334h2.252v5.518h-2.252V83.334z"
        />
        <path
          fill="#666666"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M22.636,83.334h2.253v5.518h-2.253V83.334z"
        />
        <path
          fill="#666666"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M26.015,83.334h2.14v5.518h-2.14V83.334z"
        />
        <path
          fill="#666666"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M29.281,83.334h2.252v5.518h-2.252V83.334z"
        />
        <path
          fill="#B2B2B2"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M19.259,90.203h12.388v5.293H19.259V90.203z"
        />
      </g>
    </svg>
    <svg
      v-else-if="type === 'gas'"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 53.941 112.5"
      enable-background="new 0 0 53.941 112.5"
      xml:space="preserve"
    >
      <g id="Group_Lower_Tank">
        <linearGradient
          id="GAS_PRINT_1_"
          gradientUnits="userSpaceOnUse"
          x1="0.4514"
          y1="90.9746"
          x2="53.3792"
          y2="90.9746"
        >
          <stop offset="0.01" style="stop-color: #4d4d4d" />
          <stop offset="0.51" style="stop-color: #f5f5f5" />
          <stop offset="1" style="stop-color: #4d4d4d" />
        </linearGradient>
        <polyline
          fill="url(#GAS_PRINT_1_)"
          points="7.433,71.734 0.451,71.734 0.451,76.238 2.816,76.238 2.816,106.193 0.451,106.193    0.451,110.698 0.451,110.699 53.379,110.699 53.379,106.193 51.014,106.193 51.014,76.239 53.379,76.239 53.379,71.734    46.397,71.734 46.397,71.25  "
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M7.433,71.734h38.964"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M0.451,73.986h52.928"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M0.451,108.445h52.928"
        />
      </g>
      <g id="Group_Sump_Tank">
        <linearGradient
          id="GAS_PRINT_2_"
          gradientUnits="userSpaceOnUse"
          x1="0.4514"
          y1="47.5791"
          x2="53.3792"
          y2="47.5791"
        >
          <stop offset="0.01" style="stop-color: #4d4d4d" />
          <stop offset="0.51" style="stop-color: #f5f5f5" />
          <stop offset="1" style="stop-color: #4d4d4d" />
        </linearGradient>
        <path
          fill="url(#GAS_PRINT_2_)"
          d="M46.397,71.25c-0.481,0-0.666,0-1.426,0C45.731,71.25,45.915,71.25,46.397,71.25V28.041h6.982   v-4.617H0.451v4.617h6.981v43.693"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M0.451,25.788h52.928"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M7.433,28.041h38.964"
        />
      </g>
      <g id="Group_Filter">
        <path
          :fill="contentColor"
          d="M8.108,76.238h37.727v29.843H8.108V76.238z"
        />
      </g>
      <g id="Group_Humidifier">
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M16.78,106.193l34.348-29.955"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M21.284,106.193l29.842-25.9"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M25.901,106.193l25.226-21.959"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M30.518,106.193l20.608-18.019"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M35.135,106.193l15.991-13.965"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M39.752,106.193l11.374-10.022"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M44.257,106.193l6.869-5.969"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M48.875,106.193l2.252-2.027"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M12.163,106.193L46.51,76.238"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M7.433,106.193l34.46-29.955"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M2.816,106.193l34.46-29.955"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M2.928,102.027l29.73-25.789"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M2.928,98.086l25.113-21.848"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M2.928,94.031l20.496-17.793"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M2.928,89.979l15.878-13.739"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M2.928,86.036l11.149-9.798"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M2.928,81.982l6.532-5.744"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M2.928,77.928l1.915-1.688"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M37.276,106.193L2.816,76.238"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M32.659,106.193l-29.844-25.9"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M28.042,106.193L2.816,84.234"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M23.424,106.193L2.816,88.176"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M18.919,106.193L2.816,92.229"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M14.302,106.193L2.816,96.171"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M9.685,106.193l-6.869-5.969"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M5.068,106.193l-2.252-2.027"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M41.893,106.193L7.433,76.238"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M46.509,106.193L12.05,76.238"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M51.126,106.193L16.667,76.238"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M51.014,102.027L21.284,76.238"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M51.014,98.086l-25-21.848"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M51.014,94.031L30.631,76.238"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M51.014,89.979l-15.765-13.74"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M51.014,86.036l-11.147-9.798"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M51.014,81.982l-6.531-5.744"
        />
        <path
          fill="none"
          stroke="#4C4C4C"
          stroke-width="0.25"
          d="M51.014,77.928L49.1,76.238"
        />
      </g>
      <g id="Group_Drivershaft">
        <linearGradient
          id="GAS_PRINT_3_"
          gradientUnits="userSpaceOnUse"
          x1="23.4241"
          y1="100.5635"
          x2="30.4055"
          y2="100.5635"
          gradientTransform="matrix(1 0 0 -1 0 112.5)"
        >
          <stop offset="0.01" style="stop-color: #4d4d4d" />
          <stop offset="0.51" style="stop-color: #f5f5f5" />
          <stop offset="1" style="stop-color: #4d4d4d" />
        </linearGradient>
        <path
          fill="url(#GAS_PRINT_3_)"
          d="M23.424,23.423h6.981V0.45h-6.981V23.423"
        />
      </g>
      <g id="Group_Float_Switch">
        <radialGradient
          id="gasPrintHeadsState"
          cx="39.5276"
          cy="94.5947"
          r="5.7432"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0" :style="'stop-color: ' + stateColor" />
          <stop offset="0.6" :style="'stop-color: ' + stateColor" />
          <stop offset="1" style="stop-color: #e0e0e0" />
        </radialGradient>
        <circle
          fill="url(#gasPrintHeadsState)"
          cx="39.528"
          cy="94.595"
          r="5.743"
        />
      </g>
      <g id="Group_Layer_6">
        <path d="M2.253,28.041h3.378v1.126H2.253V28.041z" />
        <path d="M48.199,28.041h3.378v1.126h-3.378V28.041z" />
        <path d="M25.226,28.041h3.378v1.126h-3.378V28.041z" />
        <path d="M37.276,28.041h3.378v1.126h-3.378V28.041z" />
        <path d="M13.176,28.041h3.378v1.126h-3.378V28.041z" />
        <path d="M2.253,22.297h3.378v1.126H2.253V22.297z" />
        <path d="M48.199,22.297h3.378v1.126h-3.378V22.297z" />
        <path d="M25.226,22.297h3.378v1.126h-3.378V22.297z" />
        <path d="M37.276,22.297h3.378v1.126h-3.378V22.297z" />
        <path d="M13.176,22.297h3.378v1.126h-3.378V22.297z" />
        <path d="M2.253,110.698h3.378v1.239H2.253V110.698z" />
        <path d="M48.199,110.698h3.378v1.239h-3.378V110.698z" />
        <path d="M25.226,110.698h3.378v1.239h-3.378V110.698z" />
        <path d="M37.276,110.698h3.378v1.239h-3.378V110.698z" />
        <path d="M13.176,110.698h3.378v1.239h-3.378V110.698z" />
        <path d="M2.253,70.496h3.378v1.238H2.253V70.496z" />
        <path d="M48.199,70.496h3.378v1.238h-3.378V70.496z" />
        <path d="M25.226,70.496h3.378v1.238h-3.378V70.496z" />
        <path d="M37.276,70.496h3.378v1.238h-3.378V70.496z" />
        <path d="M13.176,70.496h3.378v1.238h-3.378V70.496z" />
      </g>
    </svg>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, toRefs } from 'vue';
import color from 'color';

export default defineComponent({
  name: 'PrintHeads',
  props: {
    type: {
      type: String,
      default: 'liquid',
    },
    color: {
      type: String,
      default: '#4d4d4d',
    },
    stateColor: {
      type: String,
      default: '#4d4d4d',
    },
    rotate: {
      type: Number,
      default: 0,
    },
  },
  setup(props) {
    const { type, color: contentColor, stateColor, rotate } = toRefs(props);

    const colorLighten = computed(() => {
      const hslColor = color(contentColor.value);
      return hslColor.alpha(0.55).string();
    });

    return {
      type,
      contentColor,
      stateColor,
      rotate,
      colorLighten,
    };
  },
});
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  transform: v-bind('`rotate(${rotate}deg)`');
  svg {
    width: 100%;
    height: 100%;
  }
}
</style>
