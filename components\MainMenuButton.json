{"id": 1, "version": "0.0.17", "name": {"zh_CN": "Semi按钮"}, "component": "MainMenuButton", "icon": "button", "description": "Semi标准按钮", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "MainMenuButton", "version": "0.0.17", "script": "http://*************:4874/@dcp/component-library@0.0.17/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "group": "DCP", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["width", "height", "text", "icon"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}}, "schema": {"properties": [{"label": {"zh_CN": "基础属性"}, "description": {"zh_CN": "按钮的基础配置属性"}, "content": [{"property": "width", "label": {"text": {"zh_CN": "宽度"}}, "description": {"zh_CN": "按钮的宽度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 140, "widget": {"component": "NumberConfigurator", "props": {"min": 50, "max": 500, "step": 10}}}, {"property": "height", "label": {"text": {"zh_CN": "高度"}}, "description": {"zh_CN": "按钮的高度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 70, "widget": {"component": "NumberConfigurator", "props": {"min": 30, "max": 200, "step": 5}}}, {"property": "text", "label": {"text": {"zh_CN": "按钮文本"}}, "description": {"zh_CN": "按钮显示的文本内容"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "按钮", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "icon", "label": {"text": {"zh_CN": "图标"}}, "description": {"zh_CN": "按钮显示的图标"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "icon-button", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "iconPosition", "label": {"text": {"zh_CN": "图标位置"}}, "description": {"zh_CN": "图标位置"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "top", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "顶部", "value": "top"}, {"label": "左侧", "value": "left"}, {"label": "右侧", "value": "right"}]}}}]}, {"label": {"zh_CN": "样式属性"}, "description": {"zh_CN": "按钮的样式配置属性"}, "content": [{"property": "backgroundColor", "label": {"text": {"zh_CN": "背景颜色"}}, "description": {"zh_CN": "按钮的背景颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#1A7285", "widget": {"component": "ColorConfigurator", "props": {}}}, {"property": "color", "label": {"text": {"zh_CN": "文本颜色"}}, "description": {"zh_CN": "按钮的文本颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#18DBD3", "widget": {"component": "ColorConfigurator", "props": {}}}, {"property": "disabled", "label": {"text": {"zh_CN": "禁用状态"}}, "description": {"zh_CN": "是否禁用按钮"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator", "props": {}}}]}], "events": {"onClick": {"label": {"zh_CN": "点击时触发"}, "description": {"zh_CN": "当按钮被点击时触发的事件"}, "type": "event", "functionInfo": {"params": [], "returns": {}}, "defaultValue": ""}}}, "snippets": [{"name": {"zh_CN": "Semi按钮"}, "icon": "button", "screenshot": "", "snippetName": "MainMenuButton", "schema": {"props": {"width": 140, "height": 70, "text": "Semi按钮", "icon": "mdi:menu", "backgroundColor": "#1A7285", "color": "#18DBD3", "disabled": false}}}], "category": "DCP"}