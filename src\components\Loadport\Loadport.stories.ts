import type { <PERSON><PERSON>, StoryObj } from '@storybook/vue3';
import { ref, reactive, watch } from 'vue';
import { Loadport } from './index';
import { defaultConfig } from './config';

// 创建深拷贝的配置，避免修改原始配置
const getDefaultWaferList = () =>
  JSON.parse(JSON.stringify(defaultConfig.waferList));

const meta = {
  title: 'Equipment/Loadport',
  component: Loadport,
  tags: ['autodocs'],
  argTypes: {
    width: { control: 'number' },
    height: { control: 'number' },
    name: { control: 'text' },
    waferNumber: { control: 'number' },
    splitNumber: { control: 'number' },
    waferShape: {
      control: 'select',
      options: ['square', 'circle'],
    },
    slotNumberStep: { control: 'number' },
    selectedColor: { control: 'color' },
    disabledColor: { control: 'color' },
    defaultColor: { control: 'color' },
    successColor: { control: 'color' },
    errorColor: { control: 'color' },
    wipColor: { control: 'color' },
    waferList: {
      control: false,
      description: '晶圆列表配置，请使用下方的晶圆编辑器进行修改',
    },
  },
  args: {
    width: defaultConfig.width,
    height: defaultConfig.height,
    name: defaultConfig.name,
    waferNumber: defaultConfig.waferNumber,
    splitNumber: defaultConfig.splitNumber || -1,
    waferShape: defaultConfig.waferShape,
    slotNumberStep: defaultConfig.slotNumberStep,
    selectedColor: defaultConfig.selectedColor,
    disabledColor: defaultConfig.disabledColor,
    defaultColor: defaultConfig.defaultColor,
    successColor: defaultConfig.successColor,
    errorColor: defaultConfig.errorColor,
    wipColor: defaultConfig.wipColor,
    waferList: getDefaultWaferList(),
  },
  parameters: {
    docs: {
      description: {
        component:
          'Loadport组件用于显示晶圆装载端口，可以配置不同数量的晶圆槽位，并支持不同状态的颜色显示。',
      },
    },
  },
} satisfies Meta<typeof Loadport>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  name: '基础示例',
  args: {},
};

// 圆形晶圆示例
export const CircleWafers: Story = {
  name: '圆形晶圆',
  args: {
    waferShape: 'circle',
  },
};

// 更多槽位示例
export const MoreWafers: Story = {
  name: '更多槽位',
  args: {
    waferNumber: 25,
    height: 600,
  },
};

// 添加一个带有晶圆编辑器的交互示例
export const WithWaferEditor: Story = {
  name: '晶圆编辑器',
  render: (args) => ({
    components: { Loadport },
    setup() {
      // 使用响应式数据存储晶圆列表
      const waferList = reactive(getDefaultWaferList());
      const waferNumber = ref(args.waferNumber || defaultConfig.waferNumber);
      const waferShape = ref(args.waferShape || defaultConfig.waferShape);
      const slotNumberStep = ref(
        args.slotNumberStep || defaultConfig.slotNumberStep,
      );
      const splitNumber = ref(args.splitNumber || defaultConfig.splitNumber);
      const loadportKey = ref(0);
      // 可用的颜色选项
      const colorOptions = [
        { name: '默认', value: args.defaultColor },
        { name: '选中', value: args.selectedColor },
        { name: '禁用', value: args.disabledColor },
        { name: '成功', value: args.successColor },
        { name: '错误', value: args.errorColor },
        { name: '处理中', value: args.wipColor },
      ];

      // 当前选中的晶圆索引
      const selectedWaferIndex = ref(0);

      // 确保waferList长度与waferNumber一致
      const ensureWaferListLength = () => {
        const currentLength = waferList.length;
        if (currentLength < waferNumber.value) {
          // 添加新的晶圆
          for (let i = currentLength; i < waferNumber.value; i++) {
            waferList.push({
              color: { value: args.defaultColor },
            });
          }
        } else if (currentLength > waferNumber.value) {
          // 移除多余的晶圆
          waferList.splice(waferNumber.value);
        }
      };

      // 设置晶圆颜色
      const setWaferColor = (index: number, color: string) => {
        if (index >= 0 && index < waferList.length) {
          waferList[index].color = { value: color };
          loadportKey.value++;
        }
      };

      // 重置所有晶圆颜色
      const resetAllWafers = () => {
        waferList.forEach((wafer: any, _index: number) => {
          wafer.color = { value: args.defaultColor };
        });
      };

      // 随机设置晶圆颜色
      const randomizeWafers = () => {
        waferList.forEach((wafer: any, _index: number) => {
          const randomColorIndex = Math.floor(
            Math.random() * colorOptions.length,
          );
          wafer.color = { value: colorOptions[randomColorIndex].value };
        });
      };

      // 监听waferNumber变化
      watch(waferNumber, () => {
        ensureWaferListLength();
      });

      // 初始化
      ensureWaferListLength();

      return {
        args,
        waferList,
        loadportKey,
        waferNumber,
        waferShape,
        slotNumberStep,
        splitNumber,
        colorOptions,
        selectedWaferIndex,
        setWaferColor,
        resetAllWafers,
        randomizeWafers,
      };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="display: flex; flex-direction: column; gap: 10px; padding: 16px; border: 1px solid #eee; border-radius: 8px;">
          <h3 style="margin: 0 0 10px 0;">Loadport配置</h3>
          
          <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
            <div style="display: flex; flex-direction: column; gap: 5px;">
              <label>晶圆数量:</label>
              <input type="range" 
                v-model.number="waferNumber" 
                min="1" max="50" step="1" 
                style="width: 100%;">
              <div style="display: flex; justify-content: space-between;">
                <span>1</span>
                <span>{{ waferNumber }}</span>
                <span>50</span>
              </div>
            </div>
            
            <div style="display: flex; flex-direction: column; gap: 5px;">
              <label>分割位置:</label>
              <input type="range" 
                v-model.number="splitNumber" 
                min="-1" :max="waferNumber-1" step="1" 
                style="width: 100%;">
              <div style="display: flex; justify-content: space-between;">
                <span>-1</span>
                <span>{{ splitNumber }}</span>
                <span>{{ waferNumber-1 }}</span>
              </div>
            </div>
            
            <div style="display: flex; flex-direction: column; gap: 5px;">
              <label>槽位编号间隔:</label>
              <input type="range" 
                v-model.number="slotNumberStep" 
                min="0" max="10" step="1" 
                style="width: 100%;">
              <div style="display: flex; justify-content: space-between;">
                <span>0</span>
                <span>{{ slotNumberStep }}</span>
                <span>10</span>
              </div>
            </div>
            
            <div style="display: flex; flex-direction: column; gap: 5px;">
              <label>晶圆形状:</label>
              <select 
                v-model="waferShape" 
                style="padding: 6px; border-radius: 4px; border: 1px solid #ccc;">
                <option value="square">方形</option>
                <option value="circle">圆形</option>
              </select>
            </div>
          </div>
          
          <h3 style="margin: 10px 0;">晶圆编辑器</h3>
          
          <div style="display: flex; flex-direction: column; gap: 10px;">
            <div style="display: flex; gap: 10px; align-items: center;">
              <label>选择晶圆槽位:</label>
              <select 
                v-model.number="selectedWaferIndex" 
                style="padding: 6px; border-radius: 4px; border: 1px solid #ccc;">
                <option v-for="index in waferNumber" :key="index-1" :value="index-1">
                  槽位 {{ String(index).padStart(2, '0') }}
                </option>
              </select>
            </div>
            
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
              <button 
                v-for="color in colorOptions" 
                :key="color.name"
                @click="setWaferColor(selectedWaferIndex, color.value)"
                :style="{
                  padding: '8px 12px', 
                  background: color.value, 
                  color: ['#ffffff', '#000000'][['#ffffff', '#f0f0f0', '#e0e0e0'].includes(color.value) ? 1 : 0], 
                  border: '1px solid #ccc', 
                  borderRadius: '4px', 
                  cursor: 'pointer'
                }">
                {{ color.name }}
              </button>
            </div>
            
            <div style="display: flex; gap: 10px; margin-top: 10px;">
              <button 
                @click="resetAllWafers" 
                style="padding: 8px 16px; background: #9E9E9E; color: white; border: none; border-radius: 4px; cursor: pointer; flex: 1;">
                重置所有晶圆
              </button>
              <button 
                @click="randomizeWafers" 
                style="padding: 8px 16px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer; flex: 1;">
                随机设置颜色
              </button>
            </div>
          </div>
        </div>
        
        <div style="border: 1px solid #eee; border-radius: 8px; padding: 16px; display: flex; justify-content: center;">
          <Loadport 
            :key="loadportKey"
            :width="args.width" 
            :height="args.height"
            :name="args.name"
            :waferNumber="waferNumber"
            :splitNumber="splitNumber"
            :waferShape="waferShape"
            :slotNumberStep="slotNumberStep"
            :selectedColor="args.selectedColor"
            :disabledColor="args.disabledColor"
            :defaultColor="args.defaultColor"
            :successColor="args.successColor"
            :errorColor="args.errorColor"
            :wipColor="args.wipColor"
            :waferList="waferList"
          />
        </div>
        
        <div style="border: 1px solid #eee; border-radius: 8px; padding: 16px;">
          <h3 style="margin: 0 0 10px 0;">当前配置</h3>
          <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; overflow: auto; max-height: 300px;">{{ JSON.stringify({
            waferNumber,
            splitNumber,
            waferShape,
            slotNumberStep,
            waferList: waferList.map(w => ({ color: w.color.value }))
          }, null, 2) }}</pre>
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story:
          '这个示例提供了一个交互式的晶圆编辑器，您可以调整Loadport的各个参数，并为每个晶圆槽位设置不同的颜色状态。',
      },
    },
  },
};

// 不同槽位编号间隔示例
export const SlotNumberStepExample: Story = {
  name: '槽位编号间隔',
  args: {
    slotNumberStep: 5,
    waferNumber: 25,
    height: 500,
    width: 300,
  },
};

// 分割显示示例
export const SplitExample: Story = {
  name: 'Buffer',
  args: {
    waferNumber: 8,
    splitNumber: 3,
    height: 300,
    width: 240,
  },
};
