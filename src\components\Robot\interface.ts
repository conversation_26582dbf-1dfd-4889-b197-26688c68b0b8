export interface SiteConfig {
  name: string;
  lowerArmLength: number;
  rotate: number;
}

export interface ArmConfig {
  // extended: boolean;
  waferNumber: string | null;
  waferColor: string;
  currentSite: string;
  siteList: SiteConfig[];
}

export interface RobotOption {
  armList: ArmConfig[];
  rotate: number;
  scale: number;
  waferShape: string;
  transformX: number;
  transformY: number;
}
