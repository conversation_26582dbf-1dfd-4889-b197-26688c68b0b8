// 物料默认尺寸配置
export const DEFAULT_MATERIAL_SIZES = {
  block: { width: 20, height: 15 },
  cube: { width: 18, height: 18 },
  sphere: { width: 16, height: 16 },
  cylinder: { width: 14, height: 20 },
  tray: { width: 28, height: 12 },
};

// 物料默认颜色配置
export const DEFAULT_MATERIAL_COLORS = {
  block: '#8B4513', // 棕色
  cube: '#FF6B6B', // 红色
  sphere: '#4ECDC4', // 青色
  cylinder: '#95A5A6', // 灰色
  tray: '#E8E8E8', // 浅灰色
};

export const defaultConfig = {
  width: 600,
  height: 500,
  scale: 1,
  left: 250,
  top: 450,
  animateDuration: 1,
  currentPosition: 'position1', // 当前位置
  material: null, // 默认无物料
  positionList: {
    position1: {
      firstArmAngle: -45,
      secondArmAngle: 90,
      thirdArmAngle: 90,
      gripperAngle: 45,
      firstArmLength: 120,
      secondArmLength: 120,
      thirdArmLength: 100,
      gripperWidth: 25,
      gripperHeight: 60,
      gripperAnimate: 0,
    },
    position2: {
      firstArmAngle: 20,
      secondArmAngle: 30,
      thirdArmAngle: 15,
      gripperAngle: 25,
      firstArmLength: 150,
      secondArmLength: 140,
      thirdArmLength: 115,
      gripperWidth: 25,
      gripperHeight: 60,
      gripperAnimate: 1,
      endPosition: 'position1',
    },
    position3: {
      firstArmAngle: 60,
      secondArmAngle: 40,
      thirdArmAngle: -10,
      gripperAngle: 0,
      firstArmLength: 120,
      secondArmLength: 110,
      thirdArmLength: 80,
      gripperWidth: 25,
      gripperHeight: 60,
      gripperAnimate: 1,
    },
  },
};
