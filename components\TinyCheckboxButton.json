{"icon": "checkboxbutton", "name": {"zh_CN": "复选按钮"}, "component": "TinyCheckboxButton", "description": "用于配置不同场景的选项，提供用户可在一组选项中进行多选", "docUrl": "", "screenshot": "", "tags": "", "keywords": "", "devMode": "proCode", "npm": {"package": "@opentiny/vue", "exportName": "CheckboxButton"}, "group": "component", "priority": 1, "schema": {"properties": [{"label": {"zh_CN": "基础信息"}, "description": {"zh_CN": "基础信息"}, "content": [{"property": "modelValue", "label": {"text": {"zh_CN": "绑定值"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {}}, "description": {"zh_CN": "双向绑定的当前选中值"}, "labelPosition": "left"}, {"property": "disabled", "label": {"text": {"zh_CN": "禁用"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否禁用"}, "labelPosition": "left"}, {"property": "checked", "label": {"text": {"zh_CN": "勾选"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "当前是否勾选"}, "labelPosition": "left"}, {"property": "text", "label": {"text": {"zh_CN": "文本"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {}}, "description": {"zh_CN": "按钮文本"}, "labelPosition": "left"}]}], "events": {"onChange": {"label": {"zh_CN": "勾选值改变后将触发"}, "description": {"zh_CN": "勾选值改变后将触发"}, "type": "event", "functionInfo": {"params": [{"name": "value", "type": "string", "defaultValue": "", "description": {"zh_CN": "选中项的值"}}], "returns": {}}, "defaultValue": ""}, "onUpdate:modelValue": {"label": {"zh_CN": "双向绑定的值改变时触发"}, "description": {"zh_CN": "当前选中的值改变时触发"}, "type": "event", "functionInfo": {"params": [{"name": "value", "type": "array", "defaultValue": "", "description": {"zh_CN": "双向绑定的当前选中值"}}], "returns": {}}, "defaultValue": ""}}}, "configure": {"loop": true, "condition": true, "styles": true, "isContainer": true, "isModal": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["text", "size"]}, "contextMenu": {"actions": ["create symbol"], "disable": ["copy", "remove"]}}}