{"component": "TransferV2", "name": {"zh_CN": "传送带"}, "icon": "Transfer", "group": "DCP", "category": "DCP", "description": "", "tags": "", "keywords": "", "doc_url": "", "devMode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "TransferV2", "version": "0.0.55", "script": "http://*************:4874/@dcp/component-library@0.0.55/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "isNullNode": false, "isLayout": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "rootSelector": "", "shortcuts": {"properties": ["width", "height", "speed"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}, "clickCapture": false, "framework": "<PERSON><PERSON>"}, "schema": {"properties": [{"name": "0", "label": {"zh_CN": "基础属性"}, "description": {"zh_CN": "核心功能相关配置"}, "content": [{"property": "width", "label": {"text": {"zh_CN": "宽度"}}, "description": "传送带的宽度", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 500, "widget": {"component": "NumberConfigurator", "props": {"min": 50, "max": 2000, "step": 10}}}, {"property": "height", "label": {"text": {"zh_CN": "高度"}}, "description": "传送带的高度", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 80, "widget": {"component": "NumberConfigurator", "props": {"min": 50, "max": 2000, "step": 10}}}, {"property": "speed", "label": {"text": {"zh_CN": "速度"}}, "description": "传送带的速度 (像素/秒)", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 50, "widget": {"component": "NumberConfigurator", "props": {"step": 1}}}, {"property": "stopAtEnd", "label": {"text": {"zh_CN": "停止在末尾"}}, "description": "物料是否在传送带结尾处停止，而不是循环", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator", "props": {}}}, {"property": "direction", "label": {"text": {"zh_CN": "方向"}}, "description": "传送带的方向", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "forward", "widget": {"component": "SelectConfigurator", "props": {"options": ["forward", "backward", "up", "down"]}}}, {"property": "orientation", "label": {"text": {"zh_CN": "布局方向"}}, "description": "传送带的方向：水平或垂直", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "horizontal", "widget": {"component": "SelectConfigurator", "props": {"options": ["horizontal", "vertical"]}}}]}, {"name": "1", "label": {"zh_CN": "样式属性"}, "description": {"zh_CN": "外观相关配置"}, "content": [{"property": "color", "label": {"text": {"zh_CN": "颜色"}}, "description": "传送带的背景颜色", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#c0c0c0", "widget": {"component": "ColorConfigurator", "props": {}}}]}, {"name": "2", "label": {"zh_CN": "行为属性"}, "description": {"zh_CN": "交互与状态相关配置"}, "content": [{"property": "moving", "label": {"text": {"zh_CN": "是否移动"}}, "description": "传送带是否在移动", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": true, "widget": {"component": "SwitchConfigurator", "props": {}}}]}, {"name": "3", "label": {"zh_CN": "高级属性"}, "description": {"zh_CN": "专业配置项"}, "content": [{"property": "items", "label": {"text": {"zh_CN": "物品列表"}}, "description": "传送带上的物品 (BeltItem[])", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "array", "defaultValue": [], "widget": {"component": "CodeConfigurator", "props": {"language": "json", "height": 150}}}]}], "events": {}, "slots": {}}, "snippets": [{"name": {"zh_CN": "传送带"}, "icon": "TransferV2", "snippetName": "TransferV2", "schema": {"props": {"width": 600, "height": 80, "moving": true, "speed": 50, "direction": "forward", "items": [{"id": 1, "position": 0, "content": "A"}, {"id": 2, "position": 25, "content": "B"}, {"id": 3, "position": 60, "content": "C"}, {"id": 4, "position": 90, "content": "D"}], "color": "#c0c0c0", "orientation": "horizontal"}}}]}