<template>
  <div class="punch-press-container" :style="containerStyle">
    <svg
      :width="width"
      :height="height"
      viewBox="0 0 100 100"
      xmlns="http://www.w3.org/2000/svg"
      class="punch-press-svg"
    >
      <!-- 底座 -->
      <rect
        x="10"
        y="85"
        width="80"
        height="12"
        :fill="baseColor"
        rx="2"
        class="base"
      />

      <!-- 左立柱 -->
      <rect
        x="12"
        y="20"
        width="8"
        height="65"
        :fill="frameColor"
        class="left-column"
      />

      <!-- 右立柱 -->
      <rect
        x="80"
        y="20"
        width="8"
        height="65"
        :fill="frameColor"
        class="right-column"
      />

      <!-- 上梁 -->
      <rect
        x="12"
        y="18"
        width="76"
        height="6"
        :fill="frameColor"
        rx="1"
        class="top-beam"
      />

      <!-- 工作台 -->
      <rect
        x="20"
        y="70"
        width="60"
        height="4"
        :fill="workTableColor"
        rx="1"
        class="work-table"
      />

      <!-- 冲头滑块 -->
      <g :class="punchHeadClass">
        <rect
          x="25"
          y="24"
          width="50"
          height="12"
          :fill="punchHeadColor"
          rx="2"
          class="punch-slider"
        />

        <!-- 冲头 -->
        <rect
          x="48"
          y="36"
          width="4"
          height="30"
          :fill="punchHeadColor"
          class="punch-head"
        />

        <!-- 冲头尖端 -->
        <circle cx="50" cy="68" r="3" :fill="punchTipColor" class="punch-tip" />
      </g>

      <!-- 操作手柄 -->
      <g class="handle">
        <rect x="85" y="45" width="3" height="15" :fill="handleColor" rx="1" />
        <circle cx="89" cy="52" r="4" :fill="handleColor" class="handle-grip" />
      </g>

      <!-- 状态指示灯 -->
      <!-- <circle
        cx="30"
        cy="15"
        r="2"
        :fill="statusLightColor"
        :class="statusLightClass"
        class="status-light"
      /> -->

      <!-- 品牌标识 -->
      <text
        x="50"
        y="94"
        text-anchor="middle"
        font-size="3"
        :fill="textColor"
        font-family="Arial, sans-serif"
        class="brand-text"
      >
        {{ name }}
      </text>
    </svg>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, computed } from 'vue';

export default defineComponent({
  name: 'IsometricPunchPress',
  props: {
    name: {
      type: String,
      default: '冲床',
    },
    width: {
      type: Number,
      default: 100,
    },
    height: {
      type: Number,
      default: 100,
    },
    status: {
      type: String as PropType<'idle' | 'working' | 'error' | 'maintenance'>,
      default: 'idle',
    },
  },
  setup(props) {
    const containerStyle = computed(() => ({
      width: `${props.width}px`,
      height: `${props.height}px`,
    }));

    const statusColors = computed(() => {
      const colorMap = {
        idle: {
          primary: '#4A90A4',
          secondary: '#2C5AA0',
          accent: '#6B7280',
          light: '#22D3EE',
          text: '#374151',
        },
        working: {
          primary: '#22C55E',
          secondary: '#16A34A',
          accent: '#059669',
          light: '#34D399',
          text: '#065F46',
        },
        error: {
          primary: '#EF4444',
          secondary: '#DC2626',
          accent: '#B91C1C',
          light: '#F87171',
          text: '#7F1D1D',
        },
        maintenance: {
          primary: '#F97316',
          secondary: '#EA580C',
          accent: '#C2410C',
          light: '#FB923C',
          text: '#9A3412',
        },
      };
      return colorMap[props.status];
    });

    const baseColor = computed(() => statusColors.value.secondary);
    const frameColor = computed(() => statusColors.value.primary);
    const workTableColor = computed(() => statusColors.value.accent);
    const punchHeadColor = computed(() => statusColors.value.primary);
    const punchTipColor = computed(() => statusColors.value.secondary);
    const handleColor = computed(() => statusColors.value.accent);
    const statusLightColor = computed(() => statusColors.value.light);
    const textColor = computed(() => statusColors.value.text);

    const punchHeadClass = computed(() => ({
      'punch-head-working': props.status === 'working',
    }));

    return {
      containerStyle,
      baseColor,
      frameColor,
      workTableColor,
      punchHeadColor,
      punchTipColor,
      handleColor,
      statusLightColor,
      textColor,
      punchHeadClass,
    };
  },
});
</script>

<style scoped lang="scss">
.punch-press-container {
  display: inline-block;

  .punch-press-svg {
    .base {
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }

    .punch-head-working {
      animation: punchMotion 1.5s ease-in-out infinite;
    }

    .handle-grip {
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    }

    .brand-text {
      opacity: 0.8;
      font-size: 8px;
    }

    // 添加悬停效果
    &:hover {
      .handle-grip {
        transform-origin: center;
        animation: handleWiggle 0.5s ease-in-out;
      }
    }
  }
}

@keyframes punchMotion {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(8px);
  }
}

@keyframes errorBlink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes workingPulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

@keyframes handleWiggle {
  0%,
  100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-5deg);
  }
  75% {
    transform: rotate(5deg);
  }
}
</style>
