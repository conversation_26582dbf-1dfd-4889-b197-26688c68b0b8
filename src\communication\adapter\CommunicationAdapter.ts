/**
 * 通信适配器接口
 * 定义组件库与外部通信系统交互的标准接口
 */

/**
 * 通信适配器接口
 * 所有具体的通信实现（如MQTT、WebSocket等）都应实现此接口
 */
export interface ICommunicationAdapter {
  /**
   * 订阅指定主题
   * @param topic 要订阅的主题
   * @param callback 接收到消息时的回调函数
   * @returns 取消订阅的函数
   */
  subscribe(topic: string, callback: (message: any, topic: string) => void): () => void;
  
  /**
   * 发布消息到指定主题
   * @param topic 目标主题
   * @param message 要发送的消息
   */
  publish(topic: string, message: any): void;
  
  /**
   * 连接状态
   * @returns 是否已连接
   */
  isConnected(): boolean;
}

/**
 * 通信适配器工厂接口
 * 用于创建通信适配器实例
 */
export interface ICommunicationAdapterFactory {
  /**
   * 创建通信适配器实例
   * @returns 通信适配器实例
   */
  createAdapter(): ICommunicationAdapter;
}
