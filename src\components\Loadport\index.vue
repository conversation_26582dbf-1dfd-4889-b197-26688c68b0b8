<template>
  <div class="content">
    <div class="lp-main" :style="stateStyle">
      <div class="name" v-if="name">{{ name }}</div>
      <div class="lp-main_body">
        <template
          v-for="(_wafer, index) in waferNumber"
          :key="JSON.stringify(_wafer)"
        >
          <div class="wafer_slot">
            <div
              class="slot_number"
              v-if="
                slotNumberStep !== 0 &&
                ((index + 1) % slotNumberStep === 0 || index === 0)
              "
            >
              {{ String(index + 1).padStart(2, '0') }}
            </div>
            <div class="slot-rivet_left"></div>
            <div class="slot-rivet_right"></div>
            <div
              :class="['wafer', { circle: waferShape === 'circle' }]"
              :style="{
                backgroundColor:
                  getWaferColor(index)?.value ||
                  getWaferColor(index) ||
                  '#f0faff',
              }"
            ></div>
          </div>
          <div
            v-if="index === splitNumber"
            style="height: 20px; width: 100%"
          ></div>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  toRefs,
  onMounted,
  onBeforeUnmount,
  watch,
  computed,
} from 'vue';
import useComponentState from '@/hooks/useComponentState';
import { defaultConfig } from './config';
export default defineComponent({
  name: 'Loadport',
  props: {
    width: {
      type: Number,
      default: defaultConfig.width,
    },
    height: {
      type: Number,
      default: defaultConfig.height,
    },
    name: {
      type: String,
      default: defaultConfig.name,
    },
    waferNumber: {
      type: Number,
      default: defaultConfig.waferNumber,
    },
    splitNumber: {
      type: Number,
      default: defaultConfig.splitNumber,
    },
    waferList: {
      type: Array as PropType<any[]>,
      default: defaultConfig.waferList,
    },
    waferShape: {
      type: String,
      default: defaultConfig.waferShape,
    },
    slotNumberStep: {
      type: Number,
      default: defaultConfig.slotNumberStep,
    },
    selectedColor: {
      type: String,
      default: defaultConfig.selectedColor,
    },
    disabledColor: {
      type: String,
      default: defaultConfig.disabledColor,
    },
    defaultColor: {
      type: String,
      default: defaultConfig.defaultColor,
    },
    successColor: {
      type: String,
      default: defaultConfig.successColor,
    },
    errorColor: {
      type: String,
      default: defaultConfig.errorColor,
    },
    wipColor: {
      type: String,
      default: defaultConfig.wipColor,
    },
  },

  setup(props) {
    const {
      name,
      waferNumber,
      splitNumber,
      waferList,
      waferShape,
      slotNumberStep,
      selectedColor,
      disabledColor,
      defaultColor,
      successColor,
      errorColor,
      wipColor,
    } = toRefs(props);

    const { stateStyle } = useComponentState({
      state: 'default',
      selectedColor: selectedColor.value,
      disabledColor: disabledColor.value,
      defaultColor: defaultColor.value,
      successColor: successColor.value,
      errorColor: errorColor.value,
      wipColor: wipColor.value,
    });

    const getWaferColor = computed(() => {
      return (waferSlot: number) => {
        return waferList.value[waferSlot]?.color;
      };
    });

    const getWaferHeight = (waferHeight: number) => {
      if (waferHeight > 0 && waferHeight <= 5) {
        return { style: { height: '80px' } };
      } else if (waferHeight > 5 && waferHeight <= 10) {
        return { style: { height: '50px' } };
      } else if (waferHeight > 10 && waferHeight <= 15) {
        return { style: { height: '40px' } };
      } else if (waferHeight > 15 && waferHeight <= 20) {
        return { style: { height: '30px' } };
      } else {
        return {};
      }
    };

    watch(
      () => waferNumber.value,
      (newValue) => {
        getWaferHeight(newValue);
      },
    );

    onMounted(() => {});

    onBeforeUnmount(() => {});

    return {
      name,
      getWaferColor,
      stateStyle,
      waferNumber,
      splitNumber,
      waferList,
      waferShape,
      slotNumberStep,
      selectedColor,
      disabledColor,
      defaultColor,
    };
  },
});
</script>

<style lang="scss" scoped>
.content {
  width: v-bind('width + "px"');
  height: v-bind('height + "px"');
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  .name {
    height: 18px;
    line-height: 18px;
    width: 100%;
    text-align: center;
  }
  .lp-main {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    border: 5px solid #7cccf3;
    box-sizing: border-box;
    &_body {
      cursor: pointer;
      flex: 1;
      display: flex;
      flex-direction: column-reverse;
      justify-content: space-around;
      .wafer_slot {
        flex: 1;
        position: relative;
        .slot_number {
          position: absolute;
          left: 2px;
          top: 50%;
          transform: translateY(-50%);
        }
        .slot-rivet_left,
        .slot-rivet_right {
          position: absolute;
          bottom: 0;
          height: 1px;
          width: 10%;
          background-color: #969696;
        }
        .slot-rivet_left {
          left: 0;
        }
        .slot-rivet_right {
          right: 0;
        }

        .wafer {
          width: 90%;
          position: absolute;
          bottom: 1px;
          height: 3px;
          background-color: #fff;
          left: 50%;
          transform: translateX(-50%);
          &.circle {
            bottom: 3px;
            border-radius: 50%;
          }
        }
      }
    }
  }

  span {
    color: #1d2129;
  }
}
</style>
