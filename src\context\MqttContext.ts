import { InjectionKey, inject, provide } from 'vue';
import type { MqttClient } from 'mqtt';

export const Mqtt<PERSON><PERSON>: InjectionKey<MqttClient> = Symbol('mqtt-client');

/**
 * 提供MQTT客户端实例给组件树
 * @param mqttClient MQTT客户端实例
 */
export function provideMqtt(mqttClient: MqttClient) {
  provide(MqttKey, mqttClient);
}

/**
 * 在组件中使用MQTT客户端实例
 * @returns MQTT客户端实例
 */
export function useMqtt(): MqttClient | undefined {
  return inject(MqttKey);
}
