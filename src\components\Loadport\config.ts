const waferNumber = 12;
const waferList = new Array(waferNumber)
  .fill(null)
  .map((__item) => ({ color: '#2080F0' }));

export const defaultConfig = {
  width: 350,
  height: 465,
  field: '',
  name: 'Loadport',
  state: 'default',
  waferNumber, // 存储容量
  storageCapacity: 1,
  waferIo: null,
  waferStateIo: null,
  showOcr: 0,
  ocrIo: null,
  borderColorIo: null,
  waferList,
  floorHeight: 25,
  needLoop: false,
  waferShape: 'square',
  slotNumberStep: 1,
  splitNumber: -1,
  forSelect: 0,
  selectDisable: 0,
  selectedColor: '#2080F0',
  disabledColor: '#A8A8A8',
  defaultColor: '#f0faff',
  successColor: '#18A058',
  errorColor: '#D03050',
  wipColor: '#F2CD92',
  eventMap: {
    click: {
      tip: '点击',
    },
    load: {
      tip: 'Load',
      eventListener: 'io',
      onlyValuePar: false,
    },
    unload: {
      tip: 'UnLoad',
      eventListener: 'io',
      onlyValuePar: false,
    },
  },
};
