{"component": "DateTime", "name": {"zh_CN": "日期时间"}, "icon": "DateTime", "group": "DCP", "category": "DCP", "description": "", "tags": "", "keywords": "", "doc_url": "", "devMode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "DateTime", "version": "0.0.57", "script": "http://*************:4874/@dcp/component-library@0.0.57/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "isNullNode": false, "isLayout": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "rootSelector": "", "shortcuts": {"properties": ["value", "format"]}, "contextMenu": {"actions": ["copy", "remove", "insert"], "disable": []}}, "schema": {"properties": [{"name": "0", "label": {"zh_CN": "基础属性"}, "description": {"zh_CN": "组件的核心功能配置"}, "content": [{"property": "value", "label": {"text": {"zh_CN": "时间值"}}, "description": "指定的时间值，如果不传则显示当前时间", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": null, "widget": {"component": "InputConfigurator", "props": {"placeholder": "请输入时间值"}}}, {"property": "format", "label": {"text": {"zh_CN": "时间格式"}}, "description": "时间显示格式", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "yyyy-MM-dd HH:mm:ss", "widget": {"component": "InputConfigurator", "props": {"placeholder": "请输入时间格式"}}}]}]}, "snippets": [{"name": {"zh_CN": "日期时间"}, "icon": "DateTime", "snippetName": "DateTime", "schema": {"props": {"value": null, "format": "HH:mm:ss", "realTime": true, "interval": 1000}}}]}