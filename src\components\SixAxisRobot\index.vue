<template>
  <div class="robot-arm-container">
    <canvas ref="robotArmCanvas"></canvas>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  ref,
  watch,
  reactive,
  computed,
  toRefs,
  PropType,
} from 'vue';
import * as PIXI from 'pixi.js';
import gsap from 'gsap';
import { isEqual } from 'lodash-es';
import { useSucker } from './hooks/useSucker';
import { defaultConfig } from './config';
import { useComponentState } from '@/communication/hooks/useComponentState';
import { createComponentProps } from '@/context/ComponentContext';

export interface MaterialConfig {
  type: 'block' | 'cube' | 'sphere' | 'cylinder' | 'tray';
  color?: string;
  size?: {
    width?: number;
    height?: number;
  };
  label?: string; // 物料标识
}

// 物料输入类型：支持对象、字符串或null
export type MaterialInput = MaterialConfig | string | null;

export interface AxisConfig {
  firstArmAngle: number;
  secondArmAngle: number;
  thirdArmAngle: number;
  gripperAngle: number;
  firstArmLength: number;
  secondArmLength: number;
  thirdArmLength: number;
  gripperWidth: number; // 爪子宽度
  gripperHeight: number; // 爪子高度
  endPosition?: string;
}

export default defineComponent({
  name: 'SixAxisRobot',
  props: createComponentProps({
    width: {
      type: Number,
      default: defaultConfig.width,
    },
    height: {
      type: Number,
      default: defaultConfig.height,
    },
    scale: {
      type: Number,
      default: 1,
    },
    left: {
      type: Number,
      default: defaultConfig.left,
    },
    top: {
      type: Number,
      default: defaultConfig.top,
    },
    animateDuration: {
      type: Number,
      default: defaultConfig.animateDuration,
    },
    currentPosition: {
      type: String,
      default: defaultConfig.currentPosition,
    },
    positionList: {
      type: Object as PropType<Record<string, AxisConfig>>,
      default: () => defaultConfig.positionList,
    },
    material: {
      type: [Object, String] as PropType<MaterialInput>,
      default: () => defaultConfig.material,
      description:
        '物料配置：可以是对象 {type, color, size, label} 或字符串 "block"/"cube"等',
    },
  }),
  setup(props) {
    const {
      width,
      height,
      scale: _scale,
      animateDuration,
      positionList,
      partId,
      left,
      top,
      currentPosition,
      material,
    } = toRefs(props);

    const robotArmCanvas = ref<HTMLCanvasElement | null>(null);
    let app: PIXI.Application;
    let firstArm: PIXI.Graphics;
    let secondArm: PIXI.Graphics;
    let thirdArm: PIXI.Graphics;

    // 添加角度转弧度的工具函数
    const degToRad = (degrees: number) => {
      return degrees * (Math.PI / 180);
    };

    // 使用组件状态Hook（响应式工厂函数版本）
    const { state } = useComponentState(
      partId.value,
      () => ({
        left: left.value,
        top: top.value,
        currentPosition: currentPosition.value,
        material: material.value,
      }),
      props.dynamicPartInfo,
    );

    // 创建统一的material computed，优先使用state中的值（MQTT更新后的值）
    const currentMaterial = computed(() => {
      // 如果state中有material且不为undefined/null，使用state中的值
      if (state.material !== undefined && state.material !== null) {
        return state.material;
      }
      // 否则使用props中的初始值
      return material.value;
    });

    // 使用统一的material来源
    const { suctionContainer: gripper } = useSucker(currentMaterial);

    onMounted(() => {
      initPixiApp();
      createRobotArm();
    });

    const initPixiApp = () => {
      app = new PIXI.Application({
        width: width.value,
        height: height.value,
        backgroundColor: 0x000000,
        backgroundAlpha: 0,
        view: robotArmCanvas.value as HTMLCanvasElement,
        antialias: true,
      });
    };

    const createRobotArm = () => {
      // 绘制底座
      const base = new PIXI.Graphics();
      base.beginFill(0x292327); // 深灰色
      base.drawRect(-20, 0, 120, 25);
      base.endFill();

      // 上层较小的矩形
      base.beginFill(0x3e3e3e); // 稍浅的蓝灰色
      base.drawRect(0, -25, 80, 25);
      base.endFill();

      // 底座主体
      base.beginFill(0x292327);
      base.drawRect(20, -75, 40, 50);
      base.endFill();

      // 底座主体上的圆形
      base.beginFill(0x292327);
      base.drawCircle(40, -75, 20);
      base.endFill();

      // 绘制第一节臂
      firstArm = new PIXI.Graphics();
      firstArm.lineStyle(4, 0x292327);
      firstArm.beginFill(0xff5d03);
      firstArm.drawRoundedRect(
        -17.5,
        -currentPositionData.value.firstArmLength + 15, // 调整长度
        35,
        currentPositionData.value.firstArmLength, // 使用 state 中的长度
        20,
      );
      firstArm.endFill();

      // 添加橙色圆形轮廓
      firstArm.lineStyle(3, 0x292327);
      firstArm.drawCircle(0, -1, 9);

      // 添加小实心圆
      firstArm.lineStyle(0);
      firstArm.beginFill(0x292327);
      firstArm.drawCircle(0, -1, 5);
      firstArm.endFill();

      firstArm.position.set(40, -70);

      // 绘制第二节臂
      secondArm = new PIXI.Graphics();
      secondArm.lineStyle(4, 0x292327);
      secondArm.beginFill(0xa83302);
      secondArm.drawRoundedRect(
        -17.5,
        -currentPositionData.value.secondArmLength + 15,
        35,
        currentPositionData.value.secondArmLength,
        20,
      );
      secondArm.endFill();

      // 添加圆形轮廓
      secondArm.lineStyle(3, 0x292327);
      secondArm.drawCircle(0, -1, 9);

      // 添加小实心圆
      secondArm.lineStyle(0);
      secondArm.beginFill(0x292327);
      secondArm.drawCircle(0, -1, 5);
      secondArm.endFill();

      secondArm.position.set(0, -currentPositionData.value.firstArmLength + 30);

      // 绘制第三节臂
      thirdArm = new PIXI.Graphics();
      thirdArm.lineStyle(3, 0x292327);
      thirdArm.beginFill(0xff5d03);
      thirdArm.drawRoundedRect(
        -15,
        -currentPositionData.value.thirdArmLength + 15,
        30,
        currentPositionData.value.thirdArmLength,
        18,
      );
      thirdArm.endFill();

      // 添加圆形轮廓
      thirdArm.lineStyle(3, 0x292327);
      thirdArm.drawCircle(0, 1, 9);

      // 添加小实心圆
      thirdArm.lineStyle(0);
      thirdArm.beginFill(0x292327);
      thirdArm.drawCircle(0, 1, 5);
      thirdArm.endFill();

      thirdArm.endFill();

      thirdArm.position.set(0, -currentPositionData.value.secondArmLength + 30);

      gripper.value.position.set(
        0,
        -currentPositionData.value.thirdArmLength + 30,
      );
      gripper.value.rotation = degToRad(currentPositionData.value.gripperAngle);

      thirdArm.addChild(gripper.value);

      secondArm.addChild(thirdArm);
      firstArm.addChild(secondArm);
      base.addChild(firstArm);
      // 设置基础容器位置
      base.position.set(state.left, state.top);
      // 将基础容器添加到舞台
      app.stage.addChild(base);

      firstArm.rotation = degToRad(currentPositionData.value.firstArmAngle);
      secondArm.rotation = degToRad(currentPositionData.value.secondArmAngle);
      thirdArm.rotation = degToRad(currentPositionData.value.thirdArmAngle);
    };

    // 修改动画函数
    const animateToNewPosition = (newPosition: AxisConfig) => {
      if (!currentPositionData.value) {
        return;
      }
      const tl = gsap.timeline({
        repeat: 0,
        yoyo: false,
      });

      // 第一节臂动画
      tl.to(armStates.firstArm, {
        length: newPosition.firstArmLength,
        duration: animateDuration.value,
        ease: 'power1.inOut',
        onUpdate: () => {
          firstArm.clear();
          firstArm.lineStyle(4, 0x292327);
          firstArm.beginFill(0xff5d03);
          firstArm.drawRoundedRect(
            -17.5,
            -armStates.firstArm.length + 15,
            35,
            armStates.firstArm.length,
            20,
          );
          firstArm.endFill();

          firstArm.lineStyle(3, 0x292327);
          firstArm.drawCircle(0, -1, 9);
          firstArm.lineStyle(0);
          firstArm.beginFill(0x292327);
          firstArm.drawCircle(0, -1, 5);
          firstArm.endFill();
        },
      }).to(
        firstArm,
        {
          rotation: degToRad(newPosition.firstArmAngle),
          duration: animateDuration.value,
          ease: 'power1.inOut',
        },
        `-=${animateDuration.value}`,
      );

      // 第二节臂动画
      tl.to(
        armStates.secondArm,
        {
          length: newPosition.secondArmLength,
          duration: animateDuration.value,
          ease: 'power1.inOut',
          onUpdate: () => {
            secondArm.clear();
            secondArm.lineStyle(4, 0x292327);
            secondArm.beginFill(0xa83302);
            secondArm.drawRoundedRect(
              -17.5,
              -armStates.secondArm.length + 15,
              35,
              armStates.secondArm.length,
              20,
            );
            secondArm.endFill();

            secondArm.lineStyle(3, 0x292327);
            secondArm.drawCircle(0, -1, 9);
            secondArm.lineStyle(0);
            secondArm.beginFill(0x292327);
            secondArm.drawCircle(0, -1, 5);
            secondArm.endFill();
            secondArm.position.set(0, -armStates.firstArm.length + 30);
          },
        },
        `-=${animateDuration.value}`,
      ).to(
        secondArm,
        {
          rotation: degToRad(newPosition.secondArmAngle),
          duration: animateDuration.value,
          ease: 'power1.inOut',
        },
        `-=${animateDuration.value}`,
      );

      // 第三节臂动画
      tl.to(
        armStates.thirdArm,
        {
          length: newPosition.thirdArmLength,
          duration: animateDuration.value,
          ease: 'power1.inOut',
          onUpdate: () => {
            thirdArm.clear();
            thirdArm.lineStyle(3, 0x292327);
            thirdArm.beginFill(0xff5d03);
            thirdArm.drawRoundedRect(
              -15,
              -armStates.thirdArm.length + 15,
              30,
              armStates.thirdArm.length,
              18,
            );
            thirdArm.endFill();

            thirdArm.lineStyle(3, 0x292327);
            thirdArm.drawCircle(0, 1, 9);
            thirdArm.lineStyle(0);
            thirdArm.beginFill(0x292327);
            thirdArm.drawCircle(0, 1, 5);
            thirdArm.endFill();
            thirdArm.position.set(0, -armStates.secondArm.length + 30);
          },
        },
        `-=${animateDuration.value}`,
      ).to(
        thirdArm,
        {
          rotation: degToRad(newPosition.thirdArmAngle),
          duration: animateDuration.value,
          ease: 'power1.inOut',
        },
        `-=${animateDuration.value}`,
      );

      // 吸盘动画
      tl.to(
        gripper.value,
        {
          rotation: degToRad(newPosition.gripperAngle),
          duration: animateDuration.value,
          ease: 'power1.inOut',
          onUpdate: () => {
            gripper.value.position.set(0, -armStates.thirdArm.length + 30);
          },
        },
        `-=${animateDuration.value}`,
      );

      tl.to(
        gripper.value.suction,
        {
          y: 10,
          duration: 0.5,
          ease: 'power2.inOut',
        },
        '+=0.5',
      );
      tl.to(
        gripper.value.innerCircle,
        {
          y: 5,
          duration: 0.5,
          ease: 'power2.inOut',
        },
        '<', // 与吸盘动画同时进行
      );

      // 吸盘动画
      tl.to(
        gripper.value,
        {
          rotation: degToRad(newPosition.gripperAngle),
          duration: animateDuration.value,
          ease: 'power1.inOut',
          onUpdate: () => {
            gripper.value.position.set(0, -armStates.thirdArm.length + 30);
          },
        },
        `-=${animateDuration.value}`,
      );

      tl.to(
        gripper.value.suction,
        {
          y: 0,
          duration: 0.5,
          ease: 'power2.inOut',
        },
        '+=0.5',
      );
      tl.to(
        gripper.value.innerCircle,
        {
          y: 0,
          duration: 0.5,
          ease: 'power2.inOut',
        },
        '<', // 与吸盘动画同时进行
      );
    };

    // 使用响应式状态计算当前位置数据
    const currentPositionData = computed(() => {
      return positionList.value[state.currentPosition];
    });

    const armStates = reactive({
      firstArm: {
        length: currentPositionData.value.firstArmLength,
      },
      secondArm: {
        length: currentPositionData.value.secondArmLength,
      },
      thirdArm: {
        length: currentPositionData.value.thirdArmLength,
      },
    });

    // 监听当前位置变化
    watch(
      currentPositionData,
      (newVal, oldVal) => {
        if (!isEqual(newVal, oldVal)) {
          animateToNewPosition(currentPositionData.value);
        }
      },
      {
        deep: true,
      },
    );

    // 监听状态中的位置变化
    watch(
      () => [state.top, state.left],
      ([newTop, newLeft], [_oldTop, _oldLeft]) => {
        if (!app?.stage) return;

        const base = app.stage.children[0];
        if (base) {
          gsap.to(base, {
            x: newLeft,
            y: newTop,
            duration: animateDuration.value,
            ease: 'power1.inOut',
          });
        }
      },
    );

    return {
      robotArmCanvas,
    };
  },
});
</script>

<style scoped>
.robot-arm-container {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
