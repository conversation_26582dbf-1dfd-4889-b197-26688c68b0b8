<template>
  <div class="event-detail">
    <div class="detail-section">
      <h4>基本信息</h4>
      <div class="detail-grid">
        <div class="detail-item">
          <label>事件ID:</label>
          <span class="value">{{ event.Id }}</span>
        </div>
        <div class="detail-item">
          <label>时间戳:</label>
          <span class="value">{{ formatTimestamp(event.Timestamp) }}</span>
        </div>
        <div class="detail-item">
          <label>级别:</label>
          <el-tag :type="getLevelType(event.Level)" size="small">
            {{ event.Level }}
          </el-tag>
        </div>
        <div class="detail-item">
          <label>事件类型:</label>
          <span class="value">{{ event.EventType }}</span>
        </div>
        <div v-if="event.TraceId" class="detail-item">
          <label>追踪ID:</label>
          <span class="value">{{ event.TraceId }}</span>
        </div>
        <div v-if="event.SpanId" class="detail-item">
          <label>跨度ID:</label>
          <span class="value">{{ event.SpanId }}</span>
        </div>
        <div v-if="event.SpanKind" class="detail-item">
          <label>跨度类型:</label>
          <span class="value">{{ event.SpanKind }}</span>
        </div>
      </div>
    </div>

    <div class="detail-section">
      <h4>消息内容</h4>
      <div class="message-container">
        <div class="detail-item">
          <label>消息文本:</label>
          <div class="message-template">
            {{ getMessageText(event.MessageTemplateTokens) }}
          </div>
        </div>
        <div
          v-if="
            event.MessageTemplateTokens &&
            event.MessageTemplateTokens.length > 1
          "
          class="detail-item"
        >
          <label>消息片段:</label>
          <div class="tokens-container">
            <el-tag
              v-for="(token, index) in event.MessageTemplateTokens"
              :key="index"
              size="small"
              class="token-tag"
            >
              {{ token.Text }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>

    <div
      v-if="event.Properties && event.Properties.length > 0"
      class="detail-section"
    >
      <h4>属性信息 ({{ event.Properties.length }} 个)</h4>
      <div class="properties-container">
        <div class="properties-grid">
          <div
            v-for="property in event.Properties"
            :key="property.Name"
            class="property-item"
          >
            <div class="property-key">{{ property.Name }}</div>
            <div class="property-value">
              <code>{{ formatPropertyValue(property.Value) }}</code>
            </div>
          </div>
        </div>
        <div class="json-toggle">
          <el-button size="small" @click="showRawJson = !showRawJson">
            {{ showRawJson ? '隐藏' : '显示' }} JSON 格式
          </el-button>
        </div>
        <div v-if="showRawJson" class="json-content">
          <pre>{{
            JSON.stringify(convertPropertiesToObject(event.Properties), null, 2)
          }}</pre>
        </div>
      </div>
    </div>

    <div v-if="event.Exception" class="detail-section">
      <h4>异常信息</h4>
      <div class="exception-container">
        <pre class="exception-content">{{ event.Exception }}</pre>
      </div>
    </div>

    <div v-if="event.Links" class="detail-section">
      <h4>链接信息</h4>
      <div class="links-container">
        <div class="detail-item">
          <label>自身链接:</label>
          <span class="value link">{{ event.Links.Self }}</span>
        </div>
        <div class="detail-item">
          <label>组链接:</label>
          <span class="value link">{{ event.Links.Group }}</span>
        </div>
      </div>
    </div>

    <div class="detail-actions">
      <el-button @click="copyToClipboard('event')" size="small">
        <el-icon><Icon icon="material-symbols:content-copy" /></el-icon>
        复制事件JSON
      </el-button>
      <el-button @click="copyToClipboard('message')" size="small">
        <el-icon><Icon icon="material-symbols:edit" /></el-icon>
        复制消息
      </el-button>
      <el-button
        v-if="event.Properties && event.Properties.length > 0"
        @click="copyToClipboard('properties')"
        size="small"
      >
        <el-icon><Icon icon="material-symbols:sell" /></el-icon>
        复制属性
      </el-button>
      <el-button
        v-if="event.Exception"
        @click="copyToClipboard('exception')"
        size="small"
        type="warning"
      >
        <el-icon><Icon icon="material-symbols:warning" /></el-icon>
        复制异常
      </el-button>
      <el-button @click="$emit('close')" type="primary" size="small">
        关闭
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage, ElTag, ElButton, ElIcon } from 'element-plus';
import { Icon } from '@iconify/vue';

// Props 定义
interface SeqProperty {
  Name: string;
  Value: any;
}

interface MessageTemplateToken {
  Text: string;
}

interface SeqLinks {
  Self: string;
  Group: string;
}

interface SeqEvent {
  Id: string;
  Timestamp: string;
  Level: string;
  Properties: SeqProperty[];
  MessageTemplateTokens: MessageTemplateToken[];
  EventType: string;
  TraceId?: string;
  SpanId?: string;
  SpanKind?: string;
  Links: SeqLinks;
  Exception?: string;
}

interface Props {
  event: SeqEvent;
}

defineProps<Props>();

// 事件定义
defineEmits<{
  close: [];
}>();

// 组件状态
const showRawJson = ref(false);

// 方法
const formatTimestamp = (timestamp: string): string => {
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZoneName: 'short',
  });
};

const getLevelType = (level: string) => {
  const levelMap: Record<string, any> = {
    Verbose: 'info',
    Debug: 'info',
    Information: 'success',
    Warning: 'warning',
    Error: 'danger',
    Fatal: 'danger',
  };
  return levelMap[level] || 'info';
};

const getMessageText = (tokens: MessageTemplateToken[]): string => {
  if (!tokens || tokens.length === 0) return '';
  return tokens.map((token) => token.Text).join('');
};

const formatPropertyValue = (value: any): string => {
  if (value === null) return 'null';
  if (value === undefined) return 'undefined';
  if (typeof value === 'string') return value;
  if (typeof value === 'boolean') return value.toString();
  if (typeof value === 'number') return value.toString();
  if (Array.isArray(value)) return `[${value.length} items]`;
  if (typeof value === 'object') return JSON.stringify(value);
  return String(value);
};

const convertPropertiesToObject = (
  properties: SeqProperty[],
): Record<string, any> => {
  const obj: Record<string, any> = {};
  properties.forEach((prop) => {
    obj[prop.Name] = prop.Value;
  });
  return obj;
};

const copyToClipboard = async (
  type: 'event' | 'message' | 'properties' | 'exception',
) => {
  let textToCopy = '';
  const props = defineProps<Props>();

  switch (type) {
    case 'event':
      textToCopy = JSON.stringify(props.event, null, 2);
      break;
    case 'message':
      textToCopy = getMessageText(props.event.MessageTemplateTokens);
      break;
    case 'properties':
      textToCopy = JSON.stringify(
        convertPropertiesToObject(props.event.Properties),
        null,
        2,
      );
      break;
    case 'exception':
      textToCopy = props.event.Exception || '';
      break;
  }

  try {
    await navigator.clipboard.writeText(textToCopy);
    ElMessage.success('已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    ElMessage.error('复制失败');
  }
};
</script>

<style scoped>
.event-detail {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  background: #ffffff;
  color: #303133;
}

.detail-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e4e7ed;
}

.detail-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.detail-section h4 {
  color: #409eff;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-grid {
  display: grid;
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.detail-item label {
  min-width: 80px;
  color: #606266;
  font-weight: 500;
  font-size: 13px;
}

.detail-item .value {
  flex: 1;
  word-break: break-word;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  font-size: 13px;
}

.detail-item .value.link {
  color: #409eff;
  text-decoration: underline;
  cursor: pointer;
}

.message-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-template {
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
  color: #303133;
  max-height: 200px;
  overflow-y: auto;
}

.tokens-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.token-tag {
  margin: 0;
}

.properties-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.properties-grid {
  display: grid;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
  padding: 12px;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.property-item {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}

.property-item:last-child {
  border-bottom: none;
}

.property-key {
  font-weight: 600;
  color: #409eff;
  font-size: 13px;
  word-break: break-word;
}

.property-value {
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  font-size: 12px;
  color: #303133;
}

.property-value code {
  background: #ffffff;
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid #e4e7ed;
  word-break: break-all;
}

.json-toggle {
  display: flex;
  justify-content: flex-end;
}

.json-content {
  background: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  max-height: 400px;
  overflow: auto;
}

.json-content pre {
  margin: 0;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-word;
}

.exception-container {
  background: #fef0f0;
  border: 1px solid #fde2e2;
  border-radius: 6px;
  padding: 16px;
}

.exception-content {
  margin: 0;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #f56c6c;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 400px;
  overflow-y: auto;
}

.links-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e4e7ed;
  flex-wrap: wrap;
}

/* 滚动条样式 */
.properties-grid::-webkit-scrollbar,
.json-content::-webkit-scrollbar,
.exception-content::-webkit-scrollbar,
.message-template::-webkit-scrollbar {
  width: 6px;
}

.properties-grid::-webkit-scrollbar-track,
.json-content::-webkit-scrollbar-track,
.exception-content::-webkit-scrollbar-track,
.message-template::-webkit-scrollbar-track {
  background: #f5f7fa;
}

.properties-grid::-webkit-scrollbar-thumb,
.json-content::-webkit-scrollbar-thumb,
.exception-content::-webkit-scrollbar-thumb,
.message-template::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.properties-grid::-webkit-scrollbar-thumb:hover,
.json-content::-webkit-scrollbar-thumb:hover,
.exception-content::-webkit-scrollbar-thumb:hover,
.message-template::-webkit-scrollbar-thumb:hover {
  background: #a8abb2;
}
</style>
