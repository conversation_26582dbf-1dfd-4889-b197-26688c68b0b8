/**
 * 消息转换器接口
 * 定义如何将接收到的消息转换为组件状态更新
 */

/**
 * 消息转换器接口
 * 宿主应用实现此接口来定义消息到状态的转换逻辑
 */
export interface IMessageTransformer {
  /**
   * 转换消息为状态更新
   * @param message 接收到的消息
   * @param componentType 组件类型（可选）
   * @param partId 部件ID
   * @returns 状态更新对象
   */
  transformMessage(message: any, partId: string): Record<string, any>;
}

/**
 * 默认消息转换器
 * 当宿主未提供自定义转换器时使用
 */
export class DefaultMessageTransformer implements IMessageTransformer {
  /**
   * 转换消息为状态更新
   * 默认实现假设消息格式为 { props: { ... } }
   * @param message 接收到的消息
   * @returns 状态更新对象
   */
  transformMessage(message: any): Record<string, any> {
    if (message && typeof message === 'object') {
      // 默认假设消息中有props字段包含状态更新
      if (message.props && typeof message.props === 'object') {
        return message.props;
      }
      
      // 如果没有props字段，尝试直接使用消息对象
      return { ...message };
    }
    
    return {};
  }
}
