{"name": {"zh_CN": "走马灯子项"}, "component": "TinyCarouselItem", "icon": "carouselitem", "description": "常用于一组图片或卡片轮播，当内容空间不足时，可以用走马灯的形式进行收纳，进行轮播展现。", "docUrl": "", "screenshot": "", "tags": "", "keywords": "", "devMode": "proCode", "npm": {"package": "@opentiny/vue", "exportName": "CarouselItem"}, "group": "component", "category": "容器组件", "priority": 2, "schema": {"properties": [{"label": {"zh_CN": "基础信息"}, "description": {"zh_CN": "基础信息"}, "content": [{"property": "name", "label": {"text": {"zh_CN": "名称"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {}}, "description": {"zh_CN": "幻灯片的名字，可用作 setActiveItem 的参数"}, "labelPosition": "left"}, {"property": "title", "label": {"text": {"zh_CN": "标题"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {}}, "description": {"zh_CN": "幻灯片的标题"}, "labelPosition": "left"}, {"property": "indicator-position", "label": {"text": {"zh_CN": "指示器位置"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "ButtonGroupConfigurator", "props": {"options": [{"label": "outside", "value": "outside"}, {"label": "none", "value": "none"}]}}, "description": {"zh_CN": "指示器的位置"}, "labelPosition": "left"}]}], "events": {}}, "configure": {"loop": true, "condition": true, "styles": true, "isContainer": true, "isModal": false, "nestingRule": {"childWhitelist": [], "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["disabled", "size"]}, "contextMenu": {"actions": ["create symbol"], "disable": ["copy", "remove"]}}}