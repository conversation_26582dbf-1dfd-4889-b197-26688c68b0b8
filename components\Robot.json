{"id": 1, "version": "0.0.17", "name": {"zh_CN": "机器人"}, "component": "Robot", "icon": "robot-a", "description": "可配置的多关节机器人组件，支持多个机械臂和晶圆操作", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "Robot", "version": "0.0.17", "script": "http://*************:4874/@dcp/component-library@0.0.17/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "group": "DCP", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["transformX", "transformY", "scale", "rotate"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}}, "schema": {"properties": [{"label": {"zh_CN": "位置与变换"}, "description": {"zh_CN": "机器人的位置、缩放和旋转配置"}, "content": [{"property": "transformX", "label": {"text": {"zh_CN": "X轴位置"}}, "description": {"zh_CN": "机器人在X轴上的位置"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 0, "widget": {"component": "NumberConfigurator", "props": {"step": 10}}}, {"property": "transformY", "label": {"text": {"zh_CN": "Y轴位置"}}, "description": {"zh_CN": "机器人在Y轴上的位置"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 0, "widget": {"component": "NumberConfigurator", "props": {"step": 10}}}, {"property": "scale", "label": {"text": {"zh_CN": "缩放比例"}}, "description": {"zh_CN": "机器人的整体缩放比例"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 1, "widget": {"component": "NumberConfigurator", "props": {"min": 0.1, "max": 3, "step": 0.1}}}, {"property": "rotate", "label": {"text": {"zh_CN": "旋转角度"}}, "description": {"zh_CN": "机器人的整体旋转角度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 0, "widget": {"component": "NumberConfigurator", "props": {"min": 0, "max": 360, "step": 15}}}]}, {"label": {"zh_CN": "机械臂配置"}, "description": {"zh_CN": "机器人机械臂的配置"}, "content": [{"property": "armList", "label": {"text": {"zh_CN": "机械臂列表"}}, "description": {"zh_CN": "机器人的机械臂配置列表"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "object", "defaultValue": [{"waferNumber": null, "waferColor": "#0088ff", "currentSite": "None", "siteList": [{"name": "", "lowerArmLength": 50, "rotate": 0}]}, {"waferNumber": null, "waferColor": "#0088ff", "currentSite": "None", "siteList": [{"name": "", "lowerArmLength": 50, "rotate": 0}]}], "widget": {"component": "CodeConfigurator", "props": {"language": "json", "height": 300}}}]}], "events": {"onArmMove": {"label": {"zh_CN": "机械臂移动时触发"}, "description": {"zh_CN": "当机械臂移动到新位置时触发的事件"}, "type": "event", "functionInfo": {"params": [{"name": "armIndex", "type": "Number", "defaultValue": "", "description": {"zh_CN": "机械臂索引"}}, {"name": "siteName", "type": "String", "defaultValue": "", "description": {"zh_CN": "目标位置名称"}}], "returns": {}}, "defaultValue": ""}, "onWaferChange": {"label": {"zh_CN": "晶圆变化时触发"}, "description": {"zh_CN": "当晶圆信息发生变化时触发的事件"}, "type": "event", "functionInfo": {"params": [{"name": "armIndex", "type": "Number", "defaultValue": "", "description": {"zh_CN": "机械臂索引"}}, {"name": "waferInfo", "type": "Object", "defaultValue": "", "description": {"zh_CN": "晶圆信息"}}], "returns": {}}, "defaultValue": ""}}}, "snippets": [{"name": {"zh_CN": "机械臂 A"}, "icon": "robot-a", "screenshot": "", "snippetName": "Robot", "schema": {"props": {"transformX": 0, "transformY": 0, "scale": 1, "rotate": 0, "armList": [{"waferNumber": null, "waferColor": "#0088ff", "currentSite": "None", "siteList": [{"name": "Position1", "lowerArmLength": 50, "rotate": 0}]}, {"waferNumber": null, "waferColor": "#0088ff", "currentSite": "None", "siteList": [{"name": "Position1", "lowerArmLength": 50, "rotate": 0}]}]}}}], "category": "DCP"}