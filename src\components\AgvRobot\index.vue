<template>
  <div class="robot-arm-container">
    <canvas ref="robotArmCanvas"></canvas>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  onBeforeUnmount,
  ref,
  watch,
  reactive,
  computed,
  toRefs,
  PropType,
} from 'vue';
import * as PIXI from 'pixi.js';
import gsap from 'gsap';
import { isEqual } from 'lodash-es';
import { useSucker } from '../SixAxisRobot/hooks/useSucker';
import { defaultConfig } from '../SixAxisRobot/config';
import { MaterialInput } from '../SixAxisRobot/index.vue';
import { useComponentState } from '@/communication/hooks/useComponentState';
import { createComponentProps } from '@/context/ComponentContext';

export interface AxisConfig {
  firstArmAngle: number;
  secondArmAngle: number;
  thirdArmAngle: number;
  gripperAngle: number;
  firstArmLength: number;
  secondArmLength: number;
  thirdArmLength: number;
  gripperWidth: number; // 爪子宽度
  gripperHeight: number; // 爪子高度
  endPosition?: string;
}

export default defineComponent({
  name: 'AgvRobot',
  props: createComponentProps({
    width: {
      type: Number,
      default: defaultConfig.width,
    },
    height: {
      type: Number,
      default: defaultConfig.height,
    },
    scale: {
      type: Number,
      default: 1,
    },
    currentStation: {
      type: String,
      default: 'position1',
    },
    stationList: {
      type: Object as PropType<Record<string, { left: number; top: number }>>,
      default: () => defaultConfig.positionList,
    },
    // left: {
    //   type: Number,
    //   default: -150,
    // },
    // top: {
    //   type: Number,
    //   default: 130,
    // },
    animateDuration: {
      type: Number,
      default: defaultConfig.animateDuration,
    },
    currentPosition: {
      type: String,
      default: defaultConfig.currentPosition,
    },
    positionList: {
      type: Object as PropType<Record<string, AxisConfig>>,
      default: () => defaultConfig.positionList,
    },
    material: {
      type: [Object, String] as PropType<MaterialInput>,
      default: () => defaultConfig.material,
      description:
        '物料配置：可以是对象 {type, color, size, label} 或字符串 "block"/"cube"等',
    },
  }),
  setup(props) {
    const {
      width,
      height,
      scale: _scale,
      animateDuration,
      partId,
      currentStation,
      stationList,
      currentPosition,
      positionList,
      material,
    } = toRefs(props);

    const robotArmCanvas = ref<HTMLCanvasElement | null>(null);
    let app: PIXI.Application;
    let firstArm: PIXI.Graphics;
    let secondArm: PIXI.Graphics;
    let thirdArm: PIXI.Graphics;

    // 添加角度转弧度的工具函数
    const degToRad = (degrees: number) => {
      return degrees * (Math.PI / 180);
    };

    // 使用组件状态Hook（响应式工厂函数版本）
    const { state } = useComponentState(
      partId.value,
      () => ({
        currentStation: currentStation.value,
        currentPosition: currentPosition.value,
        material: material.value,
      }),
      props.dynamicPartInfo,
    );

    // 创建统一的material computed，优先使用state中的值（MQTT更新后的值）
    const currentMaterial = computed(() => {
      // 如果state中有material且不为undefined/null，使用state中的值
      if (state.material !== undefined && state.material !== null) {
        return state.material;
      }
      // 否则使用props中的初始值
      return material.value;
    });

    // 使用统一的material来源
    const { suctionContainer: gripper } = useSucker(currentMaterial);

    onMounted(() => {
      initPixiApp();
      createAgvRobot();
    });

    // 组件销毁前清理PIXI应用，避免WebGL上下文泄露
    onBeforeUnmount(() => {
      if (app) {
        // 停止所有动画
        gsap.killTweensOf(app.stage);
        gsap.killTweensOf('*');

        // 清理舞台
        app.stage.removeChildren();

        // 销毁应用
        app.destroy(true, {
          children: true,
          texture: true,
          baseTexture: true,
        });

        app = null as any;
        console.log('PIXI应用已清理');
      }
    });

    const initPixiApp = () => {
      // 如果应用已存在，先销毁
      if (app) {
        app.destroy(true);
      }

      app = new PIXI.Application({
        width: width.value,
        height: height.value,
        backgroundColor: 0x000000,
        backgroundAlpha: 0,
        view: robotArmCanvas.value as HTMLCanvasElement,
        antialias: true,
        // 添加一些性能优化选项
        powerPreference: 'low-power', // 优先使用集成显卡
        preserveDrawingBuffer: false, // 不保留绘图缓冲区
      });
    };

    const drawAgvBase = (container: PIXI.Container) => {
      const agvBase = new PIXI.Graphics();

      // AGV车身主体
      agvBase.lineStyle(3, 0x333333);
      agvBase.beginFill(0xe6f7ff);
      agvBase.drawRoundedRect(-150, -65, 300, 140, 12);
      agvBase.endFill();

      // 前后轮子
      const wheelPositions = [
        { x: -110, y: 70 }, // 后轮
        { x: 110, y: 70 }, // 前轮
      ];

      wheelPositions.forEach((pos) => {
        // 轮子主体 - 更大更明显
        agvBase.lineStyle(3, 0x333333);
        agvBase.beginFill(0x444444);
        agvBase.drawCircle(pos.x, pos.y, 18);
        agvBase.endFill();

        // 轮子内圈
        agvBase.beginFill(0x666666);
        agvBase.drawCircle(pos.x, pos.y, 10);
        agvBase.endFill();

        // 轮胎纹理
        agvBase.lineStyle(2, 0x333333);
        agvBase.drawCircle(pos.x, pos.y, 14);
      });

      // 方向指示器（前方箭头） - 调整到合适位置
      agvBase.lineStyle(3, 0x1677ff);
      agvBase.beginFill(0x1677ff);
      agvBase.moveTo(140, 0);
      agvBase.lineTo(165, -20);
      agvBase.lineTo(165, 20);
      agvBase.closePath();
      agvBase.endFill();

      // 绘制两个盒子预留位置
      drawBoxPositions(agvBase);

      container.addChild(agvBase);
    };

    const drawBoxPositions = (container: PIXI.Graphics) => {
      // 盒子位置1
      const boxPos1 = { x: -120, y: -50, width: 50, height: 35 };
      // 盒子位置2
      const boxPos2 = { x: -120, y: 0, width: 50, height: 35 };

      [boxPos1, boxPos2].forEach((pos, index) => {
        // 绘制虚线框表示预留位置
        container.lineStyle(1, 0x999999, 0.5);
        container.beginFill(0x000000, 0);
        container.drawRoundedRect(pos.x, pos.y, pos.width, pos.height, 3);
        container.endFill();

        // 添加标签
        const text = new PIXI.Text(`Box${index + 1}`, {
          fontSize: 10,
          fill: 0x666666,
          align: 'center',
        });
        text.anchor.set(0.5);
        text.position.set(pos.x + pos.width / 2, pos.y + pos.height / 2);
        container.addChild(text);
      });
    };

    const createAgvRobot = () => {
      // 创建AGV主容器
      const agvContainer = new PIXI.Container();

      // 绘制AGV底座
      drawAgvBase(agvContainer);

      // 绘制机械臂底座
      const armBase = new PIXI.Graphics();
      armBase.beginFill(0x292327); // 深灰色
      armBase.drawRect(-20, 0, 40, 15);
      armBase.endFill();

      // 上层较小的矩形
      armBase.beginFill(0x3e3e3e); // 稍浅的蓝灰色
      armBase.drawRect(-15, -15, 30, 15);
      armBase.endFill();

      // 底座主体
      armBase.beginFill(0x292327);
      armBase.drawRect(-10, -35, 20, 20);
      armBase.endFill();

      // 底座主体上的圆形
      armBase.beginFill(0x292327);
      armBase.drawCircle(0, -35, 10);
      armBase.endFill();

      // 机械臂安装位置（AGV车身中央偏右）
      armBase.position.set(30, -80);

      // 绘制第一节臂
      firstArm = new PIXI.Graphics();
      firstArm.lineStyle(4, 0x292327);
      firstArm.beginFill(0xff5d03);
      firstArm.drawRoundedRect(
        -17.5,
        -currentPositionData.value.firstArmLength + 15, // 调整长度
        35,
        currentPositionData.value.firstArmLength, // 使用 state 中的长度
        20,
      );
      firstArm.endFill();

      // 添加橙色圆形轮廓
      firstArm.lineStyle(3, 0x292327);
      firstArm.drawCircle(0, -1, 9);

      // 添加小实心圆
      firstArm.lineStyle(0);
      firstArm.beginFill(0x292327);
      firstArm.drawCircle(0, -1, 5);
      firstArm.endFill();

      firstArm.position.set(0, -30);

      // 绘制第二节臂
      secondArm = new PIXI.Graphics();
      secondArm.lineStyle(4, 0x292327);
      secondArm.beginFill(0xa83302);
      secondArm.drawRoundedRect(
        -17.5,
        -currentPositionData.value.secondArmLength + 15,
        35,
        currentPositionData.value.secondArmLength,
        20,
      );
      secondArm.endFill();

      // 添加圆形轮廓
      secondArm.lineStyle(3, 0x292327);
      secondArm.drawCircle(0, -1, 9);

      // 添加小实心圆
      secondArm.lineStyle(0);
      secondArm.beginFill(0x292327);
      secondArm.drawCircle(0, -1, 5);
      secondArm.endFill();

      secondArm.position.set(0, -currentPositionData.value.firstArmLength + 30);

      // 绘制第三节臂
      thirdArm = new PIXI.Graphics();
      thirdArm.lineStyle(3, 0x292327);
      thirdArm.beginFill(0xff5d03);
      thirdArm.drawRoundedRect(
        -15,
        -currentPositionData.value.thirdArmLength + 15,
        30,
        currentPositionData.value.thirdArmLength,
        18,
      );
      thirdArm.endFill();

      // 添加圆形轮廓
      thirdArm.lineStyle(3, 0x292327);
      thirdArm.drawCircle(0, 1, 9);

      // 添加小实心圆
      thirdArm.lineStyle(0);
      thirdArm.beginFill(0x292327);
      thirdArm.drawCircle(0, 1, 5);
      thirdArm.endFill();

      thirdArm.endFill();

      thirdArm.position.set(0, -currentPositionData.value.secondArmLength + 30);

      gripper.value.position.set(
        0,
        -currentPositionData.value.thirdArmLength + 30,
      );
      gripper.value.rotation = degToRad(currentPositionData.value.gripperAngle);

      thirdArm.addChild(gripper.value);

      secondArm.addChild(thirdArm);
      firstArm.addChild(secondArm);
      armBase.addChild(firstArm);
      agvContainer.addChild(armBase);

      // 设置AGV容器位置
      agvContainer.position.set(width.value - 400, height.value - 100);
      // 将AGV容器添加到舞台
      app.stage.addChild(agvContainer);

      firstArm.rotation = degToRad(currentPositionData.value.firstArmAngle);
      secondArm.rotation = degToRad(currentPositionData.value.secondArmAngle);
      thirdArm.rotation = degToRad(currentPositionData.value.thirdArmAngle);
    };

    // 修改动画函数
    const animateToNewPosition = (newPosition: AxisConfig) => {
      if (!currentPositionData.value) {
        return;
      }
      const tl = gsap.timeline({
        repeat: 0,
        yoyo: false,
      });

      // 第一节臂动画
      tl.to(armStates.firstArm, {
        length: newPosition.firstArmLength,
        duration: animateDuration.value,
        ease: 'power1.inOut',
        onUpdate: () => {
          firstArm.clear();
          firstArm.lineStyle(4, 0x292327);
          firstArm.beginFill(0xff5d03);
          firstArm.drawRoundedRect(
            -17.5,
            -armStates.firstArm.length + 15,
            35,
            armStates.firstArm.length,
            20,
          );
          firstArm.endFill();

          firstArm.lineStyle(3, 0x292327);
          firstArm.drawCircle(0, -1, 9);
          firstArm.lineStyle(0);
          firstArm.beginFill(0x292327);
          firstArm.drawCircle(0, -1, 5);
          firstArm.endFill();
        },
      }).to(
        firstArm,
        {
          rotation: degToRad(newPosition.firstArmAngle),
          duration: animateDuration.value,
          ease: 'power1.inOut',
        },
        `-=${animateDuration.value}`,
      );

      // 第二节臂动画
      tl.to(
        armStates.secondArm,
        {
          length: newPosition.secondArmLength,
          duration: animateDuration.value,
          ease: 'power1.inOut',
          onUpdate: () => {
            secondArm.clear();
            secondArm.lineStyle(4, 0x292327);
            secondArm.beginFill(0xa83302);
            secondArm.drawRoundedRect(
              -17.5,
              -armStates.secondArm.length + 15,
              35,
              armStates.secondArm.length,
              20,
            );
            secondArm.endFill();

            secondArm.lineStyle(3, 0x292327);
            secondArm.drawCircle(0, -1, 9);
            secondArm.lineStyle(0);
            secondArm.beginFill(0x292327);
            secondArm.drawCircle(0, -1, 5);
            secondArm.endFill();
            secondArm.position.set(0, -armStates.firstArm.length + 30);
          },
        },
        `-=${animateDuration.value}`,
      ).to(
        secondArm,
        {
          rotation: degToRad(newPosition.secondArmAngle),
          duration: animateDuration.value,
          ease: 'power1.inOut',
        },
        `-=${animateDuration.value}`,
      );

      // 第三节臂动画
      tl.to(
        armStates.thirdArm,
        {
          length: newPosition.thirdArmLength,
          duration: animateDuration.value,
          ease: 'power1.inOut',
          onUpdate: () => {
            thirdArm.clear();
            thirdArm.lineStyle(3, 0x292327);
            thirdArm.beginFill(0xff5d03);
            thirdArm.drawRoundedRect(
              -15,
              -armStates.thirdArm.length + 15,
              30,
              armStates.thirdArm.length,
              18,
            );
            thirdArm.endFill();

            thirdArm.lineStyle(3, 0x292327);
            thirdArm.drawCircle(0, 1, 9);
            thirdArm.lineStyle(0);
            thirdArm.beginFill(0x292327);
            thirdArm.drawCircle(0, 1, 5);
            thirdArm.endFill();
            thirdArm.position.set(0, -armStates.secondArm.length + 30);
          },
        },
        `-=${animateDuration.value}`,
      ).to(
        thirdArm,
        {
          rotation: degToRad(newPosition.thirdArmAngle),
          duration: animateDuration.value,
          ease: 'power1.inOut',
        },
        `-=${animateDuration.value}`,
      );

      // 吸盘动画
      tl.to(
        gripper.value,
        {
          rotation: degToRad(newPosition.gripperAngle),
          duration: animateDuration.value,
          ease: 'power1.inOut',
          onUpdate: () => {
            gripper.value.position.set(0, -armStates.thirdArm.length + 30);
          },
        },
        `-=${animateDuration.value}`,
      );

      tl.to(
        gripper.value.suction,
        {
          y: 10,
          duration: 0.5,
          ease: 'power2.inOut',
        },
        '+=0.5',
      );
      tl.to(
        gripper.value.innerCircle,
        {
          y: 5,
          duration: 0.5,
          ease: 'power2.inOut',
        },
        '<', // 与吸盘动画同时进行
      );

      // 吸盘动画
      tl.to(
        gripper.value,
        {
          rotation: degToRad(newPosition.gripperAngle),
          duration: animateDuration.value,
          ease: 'power1.inOut',
          onUpdate: () => {
            gripper.value.position.set(0, -armStates.thirdArm.length + 30);
          },
        },
        `-=${animateDuration.value}`,
      );

      tl.to(
        gripper.value.suction,
        {
          y: 0,
          duration: 0.5,
          ease: 'power2.inOut',
        },
        '+=0.5',
      );
      tl.to(
        gripper.value.innerCircle,
        {
          y: 0,
          duration: 0.5,
          ease: 'power2.inOut',
        },
        '<', // 与吸盘动画同时进行
      );
    };

    // 使用响应式状态计算当前位置数据
    const currentPositionData = computed(() => {
      return positionList.value[state.currentPosition];
    });

    const armStates = reactive({
      firstArm: {
        length: currentPositionData.value.firstArmLength,
      },
      secondArm: {
        length: currentPositionData.value.secondArmLength,
      },
      thirdArm: {
        length: currentPositionData.value.thirdArmLength,
      },
    });

    // 监听当前位置变化
    watch(
      currentPositionData,
      (newVal, oldVal) => {
        if (!isEqual(newVal, oldVal)) {
          animateToNewPosition(currentPositionData.value);
        }
      },
      {
        deep: true,
      },
    );

    // 监听canvas尺寸变化
    watch(
      () => [width.value, height.value],
      ([newWidth, newHeight]) => {
        if (!app) return;

        app.renderer.resize(newWidth, newHeight);

        // 重新调整AGV容器的位置到画布中心
        const agvContainer = app.stage.children[0];
        if (agvContainer) {
          agvContainer.position.set(newWidth / 2, newHeight / 2);
        }
      },
    );

    // 监听状态中的位置变化
    watch(
      () => state.currentStation,
      (newStation, oldStation) => {
        console.log(`AGV位置变化: 从 ${oldStation} 到 ${newStation})`);

        if (!app?.stage) return;

        const { left: newLeft, top: newTop } = stationList.value[newStation];

        const agvContainer = app.stage.children[0];
        if (agvContainer) {
          gsap.to(agvContainer, {
            x: width.value / 2 + newLeft,
            y: height.value / 2 + newTop,
            duration: animateDuration.value,
            ease: 'power1.inOut',
          });
        }
      },
    );

    return {
      robotArmCanvas,
    };
  },
});
</script>

<style scoped>
.robot-arm-container {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
