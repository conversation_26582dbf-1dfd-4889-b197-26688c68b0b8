import type { Meta, StoryObj } from '@storybook/vue3';
import { ref, reactive, watch } from 'vue';
import Robot from './index.vue';

const meta = {
  title: 'Equipment/Robot',
  component: Robot,
  tags: ['autodocs'],
  argTypes: {
    transformX: {
      control: 'number',
      description: '机器人X轴位移（像素）',
    },
    transformY: {
      control: 'number',
      description: '机器人Y轴位移（像素）',
    },
    scale: {
      control: 'number',
      description: '机器人整体缩放比例',
    },
    rotate: {
      control: 'number',
      description: '机器人旋转角度',
    },
    armList: {
      control: false,
      description: '机械臂配置列表，包含晶圆信息和位置信息',
    },
  },
  args: {
    transformX: 0,
    transformY: 0,
    scale: 1,
    rotate: 0,
  },
  parameters: {
    docs: {
      description: {
        component:
          'Robot组件用于显示晶圆机器人，支持多机械臂配置，可以控制机器人的位移、旋转和缩放，以及机械臂的动作。',
      },
    },
  },
} satisfies Meta<typeof Robot>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  name: '基础示例',
  args: {},
};

// 多机械臂示例
export const MultiArms: Story = {
  name: '多机械臂',
  args: {
    armList: [
      {
        waferNumber: '01',
        waferColor: '#1677ff',
        currentSite: 'None',
        siteList: [
          {
            name: 'Site1',
            rotate: 45,
            lowerArmLength: 60,
          },
        ],
      },
      {
        waferNumber: '02',
        waferColor: '#52c41a',
        currentSite: 'None',
        siteList: [
          {
            name: 'Site2',
            rotate: 135,
            lowerArmLength: 80,
          },
        ],
      },
      {
        waferNumber: '03',
        waferColor: '#f5222d',
        currentSite: 'None',
        siteList: [
          {
            name: 'Site3',
            rotate: 225,
            lowerArmLength: 70,
          },
        ],
      },
      {
        waferNumber: '04',
        waferColor: '#fa8c16',
        currentSite: 'None',
        siteList: [
          {
            name: 'Site4',
            rotate: 315,
            lowerArmLength: 90,
          },
        ],
      },
    ],
  },
};

// 机器人控制示例
export const RobotControl: Story = {
  name: '机器人控制',
  render: (args) => ({
    components: { Robot },
    setup() {
      const transformX = ref(0);
      const transformY = ref(0);
      const rotate = ref(0);
      const scale = ref(1);

      // 机械臂配置
      const armList = reactive([
        {
          waferNumber: '01',
          waferColor: '#1677ff',
          currentSite: 'None',
          siteList: [
            {
              name: 'Site1',
              rotate: 45,
              lowerArmLength: 60,
            },
            {
              name: 'Site2',
              rotate: 90,
              lowerArmLength: 80,
            },
          ],
        },
        {
          waferState: false,
          currentSite: 'None',
          siteList: [
            {
              name: 'Site1',
              rotate: 45,
              lowerArmLength: 60,
            },
            {
              name: 'Site2',
              rotate: 90,
              lowerArmLength: 80,
            },
          ],
        },
      ]);

      // 当前选中的机械臂
      const selectedArm = ref(0);
      // 当前选中的站点
      const selectedSite = ref('None');

      // 更新机械臂位置
      const updateArmSite = (armIndex: number, siteName: string) => {
        if (armIndex >= 0 && armIndex < armList.length) {
          armList[armIndex].currentSite = siteName;
        }
      };

      return {
        transformX,
        transformY,
        rotate,
        scale,
        armList,
        selectedArm,
        selectedSite,
        updateArmSite,
      };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="display: flex; flex-direction: column; gap: 10px; padding: 16px; border: 1px solid #eee; border-radius: 8px;">
          <h3 style="margin: 0 0 10px 0;">机器人控制面板</h3>
          
          <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
            <div style="display: flex; flex-direction: column; gap: 5px;">
              <label>X轴位移:</label>
              <input type="range" 
                v-model.number="transformX" 
                min="-200" max="200" 
                style="width: 100%;">
              <div style="display: flex; justify-content: space-between;">
                <span>-200</span>
                <span>{{ transformX }}</span>
                <span>200</span>
              </div>
            </div>
            
            <div style="display: flex; flex-direction: column; gap: 5px;">
              <label>Y轴位移:</label>
              <input type="range" 
                v-model.number="transformY" 
                min="-200" max="200" 
                style="width: 100%;">
              <div style="display: flex; justify-content: space-between;">
                <span>-200</span>
                <span>{{ transformY }}</span>
                <span>200</span>
              </div>
            </div>
            
            <div style="display: flex; flex-direction: column; gap: 5px;">
              <label>旋转角度:</label>
              <input type="range" 
                v-model.number="rotate" 
                min="0" max="360" 
                style="width: 100%;">
              <div style="display: flex; justify-content: space-between;">
                <span>0°</span>
                <span>{{ rotate }}°</span>
                <span>360°</span>
              </div>
            </div>
            
            <div style="display: flex; flex-direction: column; gap: 5px;">
              <label>缩放比例:</label>
              <input type="range" 
                v-model.number="scale" 
                min="0.5" max="2" step="0.1" 
                style="width: 100%;">
              <div style="display: flex; justify-content: space-between;">
                <span>0.5</span>
                <span>{{ scale }}</span>
                <span>2</span>
              </div>
            </div>
          </div>

          <div style="display: flex; flex-direction: column; gap: 10px; margin-top: 10px;">
            <div style="display: flex; gap: 10px; align-items: center;">
              <label>选择机械臂:</label>
              <select 
                v-model="selectedArm"
                style="padding: 6px; border-radius: 4px; border: 1px solid #ccc;">
                <option v-for="(arm, index) in armList" 
                  :key="index" 
                  :value="index">
                  机械臂 {{ index + 1 }}
                </option>
              </select>
              
              <label style="margin-left: 20px;">目标站点:</label>
              <select 
                v-model="selectedSite"
                @change="updateArmSite(selectedArm, selectedSite)"
                style="padding: 6px; border-radius: 4px; border: 1px solid #ccc;">
                <option value="None">None</option>
                <option v-for="site in armList[selectedArm].siteList" 
                  :key="site.name" 
                  :value="site.name">
                  {{ site.name }}
                </option>
              </select>
            </div>
          </div>
        </div>
        
        <div style="border: 1px solid #eee; border-radius: 8px; padding: 16px; display: flex; justify-content: center;">
          <Robot
            :transformX="transformX"
            :transformY="transformY"
            :rotate="rotate"
            :scale="scale"
            :armList="armList"
          />
        </div>
        
        <div style="border: 1px solid #eee; border-radius: 8px; padding: 16px;">
          <h3 style="margin: 0 0 10px 0;">当前配置</h3>
          <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; overflow: auto; max-height: 300px;">{{ JSON.stringify({
            transformX,
            transformY,
            rotate,
            scale,
            armList
          }, null, 2) }}</pre>
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story:
          '这个示例提供了一个交互式的机器人控制面板，您可以调整机器人的位置、旋转角度和缩放比例，以及控制机械臂移动到不同的站点。',
      },
    },
  },
};
