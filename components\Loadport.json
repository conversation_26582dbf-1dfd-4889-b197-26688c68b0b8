{"id": 1, "version": "0.0.17", "name": {"zh_CN": "Loadport"}, "component": "Loadport", "icon": "loadport", "description": "Loadport组件，用于显示多个晶圆槽位及其状态", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "Loadport", "version": "0.0.17", "script": "http://*************:4874/@dcp/component-library@0.0.17/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "group": "DCP", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["name", "waferNumber", "waferShape"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}}, "schema": {"properties": [{"label": {"zh_CN": "基础属性"}, "description": {"zh_CN": "Loadport的基础配置属性"}, "content": [{"property": "width", "label": {"text": {"zh_CN": "宽度"}}, "description": {"zh_CN": "组件的宽度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 120, "widget": {"component": "NumberConfigurator", "props": {"min": 50, "max": 500, "step": 10}}}, {"property": "height", "label": {"text": {"zh_CN": "高度"}}, "description": {"zh_CN": "组件的高度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 300, "widget": {"component": "NumberConfigurator", "props": {"min": 100, "max": 800, "step": 10}}}, {"property": "name", "label": {"text": {"zh_CN": "名称"}}, "description": {"zh_CN": "Loadport的显示名称"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "Loadport", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "waferNumber", "label": {"text": {"zh_CN": "晶圆数量"}}, "description": {"zh_CN": "Loadport中的晶圆数量"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 10, "widget": {"component": "NumberConfigurator", "props": {"min": 1, "max": 25, "step": 1}}}, {"property": "splitNumber", "label": {"text": {"zh_CN": "分隔位置"}}, "description": {"zh_CN": "晶圆槽位的分隔位置"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 5, "widget": {"component": "NumberConfigurator", "props": {"min": -1, "max": 25, "step": 1}}}, {"property": "waferShape", "label": {"text": {"zh_CN": "晶圆形状"}}, "description": {"zh_CN": "晶圆的显示形状"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "rectangle", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "矩形", "value": "rectangle"}, {"label": "圆形", "value": "circle"}]}}}, {"property": "slotNumberStep", "label": {"text": {"zh_CN": "槽位标号步长"}}, "description": {"zh_CN": "显示槽位编号的步长，0表示不显示"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 5, "widget": {"component": "NumberConfigurator", "props": {"min": 0, "max": 10, "step": 1}}}]}, {"label": {"zh_CN": "数据配置"}, "description": {"zh_CN": "Loadport的数据配置属性"}, "content": [{"property": "waferList", "label": {"text": {"zh_CN": "晶圆列表"}}, "description": {"zh_CN": "晶圆数据列表，包含各槽位晶圆的信息"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "object", "defaultValue": [], "widget": {"component": "CodeConfigurator", "props": {"language": "json", "height": 200}}}]}, {"label": {"zh_CN": "颜色配置"}, "description": {"zh_CN": "Loadport的颜色配置属性"}, "content": [{"property": "selectedColor", "label": {"text": {"zh_CN": "选中颜色"}}, "description": {"zh_CN": "选中状态的颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#409EFF", "widget": {"component": "ColorConfigurator", "props": {}}}, {"property": "disabledColor", "label": {"text": {"zh_CN": "禁用颜色"}}, "description": {"zh_CN": "禁用状态的颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#C0C4CC", "widget": {"component": "ColorConfigurator", "props": {}}}, {"property": "defaultColor", "label": {"text": {"zh_CN": "默认颜色"}}, "description": {"zh_CN": "默认状态的颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#f0faff", "widget": {"component": "ColorConfigurator", "props": {}}}, {"property": "successColor", "label": {"text": {"zh_CN": "成功颜色"}}, "description": {"zh_CN": "成功状态的颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#67C23A", "widget": {"component": "ColorConfigurator", "props": {}}}, {"property": "errorColor", "label": {"text": {"zh_CN": "错误颜色"}}, "description": {"zh_CN": "错误状态的颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#F56C6C", "widget": {"component": "ColorConfigurator", "props": {}}}, {"property": "wipColor", "label": {"text": {"zh_CN": "进行中颜色"}}, "description": {"zh_CN": "进行中状态的颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#E6A23C", "widget": {"component": "ColorConfigurator", "props": {}}}]}], "events": {"onClick": {"label": {"zh_CN": "点击时触发"}, "description": {"zh_CN": "当Loadport被点击时触发的事件"}, "type": "event", "functionInfo": {"params": [], "returns": {}}, "defaultValue": ""}}}, "snippets": [{"name": {"zh_CN": "Loadport"}, "icon": "loadport", "screenshot": "", "snippetName": "Loadport", "schema": {"props": {"width": 120, "height": 300, "name": "Loadport", "waferNumber": 10, "splitNumber": 5, "waferShape": "rectangle", "slotNumberStep": 5, "waferList": [], "selectedColor": "#409EFF", "disabledColor": "#C0C4CC", "defaultColor": "#f0faff", "successColor": "#67C23A", "errorColor": "#F56C6C", "wipColor": "#E6A23C"}}}], "category": "DCP"}