<template>
  <div class="three-axis-gantry-robot" ref="pixiContainer"></div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  onMounted,
  onUnmounted,
  toRefs,
  watch,
} from 'vue';
import * as PIXI from 'pixi.js';
import { gsap } from 'gsap';
import { useComponentState } from '@/communication/hooks/useComponentState';
import { createComponentProps } from '@/context/ComponentContext';
import { MaterialInput } from '../SixAxisRobot/index.vue';
import { useMaterial } from '../SixAxisRobot/hooks/useMaterial';
import { PropType, computed } from 'vue';

// 等轴斜距投影变换函数
const isometricProjection = (x: number, y: number, z: number) => {
  // 等轴斜距投影矩阵变换
  const projectedX = (x - z) * Math.cos(Math.PI / 6);
  const projectedY = (x + z) * Math.sin(Math.PI / 6) - y;
  return { x: projectedX, y: projectedY };
};

export default defineComponent({
  name: 'ThreeAxisGantryLinearRobot',
  props: createComponentProps({
    width: {
      type: Number,
      default: 250,
    },
    height: {
      type: Number,
      default: 300,
    },
    frameWidth: {
      type: Number,
      default: 150,
      validator: (value: number) => value > 0,
    },
    frameHeight: {
      type: Number,
      default: 100,
      validator: (value: number) => value > 0,
    },
    frameDepth: {
      type: Number,
      default: 100,
      validator: (value: number) => value > 0,
    },
    xMaxStroke: {
      type: Number,
      default: 500,
      description: 'X轴最大行程（毫米）',
      validator: (value: number) => value > 0,
    },
    yMaxStroke: {
      type: Number,
      default: 500,
      description: 'Y轴最大行程（毫米）',
      validator: (value: number) => value > 0,
    },
    zMaxStroke: {
      type: Number,
      default: 500,
      description: 'Z轴最大行程（毫米）',
      validator: (value: number) => value > 0,
    },
    xPosition: {
      type: Number,
      default: 250,
      description: 'X轴当前位置（实际行程值，毫米）',
    },
    yPosition: {
      type: Number,
      default: 250,
      description: 'Y轴当前位置（实际行程值，毫米）',
    },
    zPosition: {
      type: Number,
      default: 250,
      description: 'Z轴当前位置（实际行程值，毫米）',
    },
    suckerActive: {
      type: Boolean,
      default: false,
      description: '吸盘状态：true为吸附状态，false为释放状态',
    },
    animationDuration: {
      type: Number,
      default: 800,
      description: '动画持续时间（毫秒）',
    },
    enableAnimation: {
      type: Boolean,
      default: true,
      description: '是否启用动画',
    },
    material: {
      type: [Object, String] as PropType<MaterialInput>,
      default: null,
      description:
        '物料配置：可以是对象 {type, color, size, label} 或字符串 "block"/"cube"等',
    },
    xReversed: {
      type: Boolean,
      default: false,
      description:
        'X轴位置反转：true时0位置变为最大值位置，最大值位置变为0位置',
    },
    yReversed: {
      type: Boolean,
      default: false,
      description:
        'Y轴位置反转：true时0位置变为最大值位置，最大值位置变为0位置',
    },
    zReversed: {
      type: Boolean,
      default: false,
      description:
        'Z轴位置反转：true时0位置变为最大值位置，最大值位置变为0位置',
    },
  }),
  setup(props) {
    const {
      width,
      height,
      frameWidth,
      frameHeight,
      frameDepth,
      xMaxStroke,
      yMaxStroke,
      zMaxStroke,
      animationDuration,
      enableAnimation,
      xPosition,
      yPosition,
      zPosition,
      suckerActive,
      material,
      xReversed,
      yReversed,
      zReversed,
    } = toRefs(props);
    const pixiContainer = ref<HTMLDivElement>();
    let app: PIXI.Application;
    let gantryContainer: PIXI.Container;

    // 坐标转换函数：实际值 → 标准化值 (-1 到 1)
    const normalizePosition = (actualValue: number, maxStroke: number) => {
      return (actualValue * 2) / maxStroke - 1;
    };

    // 位置反转处理函数：根据反转状态调整位置值
    const applyPositionReverse = (
      value: number,
      maxStroke: number,
      isReversed: boolean,
    ) => {
      return isReversed ? maxStroke - value : value;
    };

    // 使用组件状态Hook（响应式工厂函数版本）
    const { state } = useComponentState(
      props.partId,
      () => ({
        xPosition: xPosition.value,
        yPosition: yPosition.value,
        zPosition: zPosition.value,
        suckerActive: suckerActive.value,
        material: material.value,
      }),
      props.dynamicPartInfo,
    );

    // 创建统一的当前状态computed，优先使用state中的值（MQTT更新后的值），并应用位置反转
    const currentXPosition = computed(() => {
      const rawValue =
        state.xPosition !== undefined ? state.xPosition : xPosition.value;
      return applyPositionReverse(rawValue, xMaxStroke.value, xReversed.value);
    });

    const currentYPosition = computed(() => {
      const rawValue =
        state.yPosition !== undefined ? state.yPosition : yPosition.value;
      return applyPositionReverse(rawValue, yMaxStroke.value, yReversed.value);
    });

    const currentZPosition = computed(() => {
      const rawValue =
        state.zPosition !== undefined ? state.zPosition : zPosition.value;
      return applyPositionReverse(rawValue, zMaxStroke.value, zReversed.value);
    });

    const currentSuckerActive = computed(() => {
      return state.suckerActive !== undefined
        ? state.suckerActive
        : suckerActive.value;
    });

    const currentMaterial = computed(() => {
      return state.material !== undefined && state.material !== null
        ? state.material
        : material.value;
    });

    // 使用统一的物料状态
    const { materialContainer } = useMaterial(currentMaterial);

    // 动画状态管理 - 使用统一的当前状态初始化
    const animatedPositions = ref({
      x: normalizePosition(currentXPosition.value, xMaxStroke.value),
      y: normalizePosition(currentYPosition.value, yMaxStroke.value),
      z: -normalizePosition(currentZPosition.value, zMaxStroke.value),
      suckerActive: currentSuckerActive.value,
    });

    onMounted(() => {
      initPixiApp();
      // 确保动画位置使用当前有效值（包括可能的MQTT更新值）
      animatedPositions.value = {
        x: normalizePosition(currentXPosition.value, xMaxStroke.value),
        y: normalizePosition(currentYPosition.value, yMaxStroke.value),
        z: -normalizePosition(currentZPosition.value, zMaxStroke.value),
        suckerActive: currentSuckerActive.value,
      };
      createGantryFrame();
    });

    onUnmounted(() => {
      // Kill any running GSAP animations on this component to prevent memory leaks
      gsap.killTweensOf(animatedPositions.value);
      if (app) {
        app.destroy(true);
      }
    });

    // 监听尺寸变化，重新初始化应用
    watch([width, height], (newValues, oldValues) => {
      if (
        app &&
        pixiContainer.value &&
        (newValues[0] !== oldValues?.[0] || newValues[1] !== oldValues?.[1])
      ) {
        // 尺寸变化时重新初始化应用
        app.destroy(true);
        initPixiApp();
        createGantryFrame();
      }
    });

    // 使用GSAP动画到目标位置
    const animateToPosition = (
      targetX: number,
      targetY: number,
      targetZ: number,
      targetSucker: boolean,
    ) => {
      if (!enableAnimation.value) {
        // 如果禁用动画，直接跳转
        animatedPositions.value.x = targetX;
        animatedPositions.value.y = targetY;
        animatedPositions.value.z = targetZ;
        animatedPositions.value.suckerActive = targetSucker;
        renderFrame();
        return;
      }

      const duration = animationDuration.value / 1000; // GSAP uses seconds
      const startSucker = animatedPositions.value.suckerActive;

      // Kill any existing tweens on this object to avoid conflicts
      gsap.killTweensOf(animatedPositions.value);

      // Animate the position
      gsap.to(animatedPositions.value, {
        x: targetX,
        y: targetY,
        z: targetZ,
        duration,
        ease: 'cubic.inOut',
        onUpdate: renderFrame,
      });

      // Schedule the sucker state change at the halfway point
      if (startSucker !== targetSucker) {
        gsap.delayedCall(duration / 2, () => {
          animatedPositions.value.suckerActive = targetSucker;
          renderFrame(); // Re-render to show sucker state change
        });
      }
    };

    // 单独渲染帧的函数
    const renderFrame = () => {
      if (gantryContainer) {
        gantryContainer.removeChildren();
        createGantryFrame();
      }
    };

    // 监听配置变化，立即重新渲染
    watch([frameWidth, frameHeight, frameDepth], () => {
      renderFrame();
    });

    // 监听统一的当前状态变化，使用正确的值进行位置计算
    watch(
      [
        currentXPosition,
        currentYPosition,
        currentZPosition,
        currentSuckerActive,
      ],
      ([newX, newY, newZ, newSucker]) => {
        const targetX = normalizePosition(newX, xMaxStroke.value);
        const targetY = normalizePosition(newY, yMaxStroke.value);
        const targetZ = -normalizePosition(newZ, zMaxStroke.value);
        animateToPosition(targetX, targetY, targetZ, newSucker);
      },
    );

    // 监听反转状态变化，当反转状态改变时重新计算位置并触发动画
    watch([xReversed, yReversed, zReversed], () => {
      // 反转状态变化时，currentXPosition等computed会自动重新计算
      // 这里不需要额外处理，因为上面的watch会自动触发
    });

    const initPixiApp = () => {
      app = new PIXI.Application({
        width: width.value,
        height: height.value,
        backgroundColor: 0xf5f5f5,
        backgroundAlpha: 0,
        antialias: true,
        resolution: window.devicePixelRatio || 1,
      });

      if (pixiContainer.value) {
        pixiContainer.value.appendChild(app.view as HTMLCanvasElement);
      }

      // 创建主容器并居中
      gantryContainer = new PIXI.Container();
      gantryContainer.x = width.value / 2;

      // 根据框架配置动态计算垂直位置，确保整个框架（包括脚撑）都可见
      const totalHeight = frameHeight.value + 20; // 脚撑高度
      const padding = 30; // 顶部和底部的边距
      gantryContainer.y = padding + totalHeight / 2;

      app.stage.addChild(gantryContainer);
    };

    const createGantryFrame = () => {
      const config = {
        width: frameWidth.value,
        height: frameHeight.value,
        depth: frameDepth.value,
      };

      // 定义立方体的8个顶点坐标（本地坐标系）
      const vertices = [
        // 顶面4个顶点
        { x: -config.width / 2, y: 0, z: -config.depth / 2 }, // 左前上
        { x: config.width / 2, y: 0, z: -config.depth / 2 }, // 右前上
        { x: config.width / 2, y: 0, z: config.depth / 2 }, // 右后上
        { x: -config.width / 2, y: 0, z: config.depth / 2 }, // 左后上
        // 底面4个顶点
        { x: -config.width / 2, y: -config.height, z: -config.depth / 2 }, // 左前下
        { x: config.width / 2, y: -config.height, z: -config.depth / 2 }, // 右前下
        { x: config.width / 2, y: -config.height, z: config.depth / 2 }, // 右后下
        { x: -config.width / 2, y: -config.height, z: config.depth / 2 }, // 左后下
      ];

      // 将3D坐标转换为2D投影坐标
      const projectedVertices = vertices.map((v) =>
        isometricProjection(v.x, v.y, v.z),
      );

      // 创建框架图形对象
      const frameGraphics = new PIXI.Graphics();

      // 设置线条样式 - 钢结构风格
      frameGraphics.lineStyle(3, 0x4a4a4a, 1);

      // 绘制顶面矩形 (顶点 0,1,2,3)
      drawRectangle(frameGraphics, [
        projectedVertices[0],
        projectedVertices[1],
        projectedVertices[2],
        projectedVertices[3],
      ]);

      // 绘制底面矩形 (顶点 4,5,6,7)
      drawRectangle(frameGraphics, [
        projectedVertices[4],
        projectedVertices[5],
        projectedVertices[6],
        projectedVertices[7],
      ]);

      // 绘制4条垂直边 (连接底面和顶面对应顶点)
      for (let i = 0; i < 4; i++) {
        drawLine(frameGraphics, projectedVertices[i], projectedVertices[i + 4]);
      }

      gantryContainer.addChild(frameGraphics);

      // 创建四个脚撑
      createFootSupports(projectedVertices);

      // 创建统一的轴系统Graphics对象
      const axisGraphics = new PIXI.Graphics();

      // 按正确的层级顺序绘制所有轴组件
      createAllAxes(config, axisGraphics);

      gantryContainer.addChild(axisGraphics);
    };

    // 绘制矩形的辅助函数
    const drawRectangle = (
      graphics: PIXI.Graphics,
      vertices: Array<{ x: number; y: number }>,
    ) => {
      graphics.moveTo(vertices[0].x, vertices[0].y);
      for (let i = 1; i < vertices.length; i++) {
        graphics.lineTo(vertices[i].x, vertices[i].y);
      }
      graphics.lineTo(vertices[0].x, vertices[0].y); // 闭合矩形
    };

    // 绘制直线的辅助函数
    const drawLine = (
      graphics: PIXI.Graphics,
      start: { x: number; y: number },
      end: { x: number; y: number },
    ) => {
      graphics.moveTo(start.x, start.y);
      graphics.lineTo(end.x, end.y);
    };

    // 绘制简约风格的滑块
    const drawSimpleSlider = (
      graphics: PIXI.Graphics,
      centerX: number,
      centerY: number,
      width: number,
      height: number,
      color: number,
    ) => {
      const halfWidth = width / 2;
      const halfHeight = height / 2;

      // 绘制主体
      graphics.beginFill(color, 0.9);
      graphics.lineStyle(2, color, 1); // 稍深的边框
      graphics.drawRoundedRect(
        centerX - halfWidth,
        centerY - halfHeight,
        width,
        height,
        3,
      );
      graphics.endFill();
    };

    // 绘制吸盘末端执行器（侧面视图）
    const drawSucker = (
      graphics: PIXI.Graphics,
      centerX: number,
      centerY: number,
      width: number,
      height: number,
      isActive: boolean,
    ) => {
      // 1. 吸盘主体（椭圆形，橡胶材质）
      // 激活时吸盘收缩变小，颜色变深
      const sizeScale = isActive ? 0.85 : 1.0; // 激活时收缩15%
      const heightScale = isActive ? 0.7 : 1.0; // 激活时高度更明显收缩30%
      const mainColor = isActive ? 0x1a1a1a : 0x505050; // 更明显的颜色对比

      graphics.beginFill(mainColor, 1);
      graphics.lineStyle(1, isActive ? 0x0a0a0a : 0x404040, 1);
      graphics.drawEllipse(
        centerX,
        centerY,
        (width / 2) * sizeScale,
        (height / 2) * heightScale,
      );
      graphics.endFill();

      // 2. 吸盘内部凹陷（更小的椭圆，显示内部结构）
      const innerColor = isActive ? 0x000000 : 0x2a2a2a; // 更强烈的颜色对比
      const innerScale = isActive ? 0.45 : 0.35; // 激活时内部凹陷更大，表示强吸力
      const innerHeightScale = isActive ? 0.4 : 0.3; // 内部凹陷高度也相应调整

      graphics.beginFill(innerColor, 1);
      graphics.lineStyle(0);
      graphics.drawEllipse(
        centerX,
        centerY,
        width * innerScale * sizeScale,
        height * innerHeightScale * heightScale,
      );
      graphics.endFill();

      // 3. 中心连接部位（小矩形，连接到连接杆）
      const connectorWidth = width * 0.3 * sizeScale;
      const connectorHeight = height * 0.2;
      const connectorColor = isActive ? 0x606060 : 0x4a4a4a; // 激活时连接器也稍亮一些

      graphics.beginFill(connectorColor, 1);
      graphics.drawRoundedRect(
        centerX - connectorWidth / 2,
        centerY - (height / 2) * heightScale - connectorHeight / 2,
        connectorWidth,
        connectorHeight,
        2,
      );
      graphics.endFill();

      // 4. 橡胶高光（椭圆形高光）
      const highlightIntensity = isActive ? 0.1 : 0.25; // 更明显的高光对比
      graphics.beginFill(0xffffff, highlightIntensity);
      graphics.drawEllipse(
        centerX - width * 0.2 * sizeScale,
        centerY - height * 0.2 * heightScale,
        width * 0.15 * sizeScale,
        height * 0.1 * heightScale,
      );
      graphics.endFill();

      // 5. 密封边缘线条（增强立体感）
      const edgeColor = isActive ? 0x111111 : 0x666666; // 更明显的边缘颜色对比
      graphics.lineStyle(1, edgeColor, 0.8);
      graphics.moveTo(centerX - width * 0.4 * sizeScale, centerY);
      graphics.lineTo(centerX + width * 0.4 * sizeScale, centerY);

      // 6. 激活状态指示（可选的额外视觉效果）
      if (isActive) {
        // 添加小的吸力指示线（从中心向内的短线）
        graphics.lineStyle(1, 0x888888, 0.8);
        const indicatorCount = 6; // 增加指示线数量，更明显
        for (let i = 0; i < indicatorCount; i++) {
          const angle = (i * Math.PI * 2) / indicatorCount;
          const startRadius = width * 0.25 * sizeScale;
          const endRadius = width * 0.12 * sizeScale;
          const startX = centerX + Math.cos(angle) * startRadius;
          const startY =
            centerY + Math.sin(angle) * startRadius * 0.3 * heightScale;
          const endX = centerX + Math.cos(angle) * endRadius;
          const endY =
            centerY + Math.sin(angle) * endRadius * 0.3 * heightScale;
          graphics.moveTo(startX, startY);
          graphics.lineTo(endX, endY);
        }

        // 添加中心点，表示强烈的吸力
        graphics.beginFill(0xcccccc, 0.8);
        graphics.drawCircle(centerX, centerY, 1);
        graphics.endFill();
      }
    };

    // 创建四个脚撑支架
    const createFootSupports = (
      projectedVertices: Array<{ x: number; y: number }>,
    ) => {
      const footGraphics = new PIXI.Graphics();
      footGraphics.lineStyle(3, 0x666666, 1);

      // 为底面的4个顶点创建脚撑（从地面向上支撑到框架底部）
      // 底面顶点现在是索引 4,5,6,7
      for (let i = 4; i < 8; i++) {
        const frameBottomVertex = projectedVertices[i]; // 框架底面顶点

        // 计算脚撑底部位置（地面位置，向下延伸）
        const footBottom = {
          x: frameBottomVertex.x,
          y: frameBottomVertex.y + 20, // 脚撑高度
        };

        // 绘制脚撑：从地面底部到框架底部的垂直直线
        drawLine(footGraphics, footBottom, frameBottomVertex);
      }

      gantryContainer.addChild(footGraphics);
    };

    // 创建统一的轴系统
    const createAllAxes = (
      frameConfig: { width: number; height: number; depth: number },
      graphics: PIXI.Graphics,
    ) => {
      // === 第一步：计算所有位置参数 ===

      // 计算Z轴滑块位置（需要在绘制Z轴导轨前计算）
      const maxRailHeight = frameConfig.height * 0.9;
      const topPosition = -5;
      const bottomPosition = -maxRailHeight + 15;
      const zSliderYPosition =
        topPosition +
        ((1 - animatedPositions.value.z) * (bottomPosition - topPosition)) / 2;

      // === 第二步：绘制所有导轨（顶层） ===

      // Y轴导轨
      const railLength = frameConfig.depth;
      const railStart3D = {
        x: animatedPositions.value.x * (frameConfig.width / 2),
        y: 0,
        z: -railLength / 2,
      };
      const railEnd3D = {
        x: animatedPositions.value.x * (frameConfig.width / 2),
        y: 0,
        z: railLength / 2,
      };
      const railStart2D = isometricProjection(
        railStart3D.x,
        railStart3D.y,
        railStart3D.z,
      );
      const railEnd2D = isometricProjection(
        railEnd3D.x,
        railEnd3D.y,
        railEnd3D.z,
      );

      graphics.lineStyle(4, 0x666666, 1);
      drawLine(graphics, railStart2D, railEnd2D);

      // Z轴导轨（动态长度，从Y轴滑块延伸到Z轴滑块当前位置）
      const railTop3D = {
        x: animatedPositions.value.x * (frameConfig.width / 2),
        y: 0, // 从顶面开始
        z: animatedPositions.value.y * (frameConfig.depth / 2),
      };
      const railBottom3D = {
        x: animatedPositions.value.x * (frameConfig.width / 2),
        y: zSliderYPosition + 10, // 延伸到Z轴滑块位置稍上方
        z: animatedPositions.value.y * (frameConfig.depth / 2),
      };
      const railTop2D = isometricProjection(
        railTop3D.x,
        railTop3D.y,
        railTop3D.z,
      );
      const railBottom2D = isometricProjection(
        railBottom3D.x,
        railBottom3D.y,
        railBottom3D.z,
      );

      graphics.lineStyle(4, 0x444444, 1);
      drawLine(graphics, railTop2D, railBottom2D);

      // === 第三步：绘制所有滑块（上层） ===

      // 定义统一的金属色系
      const metalColor = 0xb0b0b0; // 不锈钢色

      // X轴滑块
      const sliderXPosition =
        animatedPositions.value.x * (frameConfig.width / 2);
      const xSliderCenter3D = { x: sliderXPosition, y: 0, z: 0 };
      const xSliderCenter2D = isometricProjection(
        xSliderCenter3D.x,
        xSliderCenter3D.y,
        xSliderCenter3D.z,
      );

      drawSimpleSlider(
        graphics,
        xSliderCenter2D.x,
        xSliderCenter2D.y,
        24,
        16,
        metalColor, // 统一金属色
      );
      graphics.lineStyle(4, 0xffffff, 0.9); // 白色金属光泽
      graphics.moveTo(xSliderCenter2D.x - 2, xSliderCenter2D.y);
      graphics.lineTo(xSliderCenter2D.x + 2, xSliderCenter2D.y);

      // Y轴滑块
      const ySliderZPosition = animatedPositions.value.y * (railLength / 2);
      const ySliderCenter3D = {
        x: animatedPositions.value.x * (frameConfig.width / 2),
        y: 0,
        z: ySliderZPosition,
      };
      const ySliderCenter2D = isometricProjection(
        ySliderCenter3D.x,
        ySliderCenter3D.y,
        ySliderCenter3D.z,
      );

      drawSimpleSlider(
        graphics,
        ySliderCenter2D.x,
        ySliderCenter2D.y,
        20,
        14,
        metalColor, // 统一金属色
      );
      graphics.lineStyle(4, 0xffffff, 0.9); // 白色金属光泽
      graphics.moveTo(ySliderCenter2D.x, ySliderCenter2D.y - 2);
      graphics.lineTo(ySliderCenter2D.x, ySliderCenter2D.y + 2);

      // Z轴滑块（使用已计算的位置）
      const zSliderCenter3D = {
        x: animatedPositions.value.x * (frameConfig.width / 2),
        y: zSliderYPosition,
        z: animatedPositions.value.y * (frameConfig.depth / 2),
      };
      const zSliderCenter2D = isometricProjection(
        zSliderCenter3D.x,
        zSliderCenter3D.y,
        zSliderCenter3D.z,
      );

      drawSimpleSlider(
        graphics,
        zSliderCenter2D.x,
        zSliderCenter2D.y,
        16,
        20,
        metalColor, // 统一金属色
      );
      graphics.lineStyle(2, 0xffffff, 0.9); // 白色金属光泽
      // 向上箭头
      graphics.moveTo(zSliderCenter2D.x, zSliderCenter2D.y - 6);
      graphics.lineTo(zSliderCenter2D.x - 3, zSliderCenter2D.y - 3);
      graphics.moveTo(zSliderCenter2D.x, zSliderCenter2D.y - 6);
      graphics.lineTo(zSliderCenter2D.x + 3, zSliderCenter2D.y - 3);
      // 向下箭头
      graphics.moveTo(zSliderCenter2D.x, zSliderCenter2D.y + 6);
      graphics.lineTo(zSliderCenter2D.x - 3, zSliderCenter2D.y + 3);
      graphics.moveTo(zSliderCenter2D.x, zSliderCenter2D.y + 6);
      graphics.lineTo(zSliderCenter2D.x + 3, zSliderCenter2D.y + 3);

      // === 第四步：绘制吸盘末端执行器（最上层） ===
      const suckerCenter3D = {
        x: animatedPositions.value.x * (frameConfig.width / 2),
        y: zSliderYPosition - 25, // 稍微下移一些给吸盘更多空间
        z: animatedPositions.value.y * (frameConfig.depth / 2),
      };
      const suckerCenter2D = isometricProjection(
        suckerCenter3D.x,
        suckerCenter3D.y,
        suckerCenter3D.z,
      );

      // 绘制连接杆（从Z轴滑块底部到吸盘连接器）
      // 根据吸盘状态调整连接杆终点位置
      const connectionOffset = animatedPositions.value.suckerActive ? 4 : 6; // 激活时连接点更近

      graphics.lineStyle(4, metalColor, 1);
      graphics.moveTo(zSliderCenter2D.x, zSliderCenter2D.y + 10); // Z轴滑块底部
      graphics.lineTo(suckerCenter2D.x, suckerCenter2D.y - connectionOffset); // 根据吸盘状态调整连接点

      // 绘制吸盘（侧面视图）
      drawSucker(
        graphics,
        suckerCenter2D.x,
        suckerCenter2D.y,
        20, // 吸盘宽度
        10, // 吸盘高度
        animatedPositions.value.suckerActive, // 吸盘状态
      );

      // 绘制物料（2D渲染，不使用3D投影）
      if (materialContainer.value) {
        // 设置物料容器位置到吸盘的2D坐标
        materialContainer.value.position.set(
          suckerCenter2D.x,
          suckerCenter2D.y + 8,
        ); // 稍微向上偏移

        // 将物料容器添加到主容器中（如果还没有添加）
        if (!gantryContainer.children.includes(materialContainer.value)) {
          gantryContainer.addChild(materialContainer.value);
        }
      }
    };

    return {
      pixiContainer,
    };
  },
});
</script>

<style scoped>
.three-axis-gantry-robot {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
