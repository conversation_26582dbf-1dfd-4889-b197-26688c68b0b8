// import * as acorn from 'acorn';
// import { useRoute } from 'vue-router';
// import { set, get, isEqual } from 'lodash';
// import { usePageInfoStore } from '@/store/modules/pageInfoStore';
// import ws from './ws';

// type Context = { [key: string]: any };
// type Sandbox = { [key: string]: any };
// type ExecutedCode = {
//   code: string;
//   result: any;
//   dependencies: string[];
//   isDirty: boolean;
// };

// export class JSExecutionEngine {
//   private context: Context;
//   private sandbox: Sandbox;
//   private executedCode: ExecutedCode[];
//   private expressionResults: Map<string, any> = new Map();
//   private routeDependentCode: Set<string> = new Set();

//   constructor(
//     globalVariables: { name: string; value: any }[],
//     globalFunctions: { name: string; content: Function | string }[],
//   ) {
//     this.context = {};
//     this.sandbox = {
//       form: {},
//     };
//     this.executedCode = [];

//     const route = useRoute();

//     watch(
//       () => route.fullPath,
//       () => {
//         this.updateRouteDependentCode();
//       },
//     );

//     globalVariables.forEach((item) => {
//       this.updateVariable(item.name, item.value);
//     });

//     this.addFunction('setFieldsValue', (data: any) => {
//       const pageInfoStore = usePageInfoStore();
//       pageInfoStore.setFieldsValue(data);
//     });

//     this.addFunction('setFieldValue', (field: string, value: any) => {
//       const pageInfoStore = usePageInfoStore();
//       pageInfoStore.setFieldValue(field, value);
//     });

//     this.addFunction('wsSendMsg', (data: any) => {
//       ws.sendMsg(data);
//     });

//     this.addFunction('getRouteInfo', (key: string) => {
//       try {
//         return get(route, key);
//       } catch (error) {
//         console.info('获取路由信息出错:', error);
//         return '';
//       }
//     });

//     globalFunctions.forEach((item) => {
//       const { name, content } = item;
//       if (typeof content === 'function') {
//         this.addFunction(name, content);
//       } else {
//         const func = new Function(`return ${content}`)();
//         this.addFunction(name, func);
//       }
//     });
//   }

//   addFunction(name: string, func: Function): void {
//     set(this.context, name, func);
//   }

//   updateVariable(name: string, value: any): void {
//     const oldValue = get(this.context, name);
//     if (!isEqual(oldValue, value)) {
//       const changedPaths = this.getChangedPaths(name, oldValue, value);
//       set(this.context, name, value);
//       set(this.sandbox, name, value);
//       changedPaths.forEach((path) => this.markDependentCodeDirty(path));
//       this.recomputeDirtyCode();
//     }
//   }

//   private getChangedPaths(basePath: string, oldObj: any, newObj: any): string[] {
//     const changedPaths: string[] = [];

//     function compareObjects(path: string, oldValue: any, newValue: any) {
//       if (isEqual(oldValue, newValue)) return;

//       if (
//         typeof oldValue !== 'object' ||
//         typeof newValue !== 'object' ||
//         oldValue === null ||
//         newValue === null
//       ) {
//         changedPaths.push(path);
//         return;
//       }

//       const allKeys = new Set([...Object.keys(oldValue), ...Object.keys(newValue)]);
//       for (const key of allKeys) {
//         compareObjects(`${path}.${key}`, oldValue[key], newValue[key]);
//       }
//     }

//     compareObjects(basePath, oldObj, newObj);
//     return changedPaths;
//   }

//   private markDependentCodeDirty(path: string): void {
//     const pathParts = path.split('.');
//     this.executedCode.forEach((code) => {
//       if (code.dependencies.some((dep) => this.isPathRelated(dep, pathParts))) {
//         code.isDirty = true;
//       }
//     });
//   }

//   private isPathRelated(dependency: string, changedPathParts: string[]): boolean {
//     const depParts = dependency.split('.');
//     const minLength = Math.min(depParts.length, changedPathParts.length);

//     for (let i = 0; i < minLength; i++) {
//       if (depParts[i] !== changedPathParts[i]) {
//         return false;
//       }
//       if (i === depParts.length - 1 || i === changedPathParts.length - 1) {
//         return true;
//       }
//     }

//     return depParts.length === changedPathParts.length;
//   }

//   private recomputeDirtyCode(): void {
//     this.executedCode.forEach((code) => {
//       if (code.isDirty) {
//         code.result = this.execute(code.code);
//         code.isDirty = false;
//       }
//     });
//   }

//   isExpression(code: string): boolean {
//     try {
//       const ast = acorn.parse(code, { ecmaVersion: 2022 });
//       return (
//         ast.body.length === 1 &&
//         (ast.body[0].type === 'ExpressionStatement' || ast.body[0].type === 'VariableDeclaration')
//       );
//     } catch (e) {
//       return false;
//     }
//   }

//   execute(code: string, withoutDependence = false): void {
//     const contextKeys = Object.keys(this.context);
//     const contextValues = Object.values(this.context);

//     try {
//       const isExpression = this.isExpression(code);

//       let wrappedCode;
//       if (isExpression) {
//         wrappedCode = `
//           with (sandbox) {
//             return (${code});
//           }
//         `;
//       } else {
//         wrappedCode = `
//           with (sandbox) {
//             return (function() {
//               ${code}
//             })();
//           }
//         `;
//       }

//       const func = new Function(...contextKeys, 'sandbox', wrappedCode);
//       const result = func(...contextValues, this.sandbox);
//       if (withoutDependence) return result;

//       for (const key in this.sandbox) {
//         if (Object.prototype.hasOwnProperty.call(this.sandbox, key)) {
//           const value = get(this.sandbox, key);
//           set(this.context, key, value);
//         }
//       }

//       // 记录执行的代码及其依赖
//       try {
//         // 检查代码是否包含 getRouteInfo 调用
//         if (code.includes('getRouteInfo')) {
//           this.routeDependentCode.add(code);
//         }
//         const hasIn = this.executedCode.findIndex((item) => item.code === code);
//         if (hasIn === -1) {
//           const dependencies = this.extractDependencies(code);
//           this.executedCode.push({ code, result, dependencies, isDirty: false });
//         }
//       } catch (error) {
//         console.error('记录执行的代码及其依赖出错:', error);
//       }

//       const pageInfoStore = usePageInfoStore();
//       pageInfoStore.updateJsEngineResult(code, result);

//       console.log(this.executedCode, 'this.executedCode');

//       // return result;
//     } catch (error: any) {
//       throw new Error(`执行错误: ${error.message}`);
//     }
//   }

//   executeFunction(funcString, ...args) {
//     const contextKeys = Object.keys(this.context);
//     const contextValues = Object.values(this.context);

//     try {
//       const fullCode = `
//         with (sandbox) {
//           return (${funcString})(...args);
//         }
//       `;
//       const func = new Function(...contextKeys, 'sandbox', 'args', fullCode);
//       return func(...contextValues, this.sandbox, args);
//     } catch (error: any) {
//       throw new Error(`函数执行错误: ${error.message}`);
//     }
//   }

//   private updateRouteDependentCode(): void {
//     const pageInfoStore = usePageInfoStore();
//     this.routeDependentCode.forEach((code) => {
//       const result = this.execute(code, true);
//       pageInfoStore.updateJsEngineResult(code, result);
//     });
//   }

//   private extractDependencies(code: string): string[] {
//     const ast = acorn.parse(code, { ecmaVersion: 2022 });
//     const dependencies: Set<string> = new Set();

//     function traverse(node: any) {
//       if (node.type === 'MemberExpression') {
//         const path: string[] = [];
//         let currentNode = node;

//         while (currentNode.type === 'MemberExpression') {
//           if (currentNode.computed) {
//             if (currentNode.property.type === 'Literal') {
//               const propertyValue = currentNode.property.value;
//               if (typeof propertyValue === 'string' && propertyValue.includes('.')) {
//                 path.unshift(...propertyValue.split('.'));
//               } else {
//                 path.unshift(String(propertyValue));
//               }
//             } else {
//               // 如果是动态计算的属性，我们递归处理它
//               traverse(currentNode.property);
//             }
//           } else {
//             path.unshift(currentNode.property.name);
//           }
//           currentNode = currentNode.object;
//         }

//         if (currentNode.type === 'Identifier') {
//           path.unshift(currentNode.name);
//           dependencies.add(path.join('.'));
//         }
//       } else if (node.type === 'Identifier') {
//         // 只有当标识符不是成员表达式的一部分时才添加
//         if (node.parent && node.parent.type !== 'MemberExpression') {
//           dependencies.add(node.name);
//         }
//       }

//       for (const key in node) {
//         if (typeof node[key] === 'object' && node[key] !== null) {
//           if (node[key] !== node.parent) {
//             // 避免无限循环
//             node[key].parent = node; // 添加父节点引用
//             traverse(node[key]);
//           }
//         }
//       }
//     }

//     traverse(ast);
//     return Array.from(dependencies);
//   }

//   registerExpression(expression: string): void {
//     if (!this.expressionResults.has(expression)) {
//       const result = this.execute(expression);
//       this.expressionResults.set(expression, result);
//       // 将表达式添加到 executedCode 数组中，以便进行依赖追踪
//       this.executedCode.push({
//         code: expression,
//         result,
//         isDirty: false,
//         dependencies: this.extractDependencies(expression),
//       });
//     }
//   }

//   getExpressionResult(expression: string): any {
//     return this.expressionResults.get(expression);
//   }
// }

export default {};
