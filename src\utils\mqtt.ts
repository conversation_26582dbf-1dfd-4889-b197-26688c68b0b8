/**
 * MQTT工具类
 * 用于在组件中处理MQTT消息的订阅和接收
 */
import type { MqttClient } from 'mqtt';

export interface MqttSubscription {
  topic: string;
  callback: (message: string, topic: string) => void;
}

/**
 * 管理组件的MQTT订阅
 */
export class MqttManager {
  private client: MqttClient;
  private subscriptions: Map<string, Array<(message: string, topic: string) => void>> = new Map();

  constructor(client: MqttClient) {
    this.client = client;
    this.setupMessageHandler();
  }

  /**
   * 设置MQTT消息处理器
   */
  private setupMessageHandler() {
    this.client.on('message', (topic, message) => {
      const callbacks = this.subscriptions.get(topic);
      if (callbacks) {
        const messageStr = message.toString();
        callbacks.forEach(callback => callback(messageStr, topic));
      }
    });
  }

  /**
   * 订阅一个MQTT主题
   * @param topic 主题
   * @param callback 收到消息时的回调函数
   * @returns 取消订阅的函数
   */
  subscribe(topic: string, callback: (message: string, topic: string) => void): () => void {
    if (!this.subscriptions.has(topic)) {
      this.subscriptions.set(topic, []);
      // 如果是新主题，在MQTT服务器上订阅
      this.client.subscribe(topic);
    }
    
    const callbacks = this.subscriptions.get(topic)!;
    callbacks.push(callback);

    // 返回一个取消订阅的函数
    return () => this.unsubscribe(topic, callback);
  }

  /**
   * 取消订阅
   * @param topic 主题
   * @param callback 回调函数
   */
  private unsubscribe(topic: string, callback: (message: string, topic: string) => void) {
    const callbacks = this.subscriptions.get(topic);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index !== -1) {
        callbacks.splice(index, 1);
      }

      // 如果没有回调函数了，取消对这个主题的订阅
      if (callbacks.length === 0) {
        this.subscriptions.delete(topic);
        this.client.unsubscribe(topic);
      }
    }
  }

  /**
   * 取消所有订阅
   */
  unsubscribeAll() {
    this.subscriptions.forEach((_, topic) => {
      this.client.unsubscribe(topic);
    });
    this.subscriptions.clear();
  }
}

/**
 * 创建MQTT管理器
 * @param client MQTT客户端实例
 * @returns MQTT管理器实例
 */
export function createMqttManager(client: MqttClient): MqttManager {
  return new MqttManager(client);
}
