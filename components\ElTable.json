{"id": 1, "version": "2.4.2", "name": {"zh_CN": "表单"}, "component": "ElTable", "icon": "table", "description": "用于展示多条结构类似的数据， 可对数据进行排序、筛选、对比或其他自定义操作", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "element-plus", "exportName": "ElTable"}, "group": "数据展示", "category": "element-plus", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": ["ElTableColumn"], "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["inline", "label-width"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent", "createBlock"], "disable": []}, "invalidity": [""], "clickCapture": true, "framework": "<PERSON><PERSON>"}, "schema": {"properties": [{"name": "0", "label": {"zh_CN": "基础属性"}, "content": [{"property": "data", "label": {"text": {"zh_CN": "数据"}}, "description": {"zh_CN": "显示的数据"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "top", "widget": {"component": "CodeConfigurator", "props": {"language": "json"}}}, {"property": "columns", "label": {"text": {"zh_CN": "表格列配置"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "properties": [{"label": {"zh_CN": "默认分组"}, "content": [{"property": "type", "type": "string", "labelPosition": "top", "label": {"text": {"zh_CN": "type"}}, "description": {"text": {"zh_CN": "对应列的类型。 如果设置了selection则显示多选框； 如果设置了 index 则显示该行的索引（从 1 开始计算）； 如果设置了 expand 则显示为一个可展开的按钮"}}, "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "selection", "value": "selection"}, {"label": "index", "value": "index"}, {"label": "expand", "value": "expand"}]}}}, {"property": "index", "type": "string", "labelPosition": "top", "label": {"text": {"zh_CN": "index"}}, "description": {"text": {"zh_CN": "如果设置了 type=index，可以通过传递 index 属性来自定义索引"}}, "widget": {"component": "CodeConfigurator", "props": {}}}, {"property": "label", "type": "string", "labelPosition": "top", "label": {"text": {"zh_CN": "label"}}, "description": {"text": {"zh_CN": "显示的标题"}}, "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "column-key", "type": "string", "labelPosition": "top", "label": {"text": {"zh_CN": "column-key"}}, "description": {"text": {"zh_CN": "column 的 key， column 的 key， 如果需要使用 filter-change 事件，则需要此属性标识是哪个 column 的筛选条件"}}, "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "prop", "type": "string", "labelPosition": "top", "label": {"text": {"zh_CN": "prop"}}, "description": {"text": {"zh_CN": "字段名称 对应列内容的字段名， 也可以使用 property属性"}}, "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "width", "type": "number", "labelPosition": "top", "label": {"text": {"zh_CN": "width"}}, "description": {"text": {"zh_CN": "对应列的宽度"}}, "widget": {"component": "NumberConfigurator", "props": {}}}, {"property": "min-width", "type": "number", "labelPosition": "top", "label": {"text": {"zh_CN": "min-width"}}, "description": {"text": {"zh_CN": "对应列的最小宽度， 对应列的最小宽度， 与 width 的区别是 width 是固定的，min-width 会把剩余宽度按比例分配给设置了 min-width 的列"}}, "widget": {"component": "NumberConfigurator", "props": {}}}, {"property": "fixed", "type": "string", "labelPosition": "top", "label": {"text": {"zh_CN": "fixed"}}, "description": {"text": {"zh_CN": "列是否固定在左侧或者右侧。 true 表示固定在左侧"}}, "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "left", "value": "left"}, {"label": "right", "value": "right"}]}}}, {"property": "sortable", "type": "boolean", "labelPosition": "left", "label": {"text": {"zh_CN": "sortable"}}, "description": {"text": {"zh_CN": "对应列是否可以排序"}}, "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "sort-method", "type": "function", "labelPosition": "top", "label": {"text": {"zh_CN": "sort-method"}}, "description": {"text": {"zh_CN": "指定数据按照哪个属性进行排序，仅当sortable设置为true的时候有效。 应该如同 Array.sort 那样返回一个 Number"}}, "widget": {"component": "CodeConfigurator", "props": {}}}, {"property": "sort-by", "type": "array", "labelPosition": "top", "label": {"text": {"zh_CN": "sort-by"}}, "description": {"text": {"zh_CN": "指定数据按照哪个属性进行排序，仅当 sortable 设置为 true 且没有设置 sort-method 的时候有效。 如果 sort-by 为数组，则先按照第 1 个属性排序，如果第 1 个相等，再按照第 2 个排序，以此类推"}}, "widget": {"component": "CodeConfigurator", "props": {"language": "json"}}}, {"property": "sort-orders", "type": "array", "labelPosition": "top", "label": {"text": {"zh_CN": "sort-orders"}}, "description": {"text": {"zh_CN": "数据在排序时所使用排序策略的轮转顺序，仅当 sortable 为 true 时有效。 需传入一个数组，随着用户点击表头，该列依次按照数组中元素的顺序进行排序"}}, "widget": {"component": "CodeConfigurator", "props": {"language": "json"}}}, {"property": "resizable", "type": "boolean", "labelPosition": "left", "defaultValue": true, "label": {"text": {"zh_CN": "resizable"}}, "description": {"text": {"zh_CN": "对应列是否可以通过拖动改变宽度（需要在 el-table 上设置 border 属性为真）"}}, "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "formatter", "type": "function", "labelPosition": "top", "defaultValue": true, "label": {"text": {"zh_CN": "formatter"}}, "description": {"text": {"zh_CN": "用来格式化内容"}}, "widget": {"component": "CodeConfigurator", "props": {"dataType": "JSFunction"}}}, {"property": "show-overflow-tooltip", "type": "boolean", "labelPosition": "left", "defaultValue": true, "label": {"text": {"zh_CN": "show-overflow-tooltip"}}, "description": {"text": {"zh_CN": "当内容过长被隐藏时显示 tooltip"}}, "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "align", "type": "string", "labelPosition": "top", "defaultValue": "left", "label": {"text": {"zh_CN": "align"}}, "description": {"text": {"zh_CN": "对齐方式"}}, "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "left", "value": "left"}, {"label": "center", "value": "center"}, {"label": "right", "value": "right"}]}}}, {"property": "header-align", "type": "string", "labelPosition": "top", "defaultValue": "left", "label": {"text": {"zh_CN": "header-align"}}, "description": {"text": {"zh_CN": "表头对齐方式， 若不设置该项，则使用表格的对齐方式"}}, "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "left", "value": "left"}, {"label": "center", "value": "center"}, {"label": "right", "value": "right"}]}}}, {"property": "class-name", "type": "string", "labelPosition": "top", "defaultValue": "left", "label": {"text": {"zh_CN": "class-name"}}, "description": {"text": {"zh_CN": "列的 className"}}, "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "label-class-name", "type": "string", "labelPosition": "top", "defaultValue": "left", "label": {"text": {"zh_CN": "label-class-name"}}, "description": {"text": {"zh_CN": "当前列标题的自定义类名"}}, "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "selectable", "type": "function", "labelPosition": "top", "defaultValue": true, "label": {"text": {"zh_CN": "selectable"}}, "description": {"text": {"zh_CN": "仅对 type=selection 的列有效，类型为 Function，Function 的返回值用来决定这一行的 CheckBox 是否可以勾选"}}, "widget": {"component": "CodeConfigurator", "props": {}}}, {"property": "reserve-selection", "type": "boolean", "labelPosition": "left", "defaultValue": true, "label": {"text": {"zh_CN": "reserve-selection"}}, "description": {"text": {"zh_CN": "数据刷新后是否保留选项，仅对  type=selection 的列有效， 请注意， 需指定 row-key 来让这个功能生效。"}}, "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "filters", "type": "array", "labelPosition": "top", "defaultValue": true, "label": {"text": {"zh_CN": "filters"}}, "description": {"text": {"zh_CN": "数据刷新后是否保留选项，仅对  type=selection 的列有效， 请注意， 需指定 row-key 来让这个功能生效。"}}, "widget": {"component": "CodeConfigurator", "props": {"language": "json"}}}, {"property": "filter-placement", "type": "string", "labelPosition": "top", "label": {"text": {"zh_CN": "filter-placement"}}, "description": {"text": {"zh_CN": "过滤弹出框的定位"}}, "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "filter-multiple", "type": "string", "labelPosition": "left", "defaultValue": true, "label": {"text": {"zh_CN": "filter-multiple"}}, "description": {"text": {"zh_CN": "数据过滤的选项是否多选"}}, "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "filter-method", "type": "function", "labelPosition": "top", "defaultValue": true, "label": {"text": {"zh_CN": "filter-method"}}, "description": {"text": {"zh_CN": "数据过滤使用的方法， 如果是多选的筛选项，对每一条数据会执行多次，任意一次返回 true 就会显示"}}, "widget": {"component": "CodeConfigurator", "props": {}}}, {"property": "filtered-value", "type": "array", "labelPosition": "top", "defaultValue": true, "label": {"text": {"zh_CN": "filtered-value"}}, "description": {"text": {"zh_CN": "选中的数据过滤项，如果需要自定义表头过滤的渲染方式，可能会需要此属性"}}, "widget": {"component": "CodeConfigurator", "props": {"language": "json"}}}]}], "widget": {"component": "TableColumnsConfigurator", "props": {"type": "object", "textField": "label", "language": "json", "buttonText": "编辑列配置", "title": "编辑列配置", "expand": true}}, "description": {"zh_CN": "表格列的配置信息"}, "labelPosition": "top"}, {"property": "max-height", "label": {"text": {"zh_CN": "最大高度"}}, "description": {"zh_CN": "Table 的最大高度。"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "widget": {"component": "NumberConfigurator", "props": {}}, "device": []}, {"property": "height", "label": {"text": {"zh_CN": "表格高度"}}, "description": {"zh_CN": "Table 的高度， 默认为自动高度。 这个高度会设置为 Table 的 style.height 的值，Table 的高度会受控于外部样式。"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "widget": {"component": "InputConfigurator", "props": {}}, "device": []}, {"property": "stripe", "label": {"text": {"zh_CN": "斑马纹"}}, "description": {"zh_CN": "是否为斑马纹 table"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "defaultValue": false, "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "border", "label": {"text": {"zh_CN": "纵向边框"}}, "description": {"zh_CN": "是否带有纵向边框"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "defaultValue": false, "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "size", "label": {"text": {"zh_CN": "表格尺寸"}}, "description": {"zh_CN": "Table 的尺寸"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "default", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "large", "value": "large"}, {"label": "default", "value": "default"}, {"label": "small", "value": "small"}]}}}, {"property": "fit", "label": {"text": {"zh_CN": "列宽自撑开"}}, "description": {"zh_CN": "列的宽度是否自撑开"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "defaultValue": true, "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "show-header", "label": {"text": {"zh_CN": "显示表头"}}, "description": {"zh_CN": "是否显示表头"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "defaultValue": true, "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "highlight-current-row", "label": {"text": {"zh_CN": "高亮当前行"}}, "description": {"zh_CN": "是否要高亮当前行"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "defaultValue": false, "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "current-row-key", "label": {"text": {"zh_CN": "当前行的 key"}}, "description": {"zh_CN": "当前行的 key，只写属性"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "top", "type": "string", "widget": {"component": "InputConfigurator", "props": {}}, "device": []}, {"property": "row-class-name", "label": {"text": {"zh_CN": "行的类名"}}, "description": {"zh_CN": "行的 className"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "widget": {"component": "InputConfigurator", "props": {}}, "device": []}, {"property": "row-key", "label": {"text": {"zh_CN": "行数据的 Key"}}, "description": {"zh_CN": "行数据的 Key，用来优化 Table 的渲染； 在使用reserve-selection功能与显示树形数据时，该属性是必填的。 类型为 String 时，支持多层访问：user.info.id，但不支持 user.info[0].id，此种情况请使用 Function"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "top", "widget": {"component": "CodeConfigurator", "props": {}}, "device": []}, {"property": "empty-text", "label": {"text": {"zh_CN": "空数据文本"}}, "description": {"zh_CN": "空数据时显示的文本内容"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "widget": {"component": "InputConfigurator", "props": {}}, "device": []}, {"property": "table-layout", "label": {"text": {"zh_CN": "表格布局方式"}}, "description": {"zh_CN": "设置表格单元、行和列的布局方式"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "top", "defaultValue": "fixed", "widget": {"component": "InputConfigurator", "props": {"options": [{"label": "fixed", "value": "fixed"}, {"label": "auto", "value": "auto"}]}}, "device": []}, {"property": "scrollbar-always-on", "label": {"text": {"zh_CN": "显示滚动条"}}, "description": {"zh_CN": "总是显示滚动条"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "defaultValue": false, "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}}, {"property": "flexible", "label": {"text": {"zh_CN": "主轴最小尺寸"}}, "description": {"zh_CN": "确保主轴的最小尺寸，以便不超过内容"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "defaultValue": false, "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}}], "description": {"zh_CN": ""}}], "events": {"onSelect": {"label": {"zh_CN": "勾选数据行的 Checkbox 时触发"}, "description": {"zh_CN": "当用户手动勾选数据行的 Checkbox 时触发的事件"}, "type": "event", "functionInfo": {"params": [{"name": "selection", "type": "Object", "defaultValue": "", "description": {"zh_CN": "当前选中项"}}, {"name": "row", "type": "Object", "defaultValue": "", "description": {"zh_CN": "当前行"}}], "returns": {}}}, "onSelectAll": {"label": {"zh_CN": "勾选全选时触发"}, "description": {"zh_CN": "当用户手动勾选全选 Checkbox 时触发的事件"}, "type": "event", "functionInfo": {"params": [{"name": "selection", "type": "Object", "defaultValue": "", "description": {"zh_CN": "当前选中项"}}], "returns": {}}}, "onSelectionChange": {"label": {"zh_CN": "选择项发生变化时会触发"}, "description": {"zh_CN": "当选择项发生变化时会触发该事件"}, "type": "event", "functionInfo": {"params": [{"name": "selection", "type": "Object", "defaultValue": "", "description": {"zh_CN": "当前选中项"}}], "returns": {}}}, "onCellMouseEnter": {"label": {"zh_CN": "单元格 hover 时会触发"}, "description": {"zh_CN": "当单元格 hover 进入时会触发该事件"}, "type": "event", "functionInfo": {"params": [{"name": "row", "type": "Object", "defaultValue": "", "description": {"zh_CN": "当前行"}}, {"name": "column", "type": "Object", "defaultValue": "", "description": {"zh_CN": "当前列"}}, {"name": "cell", "type": "Object", "defaultValue": "", "description": {"zh_CN": "当前单元格"}}, {"name": "event", "type": "Object", "defaultValue": "", "description": {"zh_CN": "原生事件 event"}}], "returns": {}}}, "onCellMouseLeave": {"label": {"zh_CN": "单元格 hover 退出时会触发"}, "description": {"zh_CN": "当单元格 hover 退出时会触发该事件"}, "type": "event", "functionInfo": {"params": [{"name": "row", "type": "Object", "defaultValue": "", "description": {"zh_CN": "当前行"}}, {"name": "column", "type": "Object", "defaultValue": "", "description": {"zh_CN": "当前列"}}, {"name": "cell", "type": "Object", "defaultValue": "", "description": {"zh_CN": "当前单元格"}}, {"name": "event", "type": "Object", "defaultValue": "", "description": {"zh_CN": "原生事件 event"}}], "returns": {}}}}, "slots": {"empty": {"label": {"zh_CN": "empty"}, "description": {"zh_CN": "当数据为空时自定义的内容"}}, "append": {"label": {"zh_CN": "append"}, "description": {"zh_CN": "插入至表格最后一行之后的内容， 如果需要对表格的内容进行无限滚动操作，可能需要用到这个 slot。 若表格有合计行，该 slot 会位于合计行之上。"}}}}, "snippets": [{"name": {"zh_CN": "表格"}, "icon": "grid", "screenshot": "", "snippetName": "ElTable", "schema": {"props": {"data": [{"date": "2016-05-03", "name": "<PERSON>", "address": "No. 189, Grove St, Los Angeles"}, {"date": "2016-05-02", "name": "<PERSON>", "address": "No. 189, Grove St, Los Angeles"}, {"date": "2016-05-04", "name": "<PERSON>", "address": "No. 189, Grove St, Los Angeles"}, {"date": "2016-05-01", "name": "<PERSON>", "address": "No. 189, Grove St, Los Angeles"}], "columns": [{"type": "index"}, {"label": "Date", "prop": "date"}, {"label": "Name", "prop": "name"}, {"label": "Address", "prop": "address"}]}}, "category": "element-plus"}]}