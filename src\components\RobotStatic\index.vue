<template>
  <div class="content">
    <svg
      v-if="type === 'hand'"
      viewBox="0 0 1024 1024"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      width="256"
      height="256"
    >
      <path
        d="M160 928l364.992-1e-8 10.016-8.99199999L717.984 736 896 736l0-192-192 0c-1.76-0.128-100.96-4.672-154.016 78.016-0.128 0.224-0.86399999-0.256-0.96 0-0.16 0.22399999 0.096 0.736 0 0.96-24.768 37.92-44.992 77.02399999-66.016 104C461.88800001 754.24 444.384 768 416 768L416.00000001 832c51.74400001 0 91.008-30.24 118.01599999-64.992 27.00800001-34.752 46.88-75.136 68.992-108.992l0-1.024C635.136 605.76 703.008 607.99999999 703.008 607.99999999L832 607.99999999l0 64.00000001-140.992 0-10.016 8.992L498.016 864 160 864 160 928z m0-768l338.016 0 182.976 183.008 10.016 8.992L832 352l0 64.00000001-130.016-1e-8s-66.88000001 2.24-98.976-48.992l0-1.024c-22.112-33.76-42.048-74.24-69.024-108.992C507.04 222.24 467.744 192 416.00000001 192l-1e-8 64c28.384 0 45.888 13.75999999 67.008 40.992 20.992 27.00800001 41.216 66.112 65.984 103.99999999 0.128 0.256-0.128 0.768 0 1.02400001l1.024 0C602.496 484 700.64 480.096 704 480l192 0 0-192-178.016 0-182.976-183.008-10.016-8.99199999L160 96l0 64z"
        :fill="stateColor"
      ></path>
    </svg>
    <svg
      v-else-if="type === 'robot'"
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="256"
      height="256"
    >
      <path
        d="M896.96 777.78H709.44V715.7c0-94.72-64.64-174.08-152.32-196.48L399.68 305.46c1.92-8.96 2.56-17.28 2.56-26.24 0-7.68-0.64-14.72-1.92-22.4l225.28-40.96 0.06-0.01 1.6 20.21c0.61 7.66 5.96 14.12 13.38 16.14l51.33 13.99-0.16-2.19 1.12 11.57c0.88 9.09 8.96 15.75 18.05 14.87l30.4-2.94c9.09-0.88 15.75-8.96 14.87-18.05l-1-10.39 50.24 17.85c4.42 1.57 9.28 1.3 13.5-0.75l67.77-32.94c3.69-1.79 6-5.55 5.95-9.64l-0.57-43.95c-0.07-5.41-5.38-9.18-10.51-7.46l-70.1 23.46-64.42-30.85-1.2-12.41 59.75-40.31L878.42 135c5.32 0.95 10.03-3.56 9.3-8.92l-5.86-43.55a10.57 10.57 0 0 0-7.3-8.67L802.7 51.19c-4.47-1.41-9.32-0.96-13.46 1.23L737.89 79.7l-1.02-10.57c-0.88-9.09-8.96-15.75-18.05-14.87l-30.4 2.94c-9.09 0.88-15.75 8.96-14.87 18.05l0.68 7.08-45.47 20.65a18.211 18.211 0 0 0-10.63 18.04l2.45 30.82-0.11 0.02-243.84 44.16c-26.88-39.04-71.04-64.64-122.24-64.64-81.28 0-147.84 66.56-147.84 147.84 0 71.04 51.2 131.2 117.76 144.64l99.2 224v0.64c-5.76 17.92-8.96 36.48-8.96 56.32v72.96H127.04c-35.2 0-64 28.16-64 64v131.84h897.92V841.78c0-35.84-28.8-64-64-64zM520.64 577.46c7.04 0.64 14.08 1.92 20.48 3.84 60.16 15.36 104.32 69.76 104.32 134.4v62.08H378.56v-72.96c0-5.12 0-10.24 1.28-15.36 1.28-15.36 5.76-30.08 12.8-42.88 21.12-41.6 64.64-69.76 113.92-69.76 5.12-0.01 9.6-0.01 14.08 0.64zM254.4 363.06c-1.28 0-3.2 0-4.48-0.64-44.16-1.92-79.36-38.4-79.36-83.2 0-46.08 37.76-83.84 83.84-83.84 17.28 0 33.92 5.12 47.36 14.08 19.2 13.44 32 33.92 35.2 58.24 1.28 3.84 1.28 7.68 1.28 11.52 0 5.76-0.64 10.88-1.92 16v0.64c-1.28 7.68-3.84 15.36-7.68 21.76-11.52 23.68-34.56 40.32-61.44 44.16-4.48 0.64-8.32 1.28-12.8 1.28z m39.04 58.88c30.08-8.32 56.32-25.6 76.16-49.28l105.6 143.36c-44.8 7.04-83.84 29.44-112.64 62.08l-69.12-156.16z m603.52 487.68H127.04v-67.84h769.92v67.84z"
        :fill="stateColor"
      ></path>
      <path
        d="M512.18 690.35m-64 0a64 64 0 1 0 128 0 64 64 0 1 0-128 0Z"
        :fill="stateColor"
      ></path>
      <path
        d="M254.61 279.28m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z"
        :fill="stateColor"
      ></path>
    </svg>
    <div class="wafer" v-if="type === 'hand' && waferName" :style="waferStyle">
      {{ waferName }}
    </div>
  </div>
</template>

<script lang="ts">
import { toRefs, computed, defineComponent } from 'vue';

export default defineComponent({
  name: 'RobotStatic',
  props: {
    type: {
      type: String,
      required: true,
    },
    state: {
      type: String,
    },
    rotate: {
      type: Number,
    },
    waferName: {
      type: String,
    },
    waferColor: {
      type: String,
    },
    stateColor: {
      type: String,
    },
  },

  setup(props) {
    const { type, waferName, waferColor, stateColor, rotate } = toRefs(props);

    const waferStyle = computed(() => {
      const color = waferColor.value;
      return {
        backgroundColor: color,
      };
    });

    return {
      type,
      rotate,
      waferStyle,
      stateColor,
      waferName,
    };
  },
});
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  svg {
    transform: v-bind('`rotate(${rotate}deg)`');
    width: 100%;
    height: 100%;
  }
}

.wafer {
  width: 50%;
  height: 50%;
  position: absolute;
  border-radius: 50%;
  top: 50%;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: translateY(-50%);
}

.name {
  position: absolute;
  bottom: 5px;
  left: 50%;
  width: 100%;
  text-align: center;
  transform: translateX(-50%);
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
}
</style>
