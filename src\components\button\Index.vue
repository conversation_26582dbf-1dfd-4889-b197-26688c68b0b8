<template>
  <button class="dcp-button" :class="buttonClass">
    <slot></slot>
  </button>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

export default defineComponent({
  name: 'Dcp<PERSON><PERSON>on',
  props: {
    type: {
      type: String,
      default: 'default',
    },
  },
  setup(props) {
    const buttonClass = computed(() => ({
      [`dcp-button--${props.type}`]: props.type,
    }));

    return {
      buttonClass,
    };
  },
});
</script>

<style lang="scss">
.dcp-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.5;
  border-radius: 4px;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #1677ff;
  color: #fff;

  &:hover {
    background: #4096ff;
    box-shadow: 0 4px 12px rgba(22, 119, 255, 0.2);
    transform: translateY(-1px);
  }

  &:active {
    background: #0958d9;
    transform: translateY(0);
  }

  &--default {
    background: #fff;
    border-color: #d9d9d9;
    color: rgba(0, 0, 0, 0.88);

    &:hover {
      color: #4096ff;
      border-color: #4096ff;
      background: #fff;
    }

    &:active {
      color: #0958d9;
      border-color: #0958d9;
    }
  }
}
</style>
