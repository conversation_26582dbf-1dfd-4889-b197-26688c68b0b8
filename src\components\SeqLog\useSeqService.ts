import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import SeqService from './seqApi';
import type {
  SeqEvent,
  SeqProperty,
  MessageTemplateToken,
  SeqLinks,
  QueryEventsParams,
  ConnectionCheckResult,
  SeqStatistics,
} from './seqApi';

// 重新导出类型供其他组件使用
export type {
  SeqEvent,
  SeqProperty,
  MessageTemplateToken,
  SeqLinks,
  QueryEventsParams,
  ConnectionCheckResult,
  SeqStatistics,
};

export interface SeqServiceConfig {
  baseUrl: string;
  apiKey?: string;
  autoConnect?: boolean;
  autoRefreshInterval?: number;
}

export function useSeqService(config: SeqServiceConfig) {
  // 创建服务实例
  const seqService = new SeqService(config.baseUrl, config.apiKey || null);

  // 响应式状态
  const loading = ref(false);
  const checkingConnection = ref(false);
  const eventsList = ref<SeqEvent[]>([]);
  const statistics = ref<SeqStatistics | null>(null);

  const connectionStatus = reactive<ConnectionCheckResult>({
    success: false,
    connected: false,
    message: '未检查',
  });

  const queryParams = reactive<QueryEventsParams>({
    filter: '',
    count: 100,
  });

  // 自动刷新定时器
  let autoRefreshTimer: NodeJS.Timeout | null = null;

  /**
   * 检查连接状态
   */
  const checkConnection = async (): Promise<boolean> => {
    checkingConnection.value = true;
    try {
      const result = await seqService.checkConnection();
      Object.assign(connectionStatus, result);

      if (result.connected) {
        ElMessage.success(result.message);
      } else {
        ElMessage.error(result.message);
      }

      return result.connected;
    } catch (error) {
      console.error('连接检查失败:', error);
      ElMessage.error('连接检查失败');
      return false;
    } finally {
      checkingConnection.value = false;
    }
  };

  /**
   * 查询日志事件
   */
  const queryEvents = async (): Promise<SeqEvent[]> => {
    loading.value = true;
    try {
      const result = await seqService.queryEvents(queryParams);
      console.log('queryEvents result', result);
      if (result.success) {
        eventsList.value = result.data;
        statistics.value = result.statistics || null;
        ElMessage.success(`查询成功，共找到 ${result.data.length} 条日志`);
        return result.data;
      } else {
        ElMessage.error(`查询失败: ${result.error}`);
        eventsList.value = [];
        statistics.value = null;
        return [];
      }
    } catch (error) {
      console.error('查询失败:', error);
      ElMessage.error('查询失败');
      return [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * 刷新事件列表
   */
  const refreshEvents = () => {
    return queryEvents();
  };

  /**
   * 清空过滤条件
   */
  const clearFilters = () => {
    queryParams.filter = '';
    delete queryParams.start;
    delete queryParams.end;
    eventsList.value = [];
    statistics.value = null;
  };

  /**
   * 设置时间范围
   */
  const setTimeRange = (start?: string, end?: string) => {
    if (start && end) {
      queryParams.start = start;
      queryParams.end = end;
    } else {
      delete queryParams.start;
      delete queryParams.end;
    }
  };

  /**
   * 启动自动刷新
   */
  const startAutoRefresh = (interval: number = 3000) => {
    stopAutoRefresh();
    autoRefreshTimer = setInterval(() => {
      if (connectionStatus.connected) {
        refreshEvents();
      }
    }, interval);
  };

  /**
   * 停止自动刷新
   */
  const stopAutoRefresh = () => {
    if (autoRefreshTimer) {
      clearInterval(autoRefreshTimer);
      autoRefreshTimer = null;
    }
  };

  /**
   * 导出事件数据
   */
  const exportEvents = (format: 'json' | 'csv' = 'json'): string => {
    if (format === 'json') {
      return JSON.stringify(eventsList.value, null, 2);
    } else {
      // CSV 格式导出
      if (eventsList.value.length === 0) return '';

      const getMessageText = (tokens: any[]): string => {
        if (!tokens || tokens.length === 0) return '';
        return tokens.map((token) => token.Text).join('');
      };

      const convertPropertiesToObject = (
        properties: any[],
      ): Record<string, any> => {
        const obj: Record<string, any> = {};
        if (properties && properties.length > 0) {
          properties.forEach((prop) => {
            obj[prop.Name] = prop.Value;
          });
        }
        return obj;
      };

      const headers = [
        'Timestamp',
        'Level',
        'Message',
        'Properties',
        'TraceId',
        'EventType',
      ];
      const csvContent = [
        headers.join(','),
        ...eventsList.value.map((event) =>
          [
            `"${event.Timestamp}"`,
            `"${event.Level}"`,
            `"${getMessageText(event.MessageTemplateTokens).replace(
              /"/g,
              '""',
            )}"`,
            `"${
              event.Properties && event.Properties.length > 0
                ? JSON.stringify(
                    convertPropertiesToObject(event.Properties),
                  ).replace(/"/g, '""')
                : ''
            }"`,
            `"${event.TraceId || ''}"`,
            `"${event.EventType || ''}"`,
          ].join(','),
        ),
      ].join('\n');

      return csvContent;
    }
  };

  /**
   * 初始化服务
   */
  const initialize = async () => {
    if (config.autoConnect !== false) {
      await checkConnection();
    }

    if (config.autoRefreshInterval && config.autoRefreshInterval > 0) {
      startAutoRefresh(config.autoRefreshInterval);
    }
  };

  /**
   * 销毁服务
   */
  const destroy = () => {
    stopAutoRefresh();
  };

  return {
    // 状态
    loading,
    checkingConnection,
    connectionStatus,
    queryParams,
    eventsList,
    statistics,

    // 方法
    checkConnection,
    queryEvents,
    refreshEvents,
    clearFilters,
    setTimeRange,
    startAutoRefresh,
    stopAutoRefresh,
    exportEvents,
    initialize,
    destroy,

    // 服务实例（如果需要直接访问）
    seqService,
  };
}

// 默认配置
export const defaultSeqConfig: SeqServiceConfig = {
  baseUrl: 'http://192.168.0.212:8880',
  apiKey: 'vIAV2wnkd0EdkyGz46qZ',
  autoConnect: true,
  autoRefreshInterval: 0, // 默认不自动刷新
};
