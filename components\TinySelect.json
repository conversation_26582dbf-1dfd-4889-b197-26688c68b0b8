{"icon": "select", "name": {"zh_CN": "下拉框"}, "component": "TinySelect", "description": "Select 选择器是一种通过点击弹出下拉列表展示数据并进行选择的 UI 组件", "docUrl": "", "screenshot": "", "tags": "", "keywords": "", "devMode": "proCode", "npm": {"package": "@opentiny/vue", "exportName": "Select"}, "group": "component", "priority": 8, "schema": {"properties": [{"label": {"zh_CN": "基础信息"}, "description": {"zh_CN": "基础信息"}, "content": [{"property": "modelValue", "label": {"text": {"zh_CN": "绑定值"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {}}, "description": {"zh_CN": "双向绑定的当前选中值"}, "labelPosition": "left"}, {"property": "placeholder", "label": {"text": {"zh_CN": "占位文本"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {}}, "description": {"zh_CN": "输入框占位文本"}, "labelPosition": "left"}, {"property": "clearable", "label": {"text": {"zh_CN": "清除按钮"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否显示清除按钮"}, "labelPosition": "left"}, {"property": "searchable", "label": {"text": {"zh_CN": "下拉可搜索"}}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "下拉面板是否可搜索"}, "labelPosition": "left"}, {"property": "disabled", "label": {"text": {"zh_CN": "禁用"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否禁用"}, "labelPosition": "left"}, {"property": "options", "label": {"text": {"zh_CN": "选项数据"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CodeConfigurator", "props": {"language": "json"}}, "description": {"zh_CN": "配置 Select 下拉数据项"}, "labelPosition": "top"}, {"property": "multiple", "label": {"text": {"zh_CN": "多选"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否允许输入框输入或选择多个项"}, "labelPosition": "left"}]}, {"name": "1", "label": {"zh_CN": "其他"}, "content": [{"property": "multiple-limit", "label": {"text": {"zh_CN": "最大可选值"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "NumberConfigurator", "props": {}}, "description": {"zh_CN": "多选时用户最多可以选择的项目数，为 0 则不限制"}, "labelPosition": "left"}, {"property": "popper-class", "label": {"text": {"zh_CN": "下拉框类名"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {}}, "description": {"zh_CN": "设置下拉框自定义的类名"}, "labelPosition": "left"}, {"property": "collapse-tags", "label": {"text": {"zh_CN": "多选展示"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "多选时是否将选中值按文字的形式展示"}, "labelPosition": "left"}], "description": {"zh_CN": ""}}], "events": {"onChange": {"label": {"zh_CN": "值改变时触发"}, "description": {"zh_CN": "在下拉框值改变时触发"}, "type": "event", "functionInfo": {"params": [{"name": "value", "type": "string", "defaultValue": "", "description": {"zh_CN": "下拉框选中项的值"}}], "returns": {}}, "defaultValue": ""}, "onUpdate:modelValue": {"label": {"zh_CN": "双向绑定的值改变时触发"}, "description": {"zh_CN": "当前选中的值改变时触发"}, "type": "event", "functionInfo": {"params": [{"name": "value", "type": "string", "defaultValue": "", "description": {"zh_CN": "双向绑定的当前选中值"}}], "returns": {}}, "defaultValue": ""}, "onBlur": {"label": {"zh_CN": "失去焦点时触发"}, "description": {"zh_CN": "在 Input 失去焦点时触发"}, "type": "event", "functionInfo": {"params": [{"name": "event", "type": "Object", "defaultValue": "", "description": {"zh_CN": "原生 event"}}], "returns": {}}, "defaultValue": ""}, "onFocus": {"label": {"zh_CN": "获取焦点时触发"}, "description": {"zh_CN": "在 Input 获取焦点时触发"}, "type": "event", "functionInfo": {"params": [{"name": "event", "type": "Object", "defaultValue": "", "description": {"zh_CN": "原生 event"}}], "returns": {}}, "defaultValue": ""}, "onClear": {"label": {"zh_CN": "点击清空按钮时触发"}, "description": {"zh_CN": "点击清空按钮时触发"}, "type": "event", "functionInfo": {"params": [], "returns": {}}, "defaultValue": ""}, "onRemoveTag": {"label": {"zh_CN": "多选模式下移除tag时触发"}, "description": {"zh_CN": "多选模式下移除tag时触发"}, "type": "event", "functionInfo": {"params": [{"name": "value", "type": "Object", "defaultValue": "", "description": {"zh_CN": "被移除Tag对应数据项的值字段"}}], "returns": {}}, "defaultValue": ""}}, "onBeforeMount": "console.log('table on load'); this.options = source.data"}, "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["multiple", "options"]}, "contextMenu": {"actions": ["create symbol"], "disable": ["copy", "remove"]}}, "snippets": [{"name": {"zh_CN": "下拉框"}, "icon": "select", "screenshot": "", "snippetName": "TinySelect", "schema": {"componentName": "TinySelect", "props": {"modelValue": "", "placeholder": "请选择", "options": [{"value": "1", "label": "黄金糕"}, {"value": "2", "label": "双皮奶"}]}}, "category": "form"}]}