<template>
  <div class="dcp-status" :class="statusClass">
    <span class="dcp-status__dot"></span>
    <span class="dcp-status__text">{{ statusText }}</span>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

export default defineComponent({
  name: 'DcpStatus',
  props: {
    status: {
      type: String,
      default: 'online',
      validator: (value: string) => ['online', 'offline'].includes(value),
    },
  },
  setup(props) {
    const statusClass = computed(() => ({
      [`dcp-status--${props.status}`]: props.status,
    }));

    const statusText = computed(
      () =>
        ({
          online: '在线',
          offline: '离线',
        }[props.status]),
    );

    return {
      statusClass,
      statusText,
    };
  },
});
</script>

<style lang="scss">
.dcp-status {
  display: inline-flex;
  align-items: center;
  gap: 8px;

  &__dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
  }

  &__text {
    font-size: 14px;
    line-height: 1.5;
  }

  // 在线状态
  &--online {
    .dcp-status__dot {
      background-color: #52c41a;
      box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
    }
    .dcp-status__text {
      color: #52c41a;
    }
  }

  // 离线状态
  &--offline {
    .dcp-status__dot {
      background-color: #d9d9d9;
      box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
    }
    .dcp-status__text {
      color: rgba(0, 0, 0, 0.45);
    }
  }
}
</style>
