{"id": 1, "version": "0.0.17", "name": {"zh_CN": "CDS状态显示"}, "component": "CdsState", "icon": "cds-state", "description": "用于显示CDS设备状态信息的组件，包含编号、名称和当前状态", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "CdsState", "version": "0.0.17", "script": "http://*************:4874/@dcp/component-library@0.0.17/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "group": "DCP", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["name", "no", "tankState", "backgroundColor", "textColor"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}}, "schema": {"properties": [{"label": {"zh_CN": "基本信息"}, "description": {"zh_CN": "CDS设备的基础信息配置"}, "content": [{"property": "name", "label": {"text": {"zh_CN": "设备名称"}}, "description": {"zh_CN": "CDS设备的名称"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "CDS设备", "widget": {"component": "InputConfigurator"}}, {"property": "no", "label": {"text": {"zh_CN": "设备编号"}}, "description": {"zh_CN": "CDS设备的编号"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "001", "widget": {"component": "InputConfigurator"}}, {"property": "tankState", "label": {"text": {"zh_CN": "罐体状态"}}, "description": {"zh_CN": "CDS设备的当前状态"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "正常", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "正常", "value": "正常"}, {"label": "异常", "value": "异常"}, {"label": "运行", "value": "运行"}, {"label": "停止", "value": "停止"}, {"label": "警告", "value": "警告"}]}}}]}, {"label": {"zh_CN": "尺寸与样式"}, "description": {"zh_CN": "CDS状态显示框的尺寸和视觉样式"}, "content": [{"property": "width", "label": {"text": {"zh_CN": "宽度"}}, "description": {"zh_CN": "状态显示框的宽度（像素）"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 320, "widget": {"component": "NumberConfigurator", "props": {"min": 200, "max": 800, "step": 10}}}, {"property": "height", "label": {"text": {"zh_CN": "高度"}}, "description": {"zh_CN": "状态显示框的高度（像素）"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 30, "widget": {"component": "NumberConfigurator", "props": {"min": 20, "max": 100, "step": 5}}}, {"property": "backgroundColor", "label": {"text": {"zh_CN": "背景颜色"}}, "description": {"zh_CN": "状态显示框的背景颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#ffffff", "widget": {"component": "ColorConfigurator", "props": {"showAlpha": true}}}, {"property": "textColor", "label": {"text": {"zh_CN": "文字颜色"}}, "description": {"zh_CN": "状态显示框的文字颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#131313", "widget": {"component": "ColorConfigurator", "props": {"showAlpha": true}}}]}], "events": {}}, "snippets": [{"name": {"zh_CN": "CDS状态显示"}, "icon": "cds-state", "screenshot": "", "snippetName": "CdsState", "schema": {"props": {"width": 320, "height": 30, "name": "CDS设备", "no": "001", "tankState": "正常", "backgroundColor": "#ffffff", "textColor": "#131313"}}}], "category": "DCP"}