<template>
  <div class="search-toolbar">
    <!-- 主搜索区域 -->
    <div class="search-section">
      <div class="search-input-wrapper">
        <el-input
          v-model="searchState.keyword"
          placeholder="搜索日志内容、异常信息..."
          size="large"
          clearable
          @keyup.enter="handleSearch"
          @input="onSearchInput"
          class="main-search-input"
        >
          <template #prefix>
            <el-icon><Icon icon="material-symbols:search" /></el-icon>
          </template>
          <template #suffix>
            <el-button
              type="primary"
              :loading="loading"
              @click="handleSearch"
              :disabled="!connectionStatus.connected"
              class="search-btn"
            >
              查询
            </el-button>
          </template>
        </el-input>
      </div>

      <!-- 快速过滤标签 -->
      <div class="quick-filters">
        <div class="filter-group">
          <span class="filter-label">级别:</span>
          <el-tag
            v-for="level in availableLevels"
            :key="level.value"
            :type="isLevelSelected(level.value) ? 'primary' : 'info'"
            :effect="isLevelSelected(level.value) ? 'dark' : 'plain'"
            @click="toggleLevel(level.value)"
            class="level-tag"
            size="small"
          >
            {{ level.label }}
          </el-tag>
        </div>

        <div class="filter-group">
          <span class="filter-label">时间:</span>
          <el-tag
            v-for="timeOption in quickTimeOptions"
            :key="timeOption.value"
            :type="
              searchState.quickTime === timeOption.value ? 'primary' : 'info'
            "
            :effect="
              searchState.quickTime === timeOption.value ? 'dark' : 'plain'
            "
            @click="setQuickTime(timeOption.value)"
            class="time-tag"
            size="small"
          >
            {{ timeOption.label }}
          </el-tag>
          <el-date-picker
            v-model="searchState.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="MM-DD HH:mm"
            value-format="YYYY-MM-DDTHH:mm:ss.sssZ"
            size="small"
            style="width: 280px; margin-left: 8px"
            clearable
            @change="onTimeRangeChange"
            class="time-range-picker"
          />
        </div>
      </div>

      <!-- 当前查询条件显示 -->
      <div v-if="hasActiveFilters" class="active-filters">
        <span class="active-filters-label">当前筛选:</span>
        <el-tag
          v-for="filter in activeFilterTags"
          :key="filter.key"
          type="success"
          closable
          @close="removeFilter(filter.key)"
          size="small"
          class="active-filter-tag"
        >
          {{ filter.label }}
        </el-tag>
        <el-button
          type="text"
          size="small"
          @click="clearAllFilters"
          class="clear-filters-btn"
        >
          清空所有
        </el-button>
      </div>
    </div>

    <!-- 操作控制区域 -->
    <div class="action-section">
      <div class="right-actions">
        <!-- 连接状态 -->
        <div class="connection-status">
          <div
            :class="[
              'status-dot',
              connectionStatus.connected ? 'connected' : 'disconnected',
            ]"
          ></div>
          <span class="status-text">{{ connectionStatus.message }}</span>
          <el-button
            :loading="checkingConnection"
            @click="handleCheckConnection"
            size="small"
            type="text"
          >
            检查连接
          </el-button>
        </div>

        <!-- 导出功能 -->
        <el-dropdown v-if="showExportButtons && hasData">
          <el-button size="default">
            <el-icon><Icon icon="material-symbols:download" /></el-icon>
            导出
            <el-icon class="el-icon--right"
              ><Icon icon="material-symbols:keyboard-arrow-down"
            /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="handleExport('json')">
                JSON 格式
              </el-dropdown-item>
              <el-dropdown-item @click="handleExport('csv')">
                CSV 格式
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 高级设置 -->
        <el-button
          v-if="enableAdvancedSettings"
          @click="showAdvancedSettings = true"
          size="default"
        >
          <el-icon><Icon icon="material-symbols:settings" /></el-icon>
          高级
        </el-button>
      </div>
      <div class="left-actions">
        <el-button-group>
          <el-button
            :loading="loading"
            @click="handleRefresh"
            :disabled="!connectionStatus.connected"
            size="default"
          >
            <el-icon><Icon icon="material-symbols:refresh" /></el-icon>
            刷新
          </el-button>
          <el-button @click="clearAllFilters" size="default">
            <el-icon><Icon icon="material-symbols:delete" /></el-icon>
            清空
          </el-button>
        </el-button-group>

        <!-- 自动刷新控制 -->
        <el-button
          v-if="enableAutoRefresh"
          :type="autoRefreshActive ? 'success' : 'info'"
          @click="toggleAutoRefresh"
          size="default"
        >
          <el-icon>
            <Icon v-if="autoRefreshActive" icon="material-symbols:pause" />
            <Icon v-else icon="material-symbols:play-arrow" />
          </el-icon>
          {{ autoRefreshActive ? '停止' : '自动' }}
        </el-button>
      </div>
    </div>

    <!-- 高级设置弹窗 -->
    <el-drawer
      v-model="showAdvancedSettings"
      title="高级查询设置"
      direction="rtl"
      size="400px"
    >
      <div class="advanced-settings">
        <!-- 查询数量设置 -->
        <div class="setting-group">
          <h4>查询设置</h4>
          <el-form-item label="查询数量">
            <el-select v-model="searchState.count" style="width: 100%">
              <el-option label="50 条" :value="50" />
              <el-option label="100 条" :value="100" />
              <el-option label="200 条" :value="200" />
              <el-option label="500 条" :value="500" />
              <el-option label="1000 条" :value="1000" />
            </el-select>
          </el-form-item>
        </div>

        <!-- 自定义过滤器 -->
        <div class="setting-group">
          <h4>自定义过滤器</h4>
          <el-form-item label="SEQ 查询语法">
            <el-input
              v-model="searchState.customFilter"
              type="textarea"
              :rows="4"
              placeholder="例如: @Level = 'Error' and @Message like '%异常%'"
            />
          </el-form-item>
          <div class="filter-examples">
            <p class="example-title">常用示例:</p>
            <el-tag
              v-for="example in filterExamples"
              :key="example.label"
              @click="applyFilterExample(example.filter)"
              class="example-tag"
              size="small"
            >
              {{ example.label }}
            </el-tag>
          </div>
        </div>

        <!-- 查询预设 -->
        <div class="setting-group">
          <h4>查询预设</h4>
          <div class="preset-actions">
            <el-input
              v-model="newPresetName"
              placeholder="预设名称"
              style="margin-bottom: 8px"
            />
            <el-button @click="saveCurrentAsPreset" type="primary" size="small">
              保存当前查询
            </el-button>
          </div>
          <div class="saved-presets">
            <div
              v-for="preset in savedPresets"
              :key="preset.id"
              class="preset-item"
            >
              <span @click="loadPreset(preset)" class="preset-name">
                {{ preset.name }}
              </span>
              <el-button
                @click="deletePreset(preset.id)"
                type="text"
                size="small"
                class="delete-preset"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import {
  ElMessage,
  ElInput,
  ElButton,
  ElTag,
  ElDatePicker,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElButtonGroup,
  ElDrawer,
  ElFormItem,
  ElSelect,
  ElOption,
  ElIcon,
} from 'element-plus';
import { Icon } from '@iconify/vue';

// Props
interface Props {
  loading?: boolean;
  checkingConnection?: boolean;
  connectionStatus?: {
    connected: boolean;
    message: string;
  };
  showExportButtons?: boolean;
  enableAutoRefresh?: boolean;
  autoRefreshActive?: boolean;
  hasData?: boolean;
  enableAdvancedSettings?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  checkingConnection: false,
  connectionStatus: () => ({ connected: false, message: '未连接' }),
  showExportButtons: true,
  enableAutoRefresh: true,
  autoRefreshActive: false,
  hasData: false,
  enableAdvancedSettings: false,
});

// Emits
const emit = defineEmits<{
  search: [params: SearchParams];
  refresh: [];
  clear: [];
  checkConnection: [];
  toggleAutoRefresh: [];
  export: [format: 'json' | 'csv'];
}>();

// 搜索状态接口
interface SearchParams {
  keyword: string;
  levels: string[];
  timeRange?: [string, string];
  quickTime?: string;
  customFilter: string;
  count: number;
  customTimeRange?: boolean;
}

interface FilterExample {
  label: string;
  filter: string;
}

interface SavedPreset {
  id: string;
  name: string;
  params: SearchParams;
  createdAt: string;
}

// 响应式状态
const searchState = reactive<SearchParams>({
  keyword: '',
  levels: [],
  timeRange: undefined,
  quickTime: '',
  customFilter: '',
  count: 100,
  customTimeRange: false,
});

// UI 状态
const showAdvancedSettings = ref(false);
const newPresetName = ref('');
const savedPresets = ref<SavedPreset[]>([]);

// 配置数据
const availableLevels = [
  { label: '调试', value: 'Debug' },
  { label: '信息', value: 'Information' },
  { label: '警告', value: 'Warning' },
  { label: '错误', value: 'Error' },
];

const quickTimeOptions = [
  { label: '1小时', value: '1h' },
  { label: '1天', value: '1d' },
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' },
];

const filterExamples: FilterExample[] = [
  { label: '仅错误', filter: "@Level = 'Error'" },
  { label: '包含异常', filter: "@Message like '%异常%'" },
  { label: '排除调试', filter: "@Level != 'Debug'" },
  { label: 'HTTP 请求', filter: '@Properties.RequestPath has @RequestPath' },
];

// 计算属性
const hasActiveFilters = computed(() => {
  return (
    searchState.keyword ||
    searchState.levels.length > 0 ||
    searchState.timeRange ||
    searchState.quickTime ||
    searchState.customFilter
  );
});

const activeFilterTags = computed(() => {
  const tags: Array<{ key: string; label: string }> = [];

  if (searchState.keyword) {
    tags.push({ key: 'keyword', label: `内容: ${searchState.keyword}` });
  }

  if (searchState.levels.length > 0) {
    tags.push({
      key: 'levels',
      label: `级别: ${searchState.levels.join(', ')}`,
    });
  }

  if (searchState.quickTime) {
    const timeOption = quickTimeOptions.find(
      (opt) => opt.value === searchState.quickTime,
    );
    tags.push({ key: 'quickTime', label: `时间: ${timeOption?.label}` });
  }

  if (searchState.customFilter) {
    tags.push({ key: 'customFilter', label: '自定义过滤器' });
  }

  return tags;
});

// 方法
const handleSearch = () => {
  emit('search', { ...searchState });
};

const handleRefresh = () => {
  emit('refresh');
};

const handleCheckConnection = () => {
  emit('checkConnection');
};

const toggleAutoRefresh = () => {
  emit('toggleAutoRefresh');
};

const handleExport = (format: 'json' | 'csv') => {
  emit('export', format);
};

const isLevelSelected = (level: string) => {
  return searchState.levels.includes(level);
};

const toggleLevel = (level: string) => {
  const index = searchState.levels.indexOf(level);
  if (index > -1) {
    searchState.levels.splice(index, 1);
  } else {
    searchState.levels.push(level);
  }
  handleSearch();
};

const setQuickTime = (timeValue: string) => {
  if (searchState.quickTime === timeValue) {
    searchState.quickTime = '';
    searchState.timeRange = undefined;
    searchState.customTimeRange = false;
  } else {
    searchState.quickTime = timeValue;
    searchState.customTimeRange = false;

    // 计算时间范围
    const now = new Date();
    const hours =
      timeValue === '1h'
        ? 1
        : timeValue === '1d'
        ? 24
        : timeValue === '7d'
        ? 24 * 7
        : timeValue === '30d'
        ? 24 * 30
        : 24;

    const start = new Date(now.getTime() - hours * 60 * 60 * 1000);
    searchState.timeRange = [start.toISOString(), now.toISOString()];
  }
  handleSearch();
};

const removeFilter = (filterKey: string) => {
  switch (filterKey) {
    case 'keyword':
      searchState.keyword = '';
      break;
    case 'levels':
      searchState.levels = [];
      break;
    case 'quickTime':
      searchState.quickTime = '';
      searchState.timeRange = undefined;
      break;
    case 'customFilter':
      searchState.customFilter = '';
      break;
  }
  handleSearch();
};

const clearAllFilters = () => {
  Object.assign(searchState, {
    keyword: '',
    levels: [],
    timeRange: undefined,
    quickTime: '',
    customFilter: '',
  });
  emit('clear');
};

const onTimeRangeChange = (timeRange: [string, string] | null) => {
  if (timeRange) {
    // 如果选择了自定义时间范围，清除快速时间选择
    searchState.quickTime = '';
    searchState.customTimeRange = true;
  } else {
    searchState.customTimeRange = false;
  }
  handleSearch();
};

const applyFilterExample = (filter: string) => {
  searchState.customFilter = filter;
};

const saveCurrentAsPreset = () => {
  if (!newPresetName.value.trim()) {
    ElMessage.warning('请输入预设名称');
    return;
  }

  const preset: SavedPreset = {
    id: Date.now().toString(),
    name: newPresetName.value.trim(),
    params: { ...searchState },
    createdAt: new Date().toISOString(),
  };

  savedPresets.value.push(preset);
  newPresetName.value = '';
  ElMessage.success('预设保存成功');

  // 保存到 localStorage
  localStorage.setItem(
    'seq-search-presets',
    JSON.stringify(savedPresets.value),
  );
};

const loadPreset = (preset: SavedPreset) => {
  Object.assign(searchState, preset.params);
  handleSearch();
  showAdvancedSettings.value = false;
  ElMessage.success(`已加载预设: ${preset.name}`);
};

const deletePreset = (presetId: string) => {
  const index = savedPresets.value.findIndex((p) => p.id === presetId);
  if (index > -1) {
    savedPresets.value.splice(index, 1);
    localStorage.setItem(
      'seq-search-presets',
      JSON.stringify(savedPresets.value),
    );
    ElMessage.success('预设删除成功');
  }
};

const onSearchInput = () => {
  // 可以在这里添加防抖搜索
};

// 初始化
const initializePresets = () => {
  const saved = localStorage.getItem('seq-search-presets');
  if (saved) {
    try {
      savedPresets.value = JSON.parse(saved);
    } catch (error) {
      console.error('Failed to load saved presets:', error);
    }
  }
};

// 监听器
watch(
  () => searchState.customFilter,
  (newVal) => {
    if (newVal) {
      // 如果有自定义过滤器，可以在这里做语法检查
    }
  },
);

// 生命周期
initializePresets();

// 暴露方法
defineExpose({
  getSearchParams: () => ({ ...searchState }),
  setSearchParams: (params: Partial<SearchParams>) => {
    Object.assign(searchState, params);
  },
  clearFilters: clearAllFilters,
});
</script>

<style scoped>
.search-toolbar {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.search-section {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.search-input-wrapper {
  margin-bottom: 16px;
}

.main-search-input {
  --el-input-height: 32px;
}

.main-search-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 2px solid #e4e7ed;
  padding-right: 4px;
  transition: all 0.3s ease;
}

.main-search-input :deep(.el-input__wrapper:hover) {
  border-color: #409eff;
}

.main-search-input :deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.search-btn {
  border-radius: 8px;
  padding: 0 20px;
  height: 36px;
  margin-right: -2px;
}

.quick-filters {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
  min-width: 40px;
}

.level-tag,
.time-tag {
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.level-tag:hover,
.time-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.time-range-picker :deep(.el-input__wrapper) {
  border-radius: 16px;
  border: 1px solid #e4e7ed;
  transition: all 0.2s ease;
}

.time-range-picker :deep(.el-input__wrapper:hover) {
  border-color: #409eff;
  box-shadow: 0 1px 4px rgba(64, 158, 255, 0.1);
}

.active-filters {
  margin-top: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.active-filters-label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.active-filter-tag {
  font-size: 11px;
}

.clear-filters-btn {
  font-size: 11px;
  color: #f56c6c;
  padding: 0;
  margin-left: 8px;
}

.action-section {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.left-actions,
.right-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: #ffffff;
  border-radius: 16px;
  border: 1px solid #e4e7ed;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.status-dot.connected {
  background-color: #67c23a;
}

.status-dot.disconnected {
  background-color: #f56c6c;
}

.status-text {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
}

.advanced-settings {
  padding: 20px;
}

.setting-group {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.setting-group:last-child {
  border-bottom: none;
}

.setting-group h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.filter-examples {
  margin-top: 12px;
}

.example-title {
  font-size: 12px;
  color: #606266;
  margin: 0 0 8px 0;
}

.example-tag {
  cursor: pointer;
  margin: 0 4px 4px 0;
  transition: all 0.2s ease;
}

.example-tag:hover {
  transform: translateY(-1px);
}

.preset-actions {
  margin-bottom: 16px;
}

.saved-presets {
  max-height: 200px;
  overflow-y: auto;
}

.preset-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.preset-item:last-child {
  border-bottom: none;
}

.preset-name {
  cursor: pointer;
  color: #409eff;
  font-size: 13px;
}

.preset-name:hover {
  text-decoration: underline;
}

.delete-preset {
  color: #f56c6c;
  font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-section {
    padding: 16px;
  }

  .action-section {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .left-actions,
  .right-actions {
    justify-content: center;
  }

  .filter-group {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-label {
    min-width: auto;
  }

  .time-range-picker {
    width: 100% !important;
    margin-left: 0 !important;
    margin-top: 8px;
  }
}

@media (max-width: 480px) {
  .quick-filters {
    display: none;
  }

  .connection-status {
    flex-direction: column;
    text-align: center;
  }
}
</style>
