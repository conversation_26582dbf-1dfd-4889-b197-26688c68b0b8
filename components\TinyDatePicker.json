{"name": {"zh_CN": "日期选择"}, "component": "TinyDatePicker", "icon": "datepick", "description": "用于输入或选择日期", "docUrl": "", "screenshot": "", "tags": "", "keywords": "", "devMode": "proCode", "npm": {"package": "@opentiny/vue", "exportName": "DatePicker"}, "group": "component", "priority": 1, "schema": {"properties": [{"label": {"zh_CN": "基础信息"}, "description": {"zh_CN": "基础信息"}, "content": [{"property": "modelValue", "label": {"text": {"zh_CN": "绑定值"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "I18nConfigurator", "props": {}}, "description": {"zh_CN": "双向绑定值"}, "labelPosition": "left"}, {"property": "type", "label": {"text": {"zh_CN": "类型"}}, "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "日期", "value": "date"}, {"label": "日期时间", "value": "datetime"}, {"label": "周", "value": "week"}, {"label": "月份", "value": "month"}, {"label": "年份", "value": "year"}]}}, "description": {"zh_CN": "设置日期框的type属性"}, "labelPosition": "left"}, {"property": "placeholder", "label": {"text": {"zh_CN": "占位文本"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "I18nConfigurator", "props": {}}, "description": {"zh_CN": "输入框占位文本"}, "labelPosition": "left"}, {"property": "clearable", "label": {"text": {"zh_CN": "清除按钮"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否显示清除按钮"}, "labelPosition": "left"}, {"property": "disabled", "label": {"text": {"zh_CN": "禁用"}}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否禁用"}, "labelPosition": "left"}, {"property": "readonly", "label": {"text": {"zh_CN": "只读"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否只读"}, "labelPosition": "left"}, {"property": "size", "label": {"text": {"zh_CN": "尺寸"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "medium", "value": "medium"}, {"label": "small", "value": "small"}, {"label": "mini", "value": "mini"}]}}, "description": {"zh_CN": "日期框尺寸。该属性的可选值为 medium / small / mini"}, "labelPosition": "left"}]}, {"name": "1", "label": {"zh_CN": "其他"}, "content": [{"property": "maxlength", "label": {"text": {"zh_CN": "输入最大长度"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "NumberConfigurator", "props": {}}, "description": {"zh_CN": "设置 input 框的maxLength"}}, {"property": "autofocus", "label": {"text": {"zh_CN": "聚焦"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "自动获取焦点"}, "labelPosition": "left"}], "description": {"zh_CN": ""}}], "events": {"onChange": {"label": {"zh_CN": "值改变时触发"}, "description": {"zh_CN": "在 Input 值改变时触发"}, "type": "event", "functionInfo": {"params": [{"name": "value", "type": "string", "defaultValue": "", "description": {"zh_CN": "输入框改变后的值"}}], "returns": {}}, "defaultValue": ""}, "onInput": {"label": {"zh_CN": "输入值改变时触发"}, "description": {"zh_CN": "在 Input 输入值改变时触发"}, "type": "event", "functionInfo": {"params": [{"name": "value", "type": "string", "defaultValue": "", "description": {"zh_CN": "输入框输入的值"}}], "returns": {}}, "defaultValue": ""}, "onUpdate:modelValue": {"label": {"zh_CN": "双向绑定的值改变时触发"}, "description": {"zh_CN": "在 Input 输入值改变时触发"}, "type": "event", "functionInfo": {"params": [{"name": "value", "type": "string", "defaultValue": "", "description": {"zh_CN": "双向绑定的值"}}], "returns": {}}, "defaultValue": ""}, "onBlur": {"label": {"zh_CN": "失去焦点时触发"}, "description": {"zh_CN": "在 Input 失去焦点时触发"}, "type": "event", "functionInfo": {"params": [{"name": "event", "type": "Object", "defaultValue": "", "description": {"zh_CN": "原生 event"}}], "returns": {}}, "defaultValue": ""}, "onFocus": {"label": {"zh_CN": "获取焦点时触发"}, "description": {"zh_CN": "在 Input 获取焦点时触发"}, "type": "event", "functionInfo": {"params": [{"name": "event", "type": "Object", "defaultValue": "", "description": {"zh_CN": "原生 event"}}], "returns": {}}, "defaultValue": ""}, "onClear": {"label": {"zh_CN": "点击清空按钮时触发"}, "description": {"zh_CN": "点击清空按钮时触发"}, "type": "event", "functionInfo": {"params": [], "returns": {}}, "defaultValue": ""}}}, "configure": {"loop": true, "condition": true, "styles": true, "isContainer": true, "isModal": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["value", "disabled"]}, "contextMenu": {"actions": ["create symbol"], "disable": ["copy", "remove"]}}, "snippets": [{"name": {"zh_CN": "日期选择"}, "icon": "datepick", "screenshot": "", "snippetName": "TinyDatePicker", "schema": {"componentName": "TinyDatePicker", "props": {"placeholder": "请输入", "modelValue": ""}}, "category": "form"}]}