import type { Met<PERSON>, StoryObj } from '@storybook/vue3';
import { ref, reactive, computed, watch } from 'vue';
import SixAxisRobot from './index.vue';
import { defaultConfig } from './config';

// 创建深拷贝的配置，避免修改原始配置
const getDefaultPositionList = () =>
  JSON.parse(JSON.stringify(defaultConfig.positionList));

const meta = {
  title: 'Equipment/SixAxisRobot',
  component: SixAxisRobot,
  tags: ['autodocs'],
  argTypes: {
    width: { control: 'number' },
    height: { control: 'number' },
    scale: { control: 'number' },
    left: { control: 'number' },
    top: { control: 'number' },
    animateDuration: { control: 'number' },
    currentPosition: {
      control: 'select',
      options: Object.keys(defaultConfig.positionList),
    },
    positionList: {
      control: false,
      description: '机器人位置配置列表，请使用下方的位置编辑器进行修改',
    },
    material: {
      control: 'object',
      description: '抓手上的物料配置，可以设置物料类型、颜色和尺寸',
    },
  },
  args: {
    width: defaultConfig.width,
    height: defaultConfig.height,
    scale: 1,
    left: defaultConfig.left,
    top: defaultConfig.top,
    animateDuration: defaultConfig.animateDuration,
    currentPosition: defaultConfig.currentPosition,
    positionList: getDefaultPositionList(),
    material: 'cube', // defaultConfig.material,
  },
  parameters: {
    docs: {
      description: {
        component:
          '六轴机器人组件，可以通过配置不同的位置参数来控制机器人的动作。',
      },
    },
  },
} satisfies Meta<typeof SixAxisRobot>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  name: '基础示例',
  args: {},
};

// 添加一个带有位置编辑器的交互示例
export const WithPositionEditor: Story = {
  name: '位置编辑器',
  render: (args: any) => ({
    components: { SixAxisRobot },
    setup() {
      // 使用响应式数据存储位置列表
      const positionList = reactive(getDefaultPositionList());
      const currentPosition = ref(defaultConfig.currentPosition);

      // 创建一个编辑中的副本，用于存储正在编辑的值
      const editingPosition = ref({ ...positionList[currentPosition.value] });

      // 可用的位置列表
      const positionOptions = computed(() => {
        return Object.keys(positionList);
      });

      const currentPositionSymbol = ref(0);

      // 添加新位置
      const addNewPosition = () => {
        const newPositionName = `position${positionOptions.value.length + 1}`;
        // 复制当前位置的配置作为新位置的初始值
        positionList[newPositionName] = JSON.parse(
          JSON.stringify(positionList[currentPosition.value]),
        );
        currentPosition.value = newPositionName;
        // 更新编辑中的副本
        editingPosition.value = { ...positionList[currentPosition.value] };
      };

      // 删除当前位置
      const deleteCurrentPosition = () => {
        if (positionOptions.value.length <= 1) {
          alert('至少需要保留一个位置配置');
          return;
        }

        delete positionList[currentPosition.value];
        currentPosition.value = positionOptions.value[0];
        // 更新编辑中的副本
        editingPosition.value = { ...positionList[currentPosition.value] };
      };

      // 应用编辑的值到实际位置
      const applyChanges = () => {
        // 将编辑中的值应用到实际位置
        Object.assign(
          positionList[currentPosition.value],
          editingPosition.value,
        );
        currentPositionSymbol.value++;
      };

      // 重置编辑的值
      const resetChanges = () => {
        // 重置为当前位置的原始值
        editingPosition.value = { ...positionList[currentPosition.value] };
      };

      // 当当前位置变化时，更新编辑中的副本
      watch(currentPosition, (newPosition) => {
        editingPosition.value = { ...positionList[newPosition] };
      });

      return {
        args,
        positionList,
        currentPosition,
        editingPosition,
        positionOptions,
        addNewPosition,
        deleteCurrentPosition,
        applyChanges,
        resetChanges,
        currentPositionSymbol,
      };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="display: flex; flex-direction: column; gap: 10px; padding: 16px; border: 1px solid #eee; border-radius: 8px;">
          <h3 style="margin: 0 0 10px 0;">位置编辑器</h3>
          
          <div style="display: flex; gap: 10px; align-items: center;">
            <label>当前位置:</label>
            <select v-model="currentPosition" style="padding: 6px; border-radius: 4px; border: 1px solid #ccc;">
              <option v-for="option in positionOptions" :key="option" :value="option">{{ option }}</option>
            </select>
            <button @click="addNewPosition" style="padding: 6px 12px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">添加位置</button>
            <button @click="deleteCurrentPosition" style="padding: 6px 12px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">删除位置</button>
          </div>
          
          <div v-if="editingPosition" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
            
          <div style="display: flex; flex-direction: column; gap: 5px;">
            <label>第一节臂长度:</label>
            <input type="range" 
              v-model.number="editingPosition.firstArmLength" 
              min="50" max="200" step="5" 
              style="width: 100%;">
            <div style="display: flex; justify-content: space-between;">
              <span>50</span>
              <span>{{ editingPosition.firstArmLength }}</span>
              <span>200</span>
            </div>
          </div>

          <div style="display: flex; flex-direction: column; gap: 5px;">
              <label>第一节臂角度:</label>
              <input type="range" 
                v-model.number="editingPosition.firstArmAngle" 
                min="-90" max="90" step="1" 
                style="width: 100%;">
              <div style="display: flex; justify-content: space-between;">
                <span>-90°</span>
                <span>{{ editingPosition.firstArmAngle }}°</span>
                <span>90°</span>
              </div>
            </div>

            <div style="display: flex; flex-direction: column; gap: 5px;">
              <label>第二节臂长度:</label>
              <input type="range" 
                v-model.number="editingPosition.secondArmLength" 
                min="50" max="200" step="5" 
                style="width: 100%;">
              <div style="display: flex; justify-content: space-between;">
                <span>50</span>
                <span>{{ editingPosition.secondArmLength }}</span>
                <span>200</span>
              </div>
            </div>
            
            <div style="display: flex; flex-direction: column; gap: 5px;">
              <label>第二节臂角度:</label>
              <input type="range" 
                v-model.number="editingPosition.secondArmAngle" 
                min="-90" max="90" step="1" 
                style="width: 100%;">
              <div style="display: flex; justify-content: space-between;">
                <span>-90°</span>
                <span>{{ editingPosition.secondArmAngle }}°</span>
                <span>90°</span>
              </div>
            </div>

            <div style="display: flex; flex-direction: column; gap: 5px;">
              <label>第三节臂长度:</label>
              <input type="range" 
                v-model.number="editingPosition.thirdArmLength" 
                min="50" max="200" step="5" 
                style="width: 100%;">
              <div style="display: flex; justify-content: space-between;">
                <span>50</span>
                <span>{{ editingPosition.thirdArmLength }}</span>
                <span>200</span>
              </div>
            </div>
            
            <div style="display: flex; flex-direction: column; gap: 5px;">
              <label>第三节臂角度:</label>
              <input type="range" 
                v-model.number="editingPosition.thirdArmAngle" 
                min="-90" max="90" step="1" 
                style="width: 100%;">
              <div style="display: flex; justify-content: space-between;">
                <span>-90°</span>
                <span>{{ editingPosition.thirdArmAngle }}°</span>
                <span>90°</span>
              </div>
            </div>
            
            <div style="display: flex; flex-direction: column; gap: 5px;">
              <label>爪子角度:</label>
              <input type="range" 
                v-model.number="editingPosition.gripperAngle" 
                min="-90" max="90" step="1" 
                style="width: 100%;">
              <div style="display: flex; justify-content: space-between;">
                <span>-90°</span>
                <span>{{ editingPosition.gripperAngle }}°</span>
                <span>90°</span>
              </div>
            </div>
            
            <div style="display: flex; flex-direction: column; gap: 5px;">
              <label>爪子动画:</label>
              <select 
                v-model.number="editingPosition.gripperAnimate" 
                style="padding: 6px; border-radius: 4px; border: 1px solid #ccc;">
                <option :value="0">关闭</option>
                <option :value="1">开启</option>
              </select>
            </div>
            
            <div style="display: flex; flex-direction: column; gap: 5px;">
              <label>结束位置 (可选):</label>
              <select 
                v-model="editingPosition.endPosition" 
                style="padding: 6px; border-radius: 4px; border: 1px solid #ccc;">
                <option value="">无</option>
                <option v-for="option in positionOptions" :key="option" :value="option">{{ option }}</option>
              </select>
            </div>
          </div>
          
          <div style="display: flex; gap: 10px; margin-top: 10px;">
            <button 
              @click="applyChanges" 
              style="padding: 8px 16px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer; flex: 1;">
              应用更改
            </button>
            <button 
              @click="resetChanges" 
              style="padding: 8px 16px; background: #9E9E9E; color: white; border: none; border-radius: 4px; cursor: pointer; flex: 1;">
              重置更改
            </button>
          </div>
        </div>
        
        <div style="border: 1px solid #eee; border-radius: 8px; padding: 16px; height: 500px;">
          <SixAxisRobot 
            :key="currentPositionSymbol"
            :width="args.width" 
            :height="args.height"
            :scale="args.scale"
            :left="args.left"
            :top="args.top"
            :animateDuration="args.animateDuration"
            :currentPosition="currentPosition"
            :positionList="positionList"
          />
        </div>
        
        <div style="border: 1px solid #eee; border-radius: 8px; padding: 16px;">
          <h3 style="margin: 0 0 10px 0;">当前配置</h3>
          <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; overflow: auto; max-height: 300px;">{{ JSON.stringify(positionList, null, 2) }}</pre>
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story:
          '这个示例提供了一个交互式的位置编辑器，您可以调整机器人的各个参数，然后点击"应用更改"按钮查看效果。',
      },
    },
  },
};

// 其他示例保持不变
export const Position1: Story = {
  name: '位置1',
  args: {
    currentPosition: 'position1',
  },
};

export const Position2: Story = {
  name: '位置2',
  args: {
    currentPosition: 'position2',
  },
};

export const CustomPosition: Story = {
  name: '自定义坐标',
  args: {
    left: 100,
    top: 350,
  },
};

// 物料演示示例
export const WithBlockMaterial: Story = {
  name: '携带木块',
  args: {
    material: {
      type: 'block',
      color: '#8B4513',
      label: '木块',
    },
  },
};

export const WithCubeMaterial: Story = {
  name: '携带魔方',
  args: {
    material: {
      type: 'cube',
      color: '#FF6B6B',
      label: '魔方',
    },
  },
};

export const WithSphereMaterial: Story = {
  name: '携带球体',
  args: {
    material: {
      type: 'sphere',
      color: '#4ECDC4',
      size: { width: 20, height: 20 },
      label: '球体',
    },
  },
};

export const WithCylinderMaterial: Story = {
  name: '携带圆柱体',
  args: {
    material: {
      type: 'cylinder',
      color: '#95A5A6',
      size: { width: 16, height: 24 },
      label: '圆柱体',
    },
  },
};

export const WithTrayMaterial: Story = {
  name: '携带托盘',
  args: {
    material: {
      type: 'tray',
      color: '#E8E8E8',
      size: { width: 28, height: 12 },
      label: '托盘',
    },
  },
};

// 字符串形式的物料演示
export const WithStringMaterial: Story = {
  name: '字符串形式物料',
  args: {
    material: 'block', // 字符串形式，等同于使用默认配置的木块
  },
};

// 交互式物料切换示例
export const MaterialSwitcher: Story = {
  name: '物料切换器',
  render: (args: any) => ({
    components: { SixAxisRobot },
    setup() {
      const currentMaterial = ref(null);

      const materialOptions = [
        { label: '无物料', value: null },
        {
          label: '木块',
          value: { type: 'block', color: '#8B4513', label: '木块' },
        },
        {
          label: '魔方',
          value: { type: 'cube', color: '#FF6B6B', label: '魔方' },
        },
        {
          label: '球体',
          value: { type: 'sphere', color: '#4ECDC4', label: '球体' },
        },
        {
          label: '圆柱体',
          value: { type: 'cylinder', color: '#95A5A6', label: '圆柱体' },
        },
        {
          label: '托盘',
          value: { type: 'tray', color: '#E8E8E8', label: '托盘' },
        },
        {
          label: '字符串木块',
          value: 'block', // 字符串形式
        },
        {
          label: '字符串托盘',
          value: 'tray', // 字符串形式
        },
      ];

      return {
        args,
        currentMaterial,
        materialOptions,
      };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="display: flex; gap: 10px; align-items: center; padding: 16px; border: 1px solid #eee; border-radius: 8px;">
          <label>选择物料:</label>
          <select v-model="currentMaterial" style="padding: 6px; border-radius: 4px; border: 1px solid #ccc;">
            <option v-for="option in materialOptions" :key="option.label" :value="option.value">
              {{ option.label }}
            </option>
          </select>
        </div>

        <div style="border: 1px solid #eee; border-radius: 8px; padding: 16px; height: 500px;">
          <SixAxisRobot
            :width="args.width"
            :height="args.height"
            :scale="args.scale"
            :left="args.left"
            :top="args.top"
            :animateDuration="args.animateDuration"
            :currentPosition="args.currentPosition"
            :positionList="args.positionList"
            :material="currentMaterial"
          />
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story:
          '这个示例提供了一个交互式的物料切换器，您可以选择不同的物料类型来查看机械臂抓手的效果。',
      },
    },
  },
};
