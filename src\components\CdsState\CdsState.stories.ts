import { Meta, StoryObj } from '@storybook/vue3';
import CdsState from './index.vue';

// CdsState 组件的 Storybook 配置
const meta = {
  title: 'Equipment/CdsState',
  component: CdsState,
  tags: ['autodocs'],
  argTypes: {
    width: { control: 'number' },
    height: { control: 'number' },
    name: { control: 'text' },
    no: { control: 'text' },
    tankState: { control: 'text' },
    backgroundColor: { control: 'color' },
    textColor: { control: 'color' },
  },
  args: {
    name: '储罐名称',
    no: 'T-001',
    tankState: '正常',
    backgroundColor: '#bcbcbc',
    textColor: '#131313',
  },
} satisfies Meta<typeof CdsState>;

export default meta;
type Story = StoryObj<typeof meta>;

// 默认状态
export const Default: Story = {
  name: '默认',
  args: {
    tankState: 'Enable',
  },
};

// 禁用
export const Disabled: Story = {
  name: '禁用',
  args: {
    tankState: 'Disabled',
    backgroundColor: '#fff3cd',
    textColor: '#856404',
  },
};

// 错误状态
export const Error: Story = {
  name: '自定义样式',
  args: {
    tankState: '错误',
    backgroundColor: '#f8d7da',
    textColor: '#721c24',
  },
};

// 自定义尺寸
export const CustomSize: Story = {
  name: '自定义尺寸',
  args: {
    width: 500,
    height: 60,
    name: '大型储罐',
    no: 'XL-002',
  },
};
