<template>
  <div
    class="transfer-belt-container"
    :style="containerStyle"
    :class="containerClasses"
  >
    <div class="belt" :style="beltStyle"></div>
    <div class="items-container">
      <div
        v-for="item in state.items"
        :key="item.id"
        class="belt-item"
        :style="getItemStyle(item)"
      >
        <!-- 如果有物料配置，显示物料组件；否则显示文本内容 -->
        <MaterialItem
          v-if="item.material"
          :material="item.material"
          :belt-height="height"
          :size-ratio="0.7"
        />
        <span v-else class="item-content">{{ item.content }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, computed, toRefs } from 'vue';
import { createComponentProps } from '@/context/ComponentContext';
import { useComponentState } from '@/communication/hooks/useComponentState';
import Color from 'color';
import MaterialItem from './components/MaterialItem.vue';
import { BeltItem, BeltItemsInput } from './types';
import { normalizeBeltItemsInput } from './hooks/useMaterial';

const BELT_TEXTURE_SIZE = 40; // in pixels

export default defineComponent({
  name: 'TransferV2',
  components: {
    MaterialItem,
  },
  props: createComponentProps({
    width: {
      type: Number,
      default: 500,
      description: '传送带的宽度',
    },
    height: {
      type: Number,
      default: 80,
      description: '传送带的高度',
    },
    moving: {
      type: Boolean,
      default: true,
      description: '传送带是否在移动',
    },
    speed: {
      type: Number,
      default: 50, // pixels per second
      description: '传送带的速度 (像素/秒)',
    },
    direction: {
      type: String as PropType<'forward' | 'backward' | 'up' | 'down'>,
      default: 'forward',
      description: '传送带的方向 (forward/backward 或 up/down)',
    },
    items: {
      type: Array as PropType<BeltItemsInput>,
      default: () => [],
      description:
        '传送带上的物品，支持字符串数组、物料配置数组或完整BeltItem数组',
    },
    color: {
      type: String,
      default: '#c0c0c0',
      description: '传送带的背景颜色',
    },
    orientation: {
      type: String as PropType<'horizontal' | 'vertical'>,
      default: 'horizontal',
      description: '传送带的方向：水平或垂直',
    },
    stopAtEnd: {
      type: Boolean,
      default: false,
      description: '物料是否在传送带结尾处停止，而不是循环',
    },
  }),
  setup(props) {
    const { moving, speed, direction, items, stopAtEnd } = toRefs(props);

    // 标准化 items 输入
    const normalizedItems = computed(() => {
      return normalizeBeltItemsInput(items.value);
    });

    // 使用组件状态Hook（响应式工厂函数版本）
    const { state } = useComponentState(
      props.partId,
      () => ({
        moving: moving.value,
        speed: speed.value,
        direction: direction.value,
        items: normalizedItems.value,
        stopAtEnd: stopAtEnd.value,
      }),
      props.dynamicPartInfo,
    );

    const isVertical = computed(() => props.orientation === 'vertical');

    const containerClasses = computed(() => ({
      'is-moving': state.moving,
      'is-vertical': isVertical.value,
      'stop-at-end': state.stopAtEnd,
    }));

    const containerStyle = computed(() => {
      const baseColor = Color(props.color);
      const borderColor = baseColor.darken(0.3).hex();
      return {
        width: `${props.width}px`,
        height: `${props.height}px`,
        '--belt-width': `${props.width}px`,
        '--belt-height': `${props.height}px`,
        '--belt-background-color': props.color,
        '--belt-border-color': borderColor,
      };
    });

    const beltStyle = computed(() => {
      const safeSpeed = Math.max(1, state.speed);
      const animationDuration = BELT_TEXTURE_SIZE / safeSpeed;

      const baseColor = Color(props.color);
      const darkColor1 = baseColor.darken(0.15).hex();
      const darkColor2 = baseColor.darken(0.25).hex();

      const gradientDirection = isVertical.value ? '0deg' : '90deg';
      const backgroundImage = `linear-gradient(${gradientDirection}, ${darkColor1} 50%, ${darkColor2} 50%)`;
      const backgroundSize = isVertical.value
        ? `100% ${BELT_TEXTURE_SIZE}px`
        : `${BELT_TEXTURE_SIZE}px 100%`;

      return {
        'animation-duration': `${animationDuration}s`,
        'animation-direction':
          state.direction === 'backward' || state.direction === 'up'
            ? 'reverse'
            : 'normal',
        'background-image': backgroundImage,
        'background-size': backgroundSize,
      };
    });

    const itemAnimationDuration = computed(() => {
      const safeSpeed = Math.max(1, state.speed);
      const distance = isVertical.value ? props.height : props.width;
      return distance / safeSpeed;
    });

    const getItemStyle = (item: BeltItem) => {
      const totalDuration = itemAnimationDuration.value;
      const delay = -(totalDuration * item.position) / 100;

      return {
        'animation-duration': `${totalDuration}s`,
        'animation-delay': `${delay}s`,
        'animation-direction':
          state.direction === 'backward' || state.direction === 'up'
            ? 'reverse'
            : 'normal',
      };
    };

    return {
      state,
      containerStyle,
      containerClasses,
      beltStyle,
      getItemStyle,
    };
  },
});
</script>

<style scoped>
.transfer-belt-container {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
}

/* Horizontal Styles */
.transfer-belt-container:not(.is-vertical) {
  border-top: 4px solid var(--belt-border-color);
  border-bottom: 4px solid var(--belt-border-color);
}

/* Vertical Styles */
.transfer-belt-container.is-vertical {
  border-left: 4px solid var(--belt-border-color);
  border-right: 4px solid var(--belt-border-color);
}

.belt {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-play-state: paused;
}

.is-moving .belt {
  animation-play-state: running;
}

/* Horizontal Belt Texture */
.transfer-belt-container:not(.is-vertical) .belt {
  animation-name: move-belt-texture-horizontal;
}

/* Vertical Belt Texture */
.transfer-belt-container.is-vertical .belt {
  animation-name: move-belt-texture-vertical;
}

.items-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.belt-item {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  /* animation-play-state: paused; */
}

.transfer-belt-container.is-moving .belt-item {
  animation-play-state: running;
}

/* 传统文本内容样式 */
.item-content {
  background-color: #f0a04b;
  border: 2px solid #d2822a;
  color: white;
  font-weight: bold;
  border-radius: 4px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 30px;
  min-height: 30px;
}

/* Horizontal Item - 循环模式 (默认) */
.transfer-belt-container:not(.is-vertical):not(.stop-at-end) .belt-item {
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  animation-name: move-item-horizontal-loop;
  animation-iteration-count: infinite;
  animation-fill-mode: none;
}

/* Horizontal Item - 停止模式 */
.transfer-belt-container:not(.is-vertical).stop-at-end .belt-item {
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  animation-name: move-item-horizontal-stop;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
}

/* Vertical Item - 循环模式 (默认) */
.transfer-belt-container.is-vertical:not(.stop-at-end) .belt-item {
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 50px;
  animation-name: move-item-vertical-loop;
  animation-iteration-count: infinite;
  animation-fill-mode: none;
}

/* Vertical Item - 停止模式 */
.transfer-belt-container.is-vertical.stop-at-end .belt-item {
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 50px;
  animation-name: move-item-vertical-stop;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
}

/* Keyframes */
@keyframes move-belt-texture-horizontal {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 40px 0;
  }
}

@keyframes move-belt-texture-vertical {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 0 40px;
  }
}

/* 水平循环动画 - 物料从左到右，然后重新开始 */
@keyframes move-item-horizontal-loop {
  from {
    left: -50px;
  }
  to {
    left: var(--belt-width);
  }
}

/* 水平停止动画 - 物料从左到右，停在结尾处 */
@keyframes move-item-horizontal-stop {
  from {
    left: -50px;
  }
  to {
    left: calc(var(--belt-width) - 60px);
  }
}

/* 垂直循环动画 - 物料从上到下，然后重新开始 */
@keyframes move-item-vertical-loop {
  from {
    top: -50px;
  }
  to {
    top: var(--belt-height);
  }
}

/* 垂直停止动画 - 物料从上到下，停在结尾处 */
@keyframes move-item-vertical-stop {
  from {
    top: -50px;
  }
  to {
    top: calc(var(--belt-height) - 60px);
  }
}
</style>
