import type { Meta, StoryObj } from '@storybook/vue3';
import { Chamber } from './index';

const meta = {
  title: 'Equipment/Chamber',
  component: Chamber,
  tags: ['autodocs'],
  argTypes: {
    equipmentWidth: { control: 'number', description: '腔体宽度（像素）' },
    equipmentHeight: { control: 'number', description: '腔体高度（像素）' },
    name: { control: 'text', description: '腔体名称' },
    LTConner: {
      control: 'select',
      options: ['rect', 'round'],
      description: '左上角形状',
    },
    LBConner: {
      control: 'select',
      options: ['rect', 'round'],
      description: '左下角形状',
    },
    RTConner: {
      control: 'select',
      options: ['rect', 'round'],
      description: '右上角形状',
    },
    RBConner: {
      control: 'select',
      options: ['rect', 'round'],
      description: '右下角形状',
    },
    waferColor: { control: 'color', description: '晶圆颜色' },
    connerColor: { control: 'color', description: '角落颜色' },
    backgroundColor: { control: 'color', description: '背景颜色' },
    waferType: {
      control: 'select',
      options: ['circle', 'square'],
      description: '晶圆形状',
    },
    waferState: { control: 'boolean', description: '是否显示晶圆' },
    waferName: { control: 'text', description: '晶圆名称' },
    waferSize: { control: 'number', description: '晶圆尺寸' },
    waferFontSize: { control: 'number', description: '晶圆字体大小' },
    isDisabled: { control: 'boolean', description: '是否禁用' },
    link: { control: 'text', description: '点击链接' },
    forSelect: { control: 'boolean', description: '是否可选择' },
  },
  args: {
    equipmentWidth: 200,
    equipmentHeight: 200,
    name: 'Chamber',
    LTConner: 'rect',
    LBConner: 'rect',
    RTConner: 'rect',
    RBConner: 'rect',
  },
  parameters: {
    docs: {
      description: {
        component:
          'Chamber组件用于显示半导体设备中的腔体，支持自定义角落形状、晶圆显示等功能。',
      },
    },
    controls: { expanded: true },
  },
} satisfies Meta<typeof Chamber>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  name: '基础示例',
  args: {
    name: 'Chamber1',
  },
};

// 圆角腔体
export const RoundedCorners: Story = {
  name: '圆角腔体',
  args: {
    LTConner: 'round',
    LBConner: 'round',
    RTConner: 'round',
    RBConner: 'round',
    connerColor: '#1677ff',
  },
};

// 带晶圆的腔体
export const WithWafer: Story = {
  name: '带晶圆的腔体',
  args: {
    waferSize: 150,
    waferState: true,
    waferName: 'W01',
    waferColor: '#1677ff',
  },
};

// 混合角落形状
export const MixedCorners: Story = {
  name: '混合角落形状',
  args: {
    LTConner: 'round',
    LBConner: 'rect',
    RTConner: 'rect',
    RBConner: 'round',
    connerColor: '#f5222d',
  },
};
// 不同尺寸的腔体
export const DifferentSizes: Story = {
  name: '不同尺寸',
  render: (args) => ({
    components: { Chamber },
    setup() {
      return { args };
    },
    template: `
      <div style="display: flex; gap: 20px; flex-wrap: wrap;">
        <div style="text-align: center;">
          <Chamber
            v-bind="args"
            :equipmentWidth="100"
            :equipmentHeight="100"
            name="小"
          />
          <div style="margin-top: 10px;">100 x 100</div>
        </div>
        <div style="text-align: center;">
          <Chamber
            v-bind="args"
            :equipmentWidth="150"
            :equipmentHeight="150"
            name="中"
          />
          <div style="margin-top: 10px;">150 x 150</div>
        </div>
        <div style="text-align: center;">
          <Chamber
            v-bind="args"
            :equipmentWidth="200"
            :equipmentHeight="200"
            name="大"
          />
          <div style="margin-top: 10px;">200 x 200</div>
        </div>
        <div style="text-align: center;">
          <Chamber
            v-bind="args"
            :equipmentWidth="250"
            :equipmentHeight="250"
            name="特大"
          />
          <div style="margin-top: 10px;">250 x 250</div>
        </div>
      </div>
    `,
  }),
};

// 不同背景颜色
export const DifferentBackgrounds: Story = {
  name: '不同背景颜色',
  render: (args) => ({
    components: { Chamber },
    setup() {
      const backgrounds = [
        { name: '白色', value: '#ffffff' },
        { name: '浅灰', value: '#f0f0f0' },
        { name: '蓝色', value: '#e6f7ff' },
        { name: '绿色', value: '#f6ffed' },
        { name: '红色', value: '#fff1f0' },
        { name: '黄色', value: '#fffbe6' },
      ];

      return { args, backgrounds };
    },
    template: `
      <div style="display: flex; gap: 20px; flex-wrap: wrap;">
        <div v-for="bg in backgrounds" :key="bg.value" style="text-align: center;">
          <Chamber
            v-bind="args"
            :backgroundColor="bg.value"
            :name="bg.name"
          />
          <div style="margin-top: 10px;">{{ bg.name }}</div>
        </div>
      </div>
    `,
  }),
};
