import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import StorageBox from './index.vue';
import type { GridItem } from './index.vue';

const meta: Meta<typeof StorageBox> = {
  title: 'Equipment/StorageBox',
  component: StorageBox,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          '基于斜二测投影的2.5D收纳盒组件，使用Pixi.js渲染，支持多种格子状态和物品显示。',
      },
    },
  },
  argTypes: {
    title: {
      control: 'text',
      description: '收纳盒标题',
    },
    showTitle: {
      control: 'boolean',
      description: '是否显示标题',
    },
    boxColor: {
      control: 'color',
      description: '盒体颜色',
    },
    width: {
      control: { type: 'range', min: 50, max: 800, step: 50 },
      description: '盒体宽度（像素）',
    },
    height: {
      control: { type: 'range', min: 50, max: 600, step: 50 },
      description: '盒体高度（像素）',
    },
    rows: {
      control: { type: 'range', min: 1, max: 10, step: 1 },
      description: '收纳盒行数',
    },
    columns: {
      control: { type: 'range', min: 1, max: 12, step: 1 },
      description: '收纳盒列数',
    },
    showGridNumbers: {
      control: 'boolean',
      description: '是否显示格子编号',
    },
    titleColor: {
      control: 'color',
      description: '标题颜色',
    },
    titleFontSize: {
      control: { type: 'range', min: 12, max: 24, step: 1 },
      description: '标题字体大小',
    },
    itemStartPosition: {
      control: { type: 'select' },
      options: ['top-left', 'top-right', 'bottom-left', 'bottom-right'],
      description: '物品排列的起始位置：左上、右上、左下、右下',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'top-left' },
      },
    },
    sortDirection: {
      control: { type: 'select' },
      options: ['horizontal', 'vertical'],
      description: '物品排列顺序',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'horizontal' },
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

// 生成示例物品数据
const generateItems = (
  rows: number,
  columns: number,
  occupiedCount: number = 5,
): GridItem[] => {
  const items: GridItem[] = [];
  const totalGrids = rows * columns;

  // 随机生成有物品的格子
  const occupiedIndices = new Set<number>();
  while (occupiedIndices.size < Math.min(occupiedCount, totalGrids)) {
    occupiedIndices.add(Math.floor(Math.random() * totalGrids));
  }

  for (let i = 0; i < totalGrids; i++) {
    if (occupiedIndices.has(i)) {
      items.push({
        name: `物品${i + 1}`,
        status: 'Completed',
      });
    } else if (Math.random() < 0.1) {
      // 10%概率是禁用格子
      items.push({
        status: 'Failed',
      });
    } else {
      items.push({
        status: 'Idled',
      });
    }
  }

  return items;
};

// 基础示例
export const Default: Story = {
  args: {
    title: '收纳盒',
    showTitle: false,
    boxColor: '#f5f5f5',
    width: 200,
    height: 150,
    rows: 4,
    columns: 2,
    items: [
      { status: 'Completed', name: '物品1' },
      { status: 'Failed', name: '物品2' },
    ],
    statusToColor: {
      Idled: '#909399',
      Processing: '#409EFF',
      Completed: '#67C23A',
      Aborted: '#E6A23C',
      Failed: '#DC3545',
      Returning: '#9370DB',
      Returned: '#B0C4DE',
    },
    showGridNumbers: false,
    titleColor: '#333333',
    titleFontSize: 16,
    itemStartPosition: 'bottom-right',
  },
};

// 显示格子编号
export const WithGridNumbers: Story = {
  args: {
    ...Default.args,
    title: '带编号的收纳盒',
    showGridNumbers: true,
    items: generateItems(3, 5, 6),
    rows: 3,
    columns: 5,
  },
};

// 大尺寸收纳盒
export const LargeBox: Story = {
  args: {
    ...Default.args,
    title: '大型收纳盒',
    width: 700,
    height: 500,
    rows: 6,
    columns: 8,
    items: generateItems(6, 8, 15),
    boxColor: '#e8f4f8',
  },
};

// 小尺寸收纳盒
export const SmallBox: Story = {
  args: {
    ...Default.args,
    title: '小型收纳盒',
    width: 300,
    height: 200,
    rows: 2,
    columns: 3,
    items: generateItems(2, 3, 3),
    boxColor: '#fff2e6',
  },
};

// 自定义颜色
export const CustomColors: Story = {
  args: {
    ...Default.args,
    title: '自定义颜色收纳盒',
    boxColor: '#f0f8ff',
    titleColor: '#2c3e50',
    statusToColor: {
      occupied: '#e74c3c',
      empty: '#f8f9fa',
      disabled: '#dee2e6',
    },
    items: [
      { status: 'occupied', name: '红色物品' },
      { status: 'occupied', name: '蓝色物品' },
      { status: 'occupied', name: '绿色物品' },
      { status: 'empty' },
      { status: 'disabled' },
      { status: 'occupied', name: '橙色物品' },
      { status: 'empty' },
      { status: 'occupied', name: '紫色物品' },
      { status: 'empty' },
      { status: 'occupied', name: '青色物品' },
      ...Array(14).fill({ status: 'empty' }),
    ],
  },
};

// 几乎满载的收纳盒
export const FullyLoaded: Story = {
  args: {
    ...Default.args,
    title: '满载收纳盒',
    items: generateItems(4, 6, 20),
    showGridNumbers: true,
  },
};

// 状态颜色映射示例
export const StatusColorMapping: Story = {
  args: {
    ...Default.args,
    title: '状态颜色映射示例',
    showTitle: true,
    statusToColor: {
      finished: '#2ecc71',
      wait: '#3498db',
      occupied: '#e74c3c',
      empty: '#ecf0f1',
      disabled: '#95a5a6',
    },
    items: [
      { status: 'occupied', name: '已占用' },
      { status: 'empty', name: '空置' },
      { status: 'disabled', name: '禁用' },
      { status: 'occupied', name: '已占用' },
      { status: 'empty', name: '空置' },
      { status: 'occupied', name: '已占用' },
      { status: 'disabled', name: '禁用' },
      { status: 'empty', name: '空置' },
      ...Array(16).fill({ status: 'empty' }),
    ],
  },
};

// 空收纳盒
export const EmptyBox: Story = {
  args: {
    ...Default.args,
    title: '空收纳盒',
    items: Array(24).fill({ status: 'empty' }),
    showGridNumbers: true,
  },
};

// 不同盒体颜色对比
export const ColorVariations: Story = {
  args: {
    ...Default.args,
    title: '深色收纳盒',
    boxColor: '#34495e',
    titleColor: '#ecf0f1',
    items: generateItems(4, 6, 10),
  },
  parameters: {
    backgrounds: {
      default: 'dark',
    },
  },
};

// 长方形收纳盒
export const RectangularBox: Story = {
  args: {
    ...Default.args,
    title: '长方形收纳盒',
    width: 600,
    height: 300,
    rows: 3,
    columns: 8,
    items: generateItems(3, 8, 12),
  },
};

// 正方形收纳盒
export const SquareBox: Story = {
  args: {
    ...Default.args,
    title: '正方形收纳盒',
    width: 400,
    height: 400,
    rows: 5,
    columns: 5,
    items: generateItems(5, 5, 12),
  },
};

// 物品起始位置演示
export const ItemStartPositions: Story = {
  args: {
    ...Default.args,
    title: '物品起始位置演示',
    showTitle: true,
    width: 400,
    height: 300,
    rows: 3,
    columns: 4,
    showGridNumbers: true,
    itemStartPosition: 'top-left',
    items: [
      { status: 'Completed', name: '物品1' },
      { status: 'Processing', name: '物品2' },
      { status: 'Aborted', name: '物品3' },
      { status: 'Failed', name: '物品4' },
      { status: 'Returning', name: '物品5' },
      { status: 'Returned', name: '物品6' },
      ...Array(6).fill({ status: 'Idled' }),
    ],
  },
};

// 多主题MQTT自定义解析演示（访问组件props）
export const MultiTopicMqttWithPropsDemo: Story = {
  args: {
    ...Default.args,
    title: '多主题MQTT收纳盒（访问组件Props）',
    showTitle: true,
    width: 500,
    height: 350,
    rows: 4,
    columns: 6,
    items: [], // 初始为空，通过MQTT动态更新
    showGridNumbers: true,
    showItemNames: true,
    dynamicPartInfo: {
      items: {
        type: 'mqtt',
        baseUrl: 'ws://192.168.0.212:8083/mqtt',
        topic:
          'guiMessage/component/RobotJK,guiMessage/component/RobotAGV,guiMessage/component/s3,guiMessage/component/s4',
        qos: 1,
        bodyPath: 'Payload',
        parseType: 'func',
        contentFields: `function parseMessage(result, componentProps, index) {
          // 可以访问组件的props，如rows, columns, showItemNames等
          const totalSlots = componentProps.rows * componentProps.columns;
          const slotName = componentProps.showItemNames ? 'Slot ' + index : '';
          const isFirstHalf = index < totalSlots / 2;

          return result.MaterialInfos?.find(item =>
            item.CurrentModule === 'StorageBox' &&
            item.CurrentSlot === index
          )?.status || {
            status: 'empty',
            name: slotName,
            color: isFirstHalf ? '#e0e0e0' : '#f0f0f0'
          };
        }`,
      },
    },
  },
  parameters: {
    docs: {
      description: {
        story: `
这个演示展示了如何在MQTT自定义解析函数中访问组件的props。

**新功能特性：**
- 自定义函数现在接收三个参数：\`(result, componentProps, index)\`
- 可以访问组件的所有props（除了dynamicPartInfo）
- 支持基于props的动态逻辑处理

**函数参数说明：**
- \`result\`: bodyPath下的数据
- \`componentProps\`: 组件props对象（已排除dynamicPartInfo）
- \`index\`: 多主题时的索引

**示例用法：**
\`\`\`javascript
function parseMessage(result, componentProps, index) {
  // 访问组件props
  const totalSlots = componentProps.rows * componentProps.columns;
  const showNames = componentProps.showItemNames;

  // 基于props的逻辑处理
  const slotName = showNames ? 'Slot ' + index : '';
  const isFirstHalf = index < totalSlots / 2;

  return result.MaterialInfos?.find(item =>
    item.CurrentModule === 'StorageBox' &&
    item.CurrentSlot === index
  )?.status || {
    status: 'empty',
    name: slotName,
    color: isFirstHalf ? '#e0e0e0' : '#f0f0f0'
  };
}
\`\`\`

**可访问的组件props包括：**
- \`rows\`, \`columns\`: 收纳盒规格
- \`showItemNames\`, \`showGridNumbers\`: 显示配置
- \`width\`, \`height\`: 尺寸配置
- \`title\`, \`titleColor\`: 标题配置
- 等等...（除了dynamicPartInfo）
        `,
      },
    },
  },
};
