import type { Meta, StoryObj } from '@storybook/vue3';
import IsometricPunchPress from './index.vue';

const meta = {
  title: 'Equipment/IsometricPunchPress',
  component: IsometricPunchPress,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          '桌上台式小冲床组件 - 一个形象化的2D冲床展示组件，支持多种状态显示和响应式尺寸。',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    width: {
      control: { type: 'range', min: 50, max: 500, step: 10 },
      description: '组件宽度',
    },
    height: {
      control: { type: 'range', min: 50, max: 500, step: 10 },
      description: '组件高度',
    },
    status: {
      control: { type: 'select' },
      options: ['idle', 'working', 'error', 'maintenance'],
      description: '冲床状态',
    },
  },
  args: {
    width: 200,
    height: 200,
    status: 'idle',
  },
} satisfies Meta<typeof IsometricPunchPress>;

export default meta;
type Story = StoryObj<typeof meta>;

// 默认状态
export const Default: Story = {
  args: {
    width: 200,
    height: 200,
    status: 'idle',
  },
};

// 空闲状态
export const Idle: Story = {
  args: {
    width: 200,
    height: 200,
    status: 'idle',
  },
  parameters: {
    docs: {
      description: {
        story: '空闲状态的冲床，显示为蓝灰色调，处于静止状态。',
      },
    },
  },
};

// 工作状态
export const Working: Story = {
  args: {
    width: 200,
    height: 200,
    status: 'working',
  },
  parameters: {
    docs: {
      description: {
        story: '工作状态的冲床，显示为绿色调，冲头会有上下运动的动画效果。',
      },
    },
  },
};

// 错误状态
export const Error: Story = {
  args: {
    width: 200,
    height: 200,
    status: 'error',
  },
  parameters: {
    docs: {
      description: {
        story: '错误状态的冲床，显示为红色调，状态指示灯会闪烁警示。',
      },
    },
  },
};

// 维护状态
export const Maintenance: Story = {
  args: {
    width: 200,
    height: 200,
    status: 'maintenance',
  },
  parameters: {
    docs: {
      description: {
        story: '维护状态的冲床，显示为橙色调，表示需要进行维护保养。',
      },
    },
  },
};

// 小尺寸
export const Small: Story = {
  args: {
    width: 100,
    height: 100,
    status: 'idle',
  },
  parameters: {
    docs: {
      description: {
        story: '小尺寸的冲床组件，适用于仪表板或列表展示。',
      },
    },
  },
};

// 大尺寸
export const Large: Story = {
  args: {
    width: 400,
    height: 400,
    status: 'working',
  },
  parameters: {
    docs: {
      description: {
        story: '大尺寸的冲床组件，适用于详细展示或主要显示区域。',
      },
    },
  },
};

// 不同尺寸对比
export const SizeComparison: Story = {
  render: (args) => ({
    components: { IsometricPunchPress },
    setup() {
      return { args };
    },
    template: `
      <div style="display: flex; align-items: center; gap: 20px; flex-wrap: wrap;">
        <div style="text-align: center;">
          <IsometricPunchPress :width="80" :height="80" status="idle" />
          <p style="margin: 10px 0 0 0; font-size: 12px;">80x80</p>
        </div>
        <div style="text-align: center;">
          <IsometricPunchPress :width="120" :height="120" status="working" />
          <p style="margin: 10px 0 0 0; font-size: 12px;">120x120</p>
        </div>
        <div style="text-align: center;">
          <IsometricPunchPress :width="200" :height="200" status="error" />
          <p style="margin: 10px 0 0 0; font-size: 12px;">200x200</p>
        </div>
        <div style="text-align: center;">
          <IsometricPunchPress :width="300" :height="300" status="maintenance" />
          <p style="margin: 10px 0 0 0; font-size: 12px;">300x300</p>
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: '不同尺寸和状态的冲床组件对比展示。',
      },
    },
  },
};

// 状态切换演示
export const StatusDemo: Story = {
  render: (args) => ({
    components: { IsometricPunchPress },
    setup() {
      return { args };
    },
    template: `
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; align-items: center;">
        <div style="text-align: center;">
          <IsometricPunchPress :width="180" :height="180" status="idle" />
          <h4 style="margin: 15px 0 5px 0; color: #4A90A4;">空闲状态 (Idle)</h4>
          <p style="margin: 0; font-size: 14px; color: #6B7280;">静态显示，等待操作</p>
        </div>
        <div style="text-align: center;">
          <IsometricPunchPress :width="180" :height="180" status="working" />
          <h4 style="margin: 15px 0 5px 0; color: #22C55E;">工作状态 (Working)</h4>
          <p style="margin: 0; font-size: 14px; color: #6B7280;">冲头运动，正在工作</p>
        </div>
        <div style="text-align: center;">
          <IsometricPunchPress :width="180" :height="180" status="error" />
          <h4 style="margin: 15px 0 5px 0; color: #EF4444;">错误状态 (Error)</h4>
          <p style="margin: 0; font-size: 14px; color: #6B7280;">故障警示，需要检修</p>
        </div>
        <div style="text-align: center;">
          <IsometricPunchPress :width="180" :height="180" status="maintenance" />
          <h4 style="margin: 15px 0 5px 0; color: #F97316;">维护状态 (Maintenance)</h4>
          <p style="margin: 0; font-size: 14px; color: #6B7280;">计划维护，暂停使用</p>
        </div>
      </div>
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: '展示冲床组件的四种状态，每种状态都有不同的颜色主题和动画效果。',
      },
    },
  },
};
