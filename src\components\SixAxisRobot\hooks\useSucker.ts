import { ref, onMounted, watch, Ref } from 'vue';
import * as PIXI from 'pixi.js';
import gsap from 'gsap';
import { MaterialInput } from '../index.vue';
import { useMaterial } from './useMaterial';

export const useSucker = (
  materialConfig?: Ref<MaterialInput> | MaterialInput,
) => {
  const suctionContainer = ref<any>();

  // 处理响应式和非响应式的materialConfig
  const materialRef = ref<MaterialInput>(null);

  // 如果传入的是响应式引用，监听其变化
  if (
    materialConfig &&
    typeof materialConfig === 'object' &&
    'value' in materialConfig
  ) {
    materialRef.value = materialConfig.value;
    watch(materialConfig, (newValue) => {
      materialRef.value = newValue;
    });
  } else {
    materialRef.value = materialConfig as MaterialInput;
  }

  // 使用物料hook
  const { materialContainer } = useMaterial(materialRef);

  const drawSucker = () => {
    suctionContainer.value = new PIXI.Container();
    // 绘制吸盘主体
    const suction = new PIXI.Graphics();
    // 设置变形原点为吸盘的中心位置
    suction.pivot.set(0, 0);

    suction.lineStyle(3, 0x292327);
    suction.beginFill(0x292327);

    // 绘制吸盘外壳
    suction.drawRect(-11, -26, 22, 30);
    suction.endFill();

    // 绘制中间圆形连接件
    suction.lineStyle(3, 0x292327);
    suction.beginFill(0x292327);
    suction.drawCircle(0, 5, 11);
    suction.endFill();

    // 添加吸盘内部圆环(用于动画效果)
    const innerCircle = new PIXI.Graphics();

    // 添加内部动画圆环
    innerCircle.lineStyle(2, 0xff5d03);
    innerCircle.drawCircle(0, 5, 6);
    innerCircle.lineStyle(0);
    innerCircle.beginFill(0xff5d03);
    innerCircle.drawCircle(0, 5, 2);
    innerCircle.endFill();

    // 绘制把手主体
    const handle = new PIXI.Graphics();

    // 绘制把手连接杆
    handle.lineStyle(3, 0x292327);
    handle.beginFill(0x292327);
    handle.drawRect(-4, 15, 8, 20);
    handle.endFill();

    // 绘制把手外圈
    handle.lineStyle(3, 0x292327);
    handle.beginFill(0xff5d03);
    handle.drawCircle(0, 45, 9);
    handle.endFill();

    // 绘制把手内圈（凹陷效果）
    handle.lineStyle(2, 0x292327);
    handle.beginFill(0xa83302);
    handle.drawCircle(0, 45, 4);
    handle.endFill();

    // 绘制把手中心点
    handle.lineStyle(0);
    handle.beginFill(0x292327);
    handle.drawCircle(0, 45, 6);
    handle.endFill();

    // 将所有部件添加到容器中
    suctionContainer.value.addChild(handle);
    suctionContainer.value.addChild(suction);

    suctionContainer.value.pivot.set(0, 45);

    // 将内部圆环添加到容器
    suctionContainer.value.addChild(innerCircle);

    suctionContainer.value.innerCircle = innerCircle;
    suctionContainer.value.suction = suction;
  };

  const animateSucker = () => {
    const innerCircle = suctionContainer.value?.innerCircle;
    const suction = suctionContainer.value?.suction;

    const tl = gsap.timeline({
      repeat: -1, // 无限循环
      yoyo: true, // 动画来回播放
    });

    tl.to(
      suction,
      {
        y: 10,
        duration: 0.5,
        ease: 'power2.inOut',
      },
      '+=0.5',
    );
    tl.to(
      innerCircle,
      {
        scaleY: 1.2, // 纵向跟随拉伸
        scaleX: 0.85, // 横向同步收缩
        y: 5,
        duration: 0.5,
        ease: 'power2.inOut',
      },
      '<', // 与吸盘动画同时进行
    );
  };

  // 监听物料容器的变化，动态添加到吸盘容器中
  watch(
    materialContainer,
    (newContainer) => {
      if (suctionContainer.value && newContainer) {
        // 移除之前的物料容器（如果存在）
        const existingMaterial = suctionContainer.value.children.find(
          (child: any) => child._materialContainer === true,
        );
        if (existingMaterial) {
          suctionContainer.value.removeChild(existingMaterial);
        }

        // 添加新的物料容器
        // newContainer._materialContainer = true; // 标记为物料容器
        suctionContainer.value.addChild(newContainer);
      }
    },
    { immediate: true },
  );

  onMounted(() => {
    drawSucker();
  });

  return {
    suctionContainer,
    animateSucker,
  };
};
