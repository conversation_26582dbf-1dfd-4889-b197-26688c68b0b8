<template>
  <div
    class="material-item"
    :class="[materialClassName, { 'has-label': renderOptions?.showLabel }]"
    :style="materialStyle"
  >
    <!-- 物料主体 -->
    <div class="material-body">
      <!-- 物料特殊装饰元素 -->
      <div v-if="materialConfig?.type === 'block'" class="material-decoration wood-grain"></div>
      <div v-else-if="materialConfig?.type === 'cube'" class="material-decoration cube-grid"></div>
      <div v-else-if="materialConfig?.type === 'sphere'" class="material-decoration sphere-highlight"></div>
      <div v-else-if="materialConfig?.type === 'cylinder'" class="material-decoration cylinder-shadow"></div>
      <div v-else-if="materialConfig?.type === 'tray'" class="material-decoration tray-dividers"></div>
    </div>
    
    <!-- 物料标签 -->
    <div
      v-if="renderOptions?.showLabel && materialConfig?.label"
      class="material-label"
      :class="`label-${renderOptions.labelPosition}`"
    >
      {{ materialConfig.label }}
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, computed, toRefs } from 'vue';
import { MaterialInput } from '../types';
import { useMaterial } from '../hooks/useMaterial';

export default defineComponent({
  name: 'MaterialItem',
  props: {
    material: {
      type: [Object, String] as PropType<MaterialInput>,
      required: true,
    },
    beltHeight: {
      type: Number,
      required: true,
    },
    sizeRatio: {
      type: Number,
      default: 0.7,
    },
  },
  setup(props) {
    const { material, beltHeight, sizeRatio } = toRefs(props);
    
    const {
      materialConfig,
      hasMaterial,
      renderOptions,
      materialClassName,
      materialStyle,
    } = useMaterial(material, beltHeight, sizeRatio);

    return {
      materialConfig,
      hasMaterial,
      renderOptions,
      materialClassName,
      materialStyle,
    };
  },
});
</script>

<style scoped>
.material-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.material-body {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 2px;
  background-color: var(--material-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 物料标签 */
.material-label {
  position: absolute;
  font-size: 8px;
  font-weight: bold;
  color: #333;
  background: rgba(255, 255, 255, 0.9);
  padding: 1px 3px;
  border-radius: 2px;
  white-space: nowrap;
  z-index: 10;
}

.label-top {
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
}

.label-bottom {
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
}

.label-center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.8);
}

/* 木块样式 */
.material-block .material-body {
  background: linear-gradient(45deg, var(--material-color) 0%, var(--material-color) 100%);
}

.wood-grain {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 3px,
    rgba(101, 67, 33, 0.3) 3px,
    rgba(101, 67, 33, 0.3) 4px
  );
  border-radius: 2px;
}

/* 魔方样式 */
.material-cube .material-body {
  border-radius: 1px;
}

.cube-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(to right, rgba(0, 0, 0, 0.3) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(0, 0, 0, 0.3) 1px, transparent 1px);
  background-size: 33.33% 33.33%;
}

/* 球体样式 */
.material-sphere .material-body {
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4), var(--material-color));
}

.sphere-highlight {
  position: absolute;
  top: 15%;
  left: 25%;
  width: 25%;
  height: 25%;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  filter: blur(1px);
}

/* 圆柱体样式 */
.material-cylinder .material-body {
  border-radius: 50%;
  background: linear-gradient(135deg, var(--material-color), rgba(0, 0, 0, 0.1));
}

.cylinder-shadow {
  position: absolute;
  top: 10%;
  left: 10%;
  right: 10%;
  bottom: 10%;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 50%;
}

/* 托盘样式 */
.material-tray .material-body {
  border: 1px solid rgba(0, 0, 0, 0.3);
  background: var(--material-color);
}

.tray-dividers {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(to right, transparent 49%, rgba(204, 204, 204, 0.8) 49%, rgba(204, 204, 204, 0.8) 51%, transparent 51%),
    linear-gradient(to bottom, transparent 49%, rgba(204, 204, 204, 0.8) 49%, rgba(204, 204, 204, 0.8) 51%, transparent 51%);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .material-label {
    font-size: 6px;
    padding: 0px 2px;
  }
}
</style>
