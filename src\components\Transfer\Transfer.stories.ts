import type { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import Transfer from './index.vue';

const meta = {
  title: 'Equipment/Transfer',
  component: Transfer,
  tags: ['autodocs'],
  argTypes: {
    width: {
      control: 'number',
      description: '传送带宽度（像素）',
    },
    height: {
      control: 'number',
      description: '传送带高度（像素）',
    },
    rotate: {
      control: 'number',
      description: '旋转角度',
    },
    speed: {
      control: 'number',
      description: '传送带运行速度（秒/周期）',
    },
    direction: {
      control: 'select',
      options: [1, -1],
      description: '运行方向：1为正向，-1为反向',
    },
    running: {
      control: 'boolean',
      description: '是否运行',
    },
    segmentCount: {
      control: 'number',
      description: '传送带节段数量',
    },
    materialImgPath: {
      control: 'text',
      description: '物料图片路径',
    },
    materialWidth: {
      control: 'number',
      description: '物料宽度（像素）',
    },
    materialHeight: {
      control: 'number',
      description: '物料高度（像素）',
    },
    listenMaterialCmd: {
      control: 'text',
      description: '监听添加物料的命令',
    },
    listenMaterialRemoveCmd: {
      control: 'text',
      description: '监听移除物料的命令',
    },
  },
  args: {
    width: 500,
    height: 100,
    rotate: 0,
    speed: 10,
    direction: 1,
    running: false,
    segmentCount: 18,
    materialImgPath: '',
    materialWidth: 0,
    materialHeight: 0,
    listenMaterialCmd: '',
    listenMaterialRemoveCmd: '',
  },
  parameters: {
    docs: {
      description: {
        component:
          'Transfer组件用于显示传送带，支持物料传送动画、方向控制、速度调节等功能。组件使用 PixiJS 渲染，具有高性能的动画效果。',
      },
    },
  },
} satisfies Meta<typeof Transfer>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  name: '基础示例',
  args: {
    width: 500,
    height: 100,
    segmentCount: 18,
    running: true,
  },
};

// 不同方向
export const Directions: Story = {
  name: '不同方向',
  render: (args) => ({
    components: { Transfer },
    setup() {
      const direction = ref(1);
      const running = ref(true);

      return { args, direction, running };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="display: flex; gap: 20px; align-items: center;">
          <div style="width: 500px;">
            <Transfer 
              v-bind="args" 
              :direction="direction"
              :running="running"
              :segmentCount="18"
            />
          </div>
          <div style="flex: 1;">
            <div style="margin-bottom: 10px;">
              <label>运行方向:</label>
              <select v-model="direction" style="margin-left: 10px;">
                <option :value="1">正向</option>
                <option :value="-1">反向</option>
              </select>
            </div>
            <div>
              <label>
                <input type="checkbox" v-model="running">
                运行状态
              </label>
            </div>
          </div>
        </div>
      </div>
    `,
  }),
};

// 速度控制
export const SpeedControl: Story = {
  name: '速度控制',
  render: (args) => ({
    components: { Transfer },
    setup() {
      const speed = ref(10);
      const running = ref(true);

      return { args, speed, running };
    },
    template: `
      <div style="display: flex; flex-direction: column; gap: 20px;">
        <div style="display: flex; gap: 20px; align-items: center;">
          <div style="width: 500px;">
            <Transfer 
              v-bind="args" 
              :speed="speed"
              :running="running"
              :segmentCount="18"
            />
          </div>
          <div style="flex: 1;">
            <div style="margin-bottom: 10px;">
              <label>运行速度: {{ speed.toFixed(1) }}秒/周期</label>
              <input 
                type="range" 
                v-model.number="speed" 
                min="0.5" 
                max="5" 
                step="0.5" 
                style="width: 100%;" 
              />
            </div>
            <div>
              <label>
                <input type="checkbox" v-model="running">
                运行状态
              </label>
            </div>
          </div>
        </div>
      </div>
    `,
  }),
};

// 物料传送示例
export const MaterialTransport: Story = {
  name: '物料传送',
  args: {
    width: 500,
    height: 100,
    segmentCount: 5,
    running: true,
    materialImgPath: '/path/to/material-image.png',
    materialWidth: 50,
    materialHeight: 50,
  },
  parameters: {
    docs: {
      description: {
        story:
          '这个示例展示了如何在传送带上添加和移除物料。需要提供物料图片路径和尺寸。',
      },
    },
  },
};
