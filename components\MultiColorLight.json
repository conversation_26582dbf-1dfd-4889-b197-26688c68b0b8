{"id": 1, "version": "0.0.17", "name": {"zh_CN": "多色灯"}, "component": "MultiColorLight", "icon": "multi-color", "description": "可配置的多色灯组件，支持自定义颜色", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "MultiColorLight", "version": "0.0.17", "script": "http://*************:4874/@dcp/component-library@0.0.17/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "group": "DCP", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["width", "height", "lightList"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}}, "schema": {"properties": [{"label": {"zh_CN": "基础属性"}, "description": {"zh_CN": "多色灯的基础配置属性"}, "content": [{"property": "width", "label": {"text": {"zh_CN": "宽度"}}, "description": {"zh_CN": "多色灯的宽度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 60, "widget": {"component": "NumberConfigurator", "props": {"min": 10, "max": 500, "step": 5}}}, {"property": "height", "label": {"text": {"zh_CN": "高度"}}, "description": {"zh_CN": "多色灯的高度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 200, "widget": {"component": "NumberConfigurator", "props": {"min": 50, "max": 500, "step": 10}}}, {"property": "lightList", "label": {"text": {"zh_CN": "灯光颜色列表"}}, "description": {"zh_CN": "多色灯的颜色列表，按顺序从上到下排列"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "object", "defaultValue": ["#FF0000", "#FFFF00", "#00FF00"], "widget": {"component": "CodeConfigurator", "props": {"language": "json", "height": 150}}}, {"property": "lightBorderStyle", "label": {"text": {"zh_CN": "灯光边框"}}, "description": {"zh_CN": "多色灯的边框"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "CheckBoxConfigurator", "props": {}}}]}], "events": {"onClick": {"label": {"zh_CN": "点击时触发"}, "description": {"zh_CN": "当多色灯被点击时触发的事件"}, "type": "event", "functionInfo": {"params": [], "returns": {}}, "defaultValue": ""}}}, "snippets": [{"name": {"zh_CN": "多色灯"}, "icon": "multi-color", "screenshot": "", "snippetName": "MultiColorLight", "schema": {"props": {"width": 60, "height": 200, "lightList": ["#FF0000", "#FFFF00", "#00FF00"]}}}], "category": "DCP"}