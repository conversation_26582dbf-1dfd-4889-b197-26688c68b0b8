<template>
  <div class="content">
    <div class="content-container">
      <img :src="platePng" alt="" srcset="" />
      <transition
        enter-active-class="animate__animated animate__fadeInUp"
        leave-active-class="animate__animated animate__fadeOutDown"
      >
        <div v-show="waferState" class="wafer">
          <wafer
            :scale="waferScale"
            :color="waferColor"
            :name="waferName"
            :show-ocr="false"
          />
        </div>
      </transition>
    </div>
  </div>
</template>

<script lang="ts">
import { toRefs, defineComponent } from 'vue';
import platePng from './plate.png';
import Wafer from '../Wafer';

export default defineComponent({
  name: 'Plate',
  components: {
    Wafer,
  },
  props: {
    scale: {
      type: Number,
      default: 1,
    },
    waferState: {
      type: Boolean,
      required: true,
    },
    waferColor: {
      type: String,
      required: true,
    },
    waferName: {
      type: String,
      required: false,
    },
    waferScale: {
      type: Number,
      default: 1,
    },
  },
  setup(props) {
    const { scale, waferState, waferColor, waferName, waferScale } =
      toRefs(props);
    return {
      platePng,
      scale,
      waferState,
      waferColor,
      waferName,
      waferScale,
    };
  },
});
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;

  overflow: visible;

  .content-container {
    transition: all 0.5s ease;
    transform-origin: center center;
    transform: v-bind('"scale(" + scale + ")"');
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;

    img {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: auto;
    }

    .wafer {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
</style>
