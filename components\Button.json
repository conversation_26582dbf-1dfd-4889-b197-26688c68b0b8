{"icon": "button", "name": {"zh_CN": "<PERSON><PERSON>"}, "component": "button", "container": false, "screenshot": "", "tags": "", "keywords": "", "devMode": "proCode", "npm": {}, "group": "component", "category": "html", "priority": 70, "schema": {"properties": [{"label": {"zh_CN": "基础信息"}, "description": {"zh_CN": "基础信息"}, "content": [{"property": "attributes3", "label": {"text": {"zh_CN": "原生属性"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "HtmlAttributesConfigurator", "props": {}}, "description": {"zh_CN": "原生属性"}, "labelPosition": "none"}]}], "events": {"onClick": {"label": {"zh_CN": "点击时触发"}, "description": {"zh_CN": "点击时触发"}, "type": "event", "functionInfo": {"params": [], "returns": {}}, "defaultValue": ""}}, "shortcuts": {"properties": []}, "contentMenu": {"actions": []}}, "configure": {"isContainer": true}}