<template>
  <div :style="transformStyle" ref="pixiContainer"></div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  reactive,
  watch,
  watchEffect,
  computed,
  onMounted,
  onUnmounted,
  nextTick,
  PropType,
  toRefs,
} from 'vue';
import * as PIXI from 'pixi.js';
import { gsap } from 'gsap';
import { cloneDeep } from 'lodash-es';
import { SiteConfig, ArmConfig, RobotOption } from './interface';

const initialSiteConfig: SiteConfig = {
  name: '',
  lowerArmLength: 50,
  rotate: 0,
};

const initialArmConfig: ArmConfig = {
  // extended: true,
  waferNumber: null,
  waferColor: '#0088ff',
  currentSite: 'None',
  siteList: [cloneDeep(initialSiteConfig)],
};

export default defineComponent({
  name: 'Robot',
  props: {
    transformX: {
      type: [String, Number],
      default: 0,
    },
    transformY: {
      type: [String, Number],
      default: 0,
    },
    armList: {
      type: Array as PropType<
        Array<{
          waferNumber: string;
          waferColor: string;
          currentSite: string;
          siteList: SiteConfig[];
        }>
      >,
      default: () => [
        Object.assign(cloneDeep(initialArmConfig), {
          name: 'P1',
        }),
        Object.assign(cloneDeep(initialArmConfig), {
          name: 'P2',
        }),
      ],
    },
    scale: {
      type: Number,
      default: 1,
    },
    rotate: {
      type: [String, Number],
      default: 0,
    },
  },
  setup(props) {
    const { transformX, transformY, armList, scale, rotate } = toRefs(props);

    const transformStyle = ref<any>({
      transition: 'all 1s',
      cursor: 'pointer',
    });
    const transitionDuration = ref<number>(0);

    const pixiContainer = ref<HTMLElement | null>(null);
    const app = ref<PIXI.Application>();
    const base = ref<PIXI.Graphics>();

    const arms = reactive(
      new Map<
        number,
        {
          baseJoint: PIXI.Graphics;
          upperArm: PIXI.Graphics;
          elbowJoint: PIXI.Graphics;
          lowerArm: PIXI.Graphics;
          claw: PIXI.Graphics;
          wafer: PIXI.Graphics;
        }
      >(),
    );

    const initPixi = () => {
      app.value = new PIXI.Application({
        width: 600,
        height: 600,
        backgroundAlpha: 0,
        antialias: true,
      });

      if (pixiContainer.value) {
        pixiContainer.value.appendChild((app.value as any)?.view);
      }

      base.value = new PIXI.Graphics();
      drawBase();
    };

    const drawBase = () => {
      base.value?.beginFill(0xcccccc);
      base.value?.drawCircle(0, 0, 50);
      base.value?.endFill();
      base.value?.position.set(300, 300);
    };

    const setupArms = () => {
      app.value?.stage.removeChildren();
      base.value?.removeChildren();
      arms.clear();
      const gapRotate = 360 / armList.value.length;
      for (let i = 0; i < armList.value.length; i++) {
        const rotation = gapRotate * i * (Math.PI / 180);
        const arm = createArm(i);
        arm.rotation = rotation;
        base.value?.addChild(arm);
      }
      app.value?.stage.addChild(base.value!);
    };

    const createArm = (armIndex: number): PIXI.Container => {
      const armInfo = armList.value[armIndex];
      const arm = new PIXI.Container();

      const baseJoint = new PIXI.Graphics();
      baseJoint.beginFill(0x999999);
      baseJoint.drawCircle(0, 0, 20);
      baseJoint.endFill();
      arm.addChild(baseJoint);

      const upperArm = new PIXI.Graphics();
      upperArm.beginFill(0x999999);
      upperArm.moveTo(0, -20);
      upperArm.lineTo(55, -10);
      upperArm.lineTo(55, 10);
      upperArm.lineTo(0, 20);
      upperArm.closePath();
      upperArm.endFill();
      upperArm.pivot.set(0, 0);
      upperArm.position.set(0, 0);
      baseJoint.addChild(upperArm);

      const elbowJoint = new PIXI.Graphics();
      elbowJoint.beginFill(0x999999);
      elbowJoint.drawCircle(0, 0, 10);
      elbowJoint.endFill();
      elbowJoint.position.set(55, 0);
      upperArm.addChild(elbowJoint);

      const lowerArm = new PIXI.Graphics();
      drawLowerArm(lowerArm);
      elbowJoint.addChild(lowerArm);

      const claw = new PIXI.Graphics();
      drawClaw(claw);
      claw.position.set(50, 0);
      lowerArm.addChild(claw);

      const wafer = new PIXI.Graphics();
      wafer.position.set(60, 0);
      lowerArm.addChild(wafer);

      if (armInfo.waferNumber) {
        drawWafer(wafer, armInfo.waferNumber, armInfo.waferColor);
      }

      arms.set(armIndex, {
        baseJoint,
        upperArm,
        elbowJoint,
        lowerArm,
        claw,
        wafer,
      });

      updateArmSite(armIndex, armInfo.siteList, armInfo.currentSite, 'None');
      return arm;
    };

    const drawLowerArm = (lowerArm: PIXI.Graphics, targetLength = 50) => {
      lowerArm.clear();
      lowerArm.beginFill(0x999999);
      lowerArm.moveTo(0, -10);
      lowerArm.lineTo(targetLength, -6);
      lowerArm.lineTo(targetLength, 6);
      lowerArm.lineTo(0, 10);
      lowerArm.closePath();
      lowerArm.endFill();
    };

    const drawClaw = (claw: PIXI.Graphics) => {
      claw.clear();
      claw.beginFill(0x999999);
      claw.moveTo(0, -9);
      claw.lineTo(25, -6);
      claw.lineTo(30, -4);
      claw.lineTo(30, 0);
      claw.lineTo(0, -1);
      claw.closePath();
      claw.moveTo(0, 10);
      claw.lineTo(25, 6);
      claw.lineTo(30, 4);
      claw.lineTo(30, 0);
      claw.lineTo(0, 2);
      claw.closePath();
      claw.endFill();
    };

    const drawWafer = (
      wafer: PIXI.Graphics,
      waferNumber: string,
      waferColor: string,
    ) => {
      wafer.removeChildren();
      wafer.clear();
      if (waferNumber) {
        wafer.beginFill(new PIXI.Color(waferColor).toNumber());
        wafer.drawCircle(0, 0, 25);
        wafer.endFill();
        const text = new PIXI.Text(waferNumber, {
          fontSize: 18,
          fill: 0xffffff,
          align: 'center',
        });
        text.anchor.set(0.5);
        wafer.addChild(text);
      }
    };

    const updateArmSite = (
      armIndex: number,
      siteList: any[],
      armSite: string,
      prevSite: string,
    ) => {
      const armData = arms.get(armIndex);
      if (!armData) return;

      const { upperArm, lowerArm, claw, wafer } = armData;
      const defaultSite = {
        rotate: 0,
        lowerArmLength: 40,
        isDefault: true,
      };
      const siteInfo =
        siteList.find((site: any) => site.name === armSite) || defaultSite;
      const prevSiteInfo =
        siteList.find((site: any) => site.name === prevSite) || defaultSite;
      const rotation = siteInfo.rotate * (Math.PI / 180);

      gsap.to(upperArm, {
        rotation: siteInfo.isDefault ? Math.PI / 4 : rotation,
        duration: transitionDuration.value,
      });

      gsap.to(lowerArm, {
        rotation: siteInfo.isDefault ? -Math.PI + 0.8 : -0.3,
        duration: transitionDuration.value,
      });

      gsap.to(
        { len: prevSiteInfo.lowerArmLength || 0 },
        {
          len: siteInfo.lowerArmLength,
          duration: 1,
          onUpdate: function () {
            drawLowerArm(lowerArm as PIXI.Graphics, this.targets()[0].len);
            claw.position.set(this.targets()[0].len, 0);
            wafer.position.set(this.targets()[0].len + 10, 0);
          },
        },
      );

      transformStyle.value = {
        transition: 'all 1s',
        translate: `${transformX.value}px ${transformY.value}px`,
      };
    };

    const updateArmWafer = (
      armIndex: number,
      waferInfo: {
        no: string;
        color: string;
      },
    ) => {
      if (!arms.size || armIndex === undefined) return;
      const armData = arms.get(armIndex);
      if (!armData) return;

      drawWafer(armData.wafer as PIXI.Graphics, waferInfo.no, waferInfo.color);
    };

    const armsCurrentSite = computed(() => {
      return armList.value?.map((arm) => {
        return arm.currentSite || 'None';
      });
    });

    const armsCurrentWafer = computed(() => {
      return armList.value?.map((arm) => {
        return {
          no: arm.waferNumber,
          color: arm.waferColor,
        };
      });
    });

    watch(
      () => [transformX.value, transformY.value],
      () => {
        if (!app.value) return;
        app.value?.stage.position.set(+transformX.value, +transformY.value);
      },
    );

    watch(
      () => [scale.value, app.value],
      () => {
        if (!app.value) return;
        const height = 600 * scale.value;
        const width = 600 * scale.value;
        app.value?.renderer.resize(width, height);
        app.value?.stage.scale.set(scale.value, scale.value);
      },
    );

    watch(
      () => [rotate.value, base.value],
      () => {
        if (!base.value) return;
        const rotation = Number(rotate.value) * (Math.PI / 180);
        gsap.to(base.value, {
          rotation: rotation,
          duration: 1,
        });
      },
    );

    watch(
      () => armsCurrentSite.value,
      (cur, prev) => {
        for (let i = 0; i < cur.length; i++) {
          if (!prev || cur[i] !== prev[i]) {
            const targetArm = arms.get(i);
            if (targetArm) {
              updateArmSite(
                i,
                armList.value[i].siteList,
                cur[i],
                (prev || ['None'])[i],
              );
            }
          }
        }
      },
    );

    watch(
      () => armsCurrentWafer.value,
      (cur, prev) => {
        for (let i = 0; i < cur.length; i++) {
          if (!prev || cur[i] !== prev[i]) {
            updateArmWafer(i, cur[i]);
          }
        }
      },
    );

    watch(
      () => armList.value.length,
      () => {
        setupArms();
      },
    );

    watchEffect(() => {
      armList.value.forEach((arm, armIndex) => {
        arm.siteList.forEach((site: SiteConfig) => {
          watchEffect(() => {
            if (site.name === arm.currentSite) {
              const targetArm = arms.get(armIndex);
              if (targetArm) {
                updateArmSite(armIndex, arm.siteList, site.name, 'None');
              }
            }
          });
        });
      });
      setTimeout(() => {
        if (transitionDuration.value === 0) {
          transitionDuration.value = 1;
        }
      }, 1500);
    });

    onMounted(() => {
      nextTick(() => {
        initPixi();
        setupArms();
      });
    });

    onUnmounted(() => {
      if (app.value) {
        app.value.destroy(true);
      }
    });

    return {
      transformStyle,
      pixiContainer,
    };
  },
});
</script>

<style lang="scss" scoped>
.edit-content-chart {
  overflow: visible !important;
}
</style>
