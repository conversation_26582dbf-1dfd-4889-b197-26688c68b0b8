<template>
  <div :class="['content', { disabled: !!isDisabled }]" @click="toggle">
    <svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 114 114">
      <g
        id="Group_Heat_Exchanger"
        transform="matrix(-4.649123E-07,-0.9999999,0.9999999,-4.649123E-07,0,112)"
      >
        <circle
          :fill="contentColor"
          stroke="#4C4C4C"
          stroke-width="2"
          cx="55"
          cy="55"
          r="54"
        />
      </g>
      <g
        id="Group_Coil"
        transform="matrix(-4.649123E-07,-0.9999999,0.9999999,-4.649123E-07,0,112)"
      >
        <path
          fill="none"
          stroke="#FFF"
          stroke-width="2"
          d="M17.793,84.347l11.261-5.63l-11.261-5.631l11.261-5.631l-11.261-5.631   l11.261-5.63l-11.261-5.631l11.261-5.63l-11.261-5.631l11.261-5.631l-11.261-5.63"
        />
        <path
          fill="none"
          stroke="#FFF"
          stroke-width="2"
          d="M93.693,84.347l-11.261-5.63l11.261-5.631l-11.261-5.631l11.261-5.631   l-11.261-5.63l11.261-5.631l-11.261-5.63l11.261-5.631l-11.261-5.631l11.261-5.63"
        />
      </g>
    </svg>
  </div>
</template>

<script lang="ts">
import { toRefs, defineComponent } from 'vue';

export default defineComponent({
  name: 'Heater',
  props: {
    color: {
      type: String,
      required: true,
    },
    rotate: {
      type: Number,
      default: 0,
    },
    isDisabled: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const { color: contentColor, rotate, isDisabled } = toRefs(props);

    const toggle = () => {
      console.log('toggle');
    };

    return { contentColor, rotate, isDisabled, toggle };
  },
});
</script>

<style lang="scss" scoped>
.content {
  cursor: pointer;
  position: relative;
  overflow: hidden;
  &.disabled {
    cursor: no-drop;
  }
  svg {
    transform: v-bind('`rotate(${rotate}deg)`');
    width: 100%;
    height: 100%;
  }

  .icon {
    transition: filter 0.3s;
  }
  &:not(.disabled) {
    .icon:hover {
      filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.8));
    }
  }

  &.disabled {
    .icon:hover {
      filter: drop-shadow(0 0 3px rgba(128, 128, 128, 0.3));
    }
  }
}
</style>
