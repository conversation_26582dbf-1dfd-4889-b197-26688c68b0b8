<template>
  <div class="login-info-dropdown" v-click-outside="closeDropdown">
    <div class="login-info" @click="toggleDropdown">
      <div class="avatar-container" v-if="showAvatar">
        <img
          v-if="avatarSrc"
          :src="avatarSrc"
          alt="User avatar"
          class="avatar-image"
        />
        <div v-else class="avatar-placeholder">
          {{ avatarText }}
        </div>
      </div>
      <div class="user-info">
        <div class="username">{{ username }}</div>
        <div class="user-group">{{ userGroup }}</div>
      </div>
      <div class="dropdown-icon" :class="{ 'is-active': isDropdownOpen }">
        <Icon class="icon" :icon="dropdownIcon" />
      </div>
    </div>
    <div class="dropdown-menu" v-show="isDropdownOpen">
      <slot name="menu-items">
        <div
          v-for="(item, index) in menuItems"
          :key="index"
          class="menu-item"
          @click="handleMenuItemClick(item)"
        >
          <Icon v-if="item.icon" class="menu-icon" :icon="item.icon" />
          <span>{{ item.label }}</span>
        </div>
      </slot>
      <div class="default-actions">
        <div
          v-if="showLanguageSwitch"
          class="menu-item"
          @click="handleLanguageSwitch"
        >
          <Icon class="menu-icon" icon="mdi:translate" />
          <span>{{ languageSwitchLabel }}</span>
        </div>
        <div v-if="showLogout" class="menu-item logout" @click="handleLogout">
          <Icon class="menu-icon" icon="mdi:logout" />
          <span>{{ logoutLabel }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, PropType } from 'vue';
import { Icon } from '@iconify/vue';

export interface MenuItem {
  label: string;
  icon?: string;
  action?: () => void;
  [key: string]: any;
}

export default defineComponent({
  name: 'LoginInfo',
  components: {
    Icon,
  },
  props: {
    username: {
      type: String,
      default: 'User',
    },
    userGroup: {
      type: String,
      default: 'Default Group',
    },
    showAvatar: {
      type: Boolean,
      default: true,
    },
    avatarSrc: {
      type: String,
      default: '',
    },
    menuItems: {
      type: Array as PropType<MenuItem[]>,
      default: () => [],
    },
    showLogout: {
      type: Boolean,
      default: true,
    },
    logoutLabel: {
      type: String,
      default: 'Logout',
    },
    showLanguageSwitch: {
      type: Boolean,
      default: true,
    },
    languageSwitchLabel: {
      type: String,
      default: 'Switch Language',
    },
    dropdownIcon: {
      type: String,
      default: 'mdi:chevron-down',
    },
  },
  setup(props, { emit }) {
    const isDropdownOpen = ref(false);

    const avatarText = computed(() => {
      if (!props.username) return '';
      return props.username.charAt(0).toUpperCase();
    });

    const toggleDropdown = () => {
      isDropdownOpen.value = !isDropdownOpen.value;
    };

    const closeDropdown = () => {
      isDropdownOpen.value = false;
    };

    const handleMenuItemClick = (item: MenuItem) => {
      if (item.action && typeof item.action === 'function') {
        item.action();
      }
      emit('menu-item-click', item);
      closeDropdown();
    };

    const handleLogout = () => {
      // localStorage.clear();
      // sessionStorage.clear();
      // window.location.href = '/login';
      emit('logout');
      closeDropdown();
    };

    const handleLanguageSwitch = () => {
      emit('language-switch');
      closeDropdown();
    };

    return {
      isDropdownOpen,
      avatarText,
      toggleDropdown,
      closeDropdown,
      handleMenuItemClick,
      handleLogout,
      handleLanguageSwitch,
    };
  },
  directives: {
    'click-outside': {
      mounted(el, binding) {
        el.clickOutsideEvent = (event: Event) => {
          if (!(el === event.target || el.contains(event.target))) {
            binding.value(event);
          }
        };
        document.addEventListener('click', el.clickOutsideEvent);
      },
      unmounted(el) {
        document.removeEventListener('click', el.clickOutsideEvent);
      },
    },
  },
});
</script>

<style lang="scss" scoped>
.login-info-dropdown {
  position: relative;
  display: inline-block;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.login-info {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #e8e8e8;
  }
}

.avatar-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #1890ff;
  color: white;
  font-size: 18px;
  font-weight: 500;
}

.user-info {
  flex: 1;
  overflow: hidden;
}

.username {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-group {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-icon {
  margin-left: 8px;
  display: flex;
  align-items: center;
  transition: transform 0.2s ease;

  &.is-active {
    transform: rotate(180deg);
  }

  .icon {
    font-size: 18px;
    color: #666;
  }
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  width: 100%;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
  }

  .menu-icon {
    margin-right: 8px;
    font-size: 16px;
    color: #666;
  }

  &.logout {
    border-top: 1px solid #f0f0f0;
    color: #ff4d4f;

    .menu-icon {
      color: #ff4d4f;
    }
  }
}

.default-actions {
  border-top: 1px solid #f0f0f0;
  margin-top: 4px;
}
</style>
