{"icon": "checkbox", "name": {"zh_CN": "复选框"}, "component": "TinyCheckbox", "description": "用于配置不同场景的选项，提供用户可在一组选项中进行多选", "docUrl": "", "screenshot": "", "tags": "", "keywords": "", "devMode": "proCode", "npm": {"package": "@opentiny/vue", "exportName": "Checkbox"}, "group": "component", "priority": 4, "schema": {"properties": [{"label": {"zh_CN": "基础信息"}, "description": {"zh_CN": "基础信息"}, "content": [{"property": "modelValue", "label": {"text": {"zh_CN": "绑定值"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {}}, "description": {"zh_CN": "双向绑定值"}, "labelPosition": "left"}, {"property": "disabled", "label": {"text": {"zh_CN": "禁用"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否禁用"}, "labelPosition": "left"}, {"property": "checked", "label": {"text": {"zh_CN": "勾选"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "当前是否勾选"}, "labelPosition": "left"}, {"property": "text", "label": {"text": {"zh_CN": "文本"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {}}, "description": {"zh_CN": "复选框的文本"}, "labelPosition": "left"}]}, {"name": "1", "label": {"zh_CN": "其他"}, "content": [{"property": "border", "label": {"text": {"zh_CN": "边框"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否显示边框"}, "labelPosition": "left"}, {"property": "false-label", "label": {"text": {"zh_CN": "未选中的值"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {}}, "description": {"zh_CN": "没有选中时的值"}, "labelPosition": "left"}, {"property": "true-label", "label": {"text": {"zh_CN": "选择时的值"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {}}, "description": {"zh_CN": "选中时的值"}, "labelPosition": "left"}], "description": {"zh_CN": ""}}], "events": {"onChange": {"label": {"zh_CN": "勾选值改变后将触发"}, "description": {"zh_CN": "勾选值改变后将触发"}, "type": "event", "functionInfo": {"params": [{"name": "value", "type": "string", "defaultValue": "", "description": {"zh_CN": "选中项的值"}}], "returns": {}}, "defaultValue": ""}, "onUpdate:modelValue": {"label": {"zh_CN": "双向绑定的值改变时触发"}, "description": {"zh_CN": "当前选中的值改变时触发"}, "type": "event", "functionInfo": {"params": [{"name": "value", "type": "string", "defaultValue": "", "description": {"zh_CN": "双向绑定的当前选中值"}}], "returns": {}}, "defaultValue": ""}}}, "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["border", "disabled"]}, "contextMenu": {"actions": ["create symbol"], "disable": ["copy", "remove"]}}, "snippets": [{"name": {"zh_CN": "复选框"}, "icon": "checkbox", "screenshot": "", "snippetName": "TinyCheckbox", "schema": {"componentName": "TinyCheckbox", "props": {"text": "复选框文案"}}, "category": "form"}]}