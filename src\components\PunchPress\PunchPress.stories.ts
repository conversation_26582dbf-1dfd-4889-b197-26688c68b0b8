import type { Meta, StoryObj } from '@storybook/vue3';
import PunchPress from './index.vue';

const meta: Meta<typeof PunchPress> = {
  title: 'Equipment/PunchPress',
  component: PunchPress,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    width: {
      control: { type: 'range', min: 50, max: 500, step: 10 },
      description: 'SVG宽度',
    },
    height: {
      control: { type: 'range', min: 50, max: 500, step: 10 },
      description: 'SVG高度',
    },
    color: {
      control: { type: 'color' },
      description: '基础颜色，将生成对应的色系',
    },
    state: {
      control: { type: 'select' },
      options: ['collapse', 'expand'],
      description: '冲头状态：收起或展开',
    },
    animate: {
      control: 'boolean',
      description: '是否启用动画过渡',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 默认状态 - 收起
export const Default: Story = {
  args: {
    width: 200,
    height: 240,
    color: '#4a90e2',
    state: 'collapse',
    animate: true,
  },
};

// 展开状态
export const Expanded: Story = {
  args: {
    width: 200,
    height: 240,
    color: '#4a90e2',
    state: 'expand',
    animate: true,
  },
};

// 无动画的收起状态
export const CollapseNoAnimation: Story = {
  args: {
    width: 200,
    height: 240,
    color: '#4a90e2',
    state: 'collapse',
    animate: false,
  },
};

// 无动画的展开状态
export const ExpandNoAnimation: Story = {
  args: {
    width: 200,
    height: 240,
    color: '#33f0ab',
    state: 'expand',
    animate: false,
  },
};
