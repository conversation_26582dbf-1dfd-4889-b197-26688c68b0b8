import IsometricPunchPress from './index.vue';

// 冲床状态接口
export interface PunchPressState {
  status: 'idle' | 'working' | 'error' | 'maintenance';
  speed: number;
  strokeLength: number;
  currentPosition: number;
  workpiecePresent: boolean;
  autoMode: boolean;
  totalStrokes: number;
  errorCode?: string;
}

// 导出组件
export { IsometricPunchPress };
export default IsometricPunchPress;

export type IsometricPunchPressStatus =
  | 'idle'
  | 'working'
  | 'error'
  | 'maintenance';

export interface IsometricPunchPressProps {
  width?: number;
  height?: number;
  status?: IsometricPunchPressStatus;
}
