import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import { LoginInfo } from './index';
import type { MenuItem } from './index';

// Define a render function for all stories
const defaultRender = (args: any) => ({
  components: { LoginInfo },
  setup() {
    const onMenuItemClick = (item: MenuItem) =>
      console.log('menu-item-click', item);
    const onLogout = () => console.log('logout');
    const onLanguageSwitch = () => console.log('language-switch');

    return {
      args,
      onMenuItemClick,
      onLogout,
      onLanguageSwitch,
    };
  },
  template: `
    <LoginInfo 
      v-bind="args" 
      @menu-item-click="onMenuItemClick" 
      @logout="onLogout" 
      @language-switch="onLanguageSwitch"
    />
  `,
});

const meta = {
  title: 'Components/LoginInfo',
  component: LoginInfo,
  tags: ['autodocs'],
  argTypes: {
    username: { control: 'text' },
    userGroup: { control: 'text' },
    showAvatar: { control: 'boolean' },
    avatarSrc: { control: 'text' },
    menuItems: { control: 'object' },
    showLogout: { control: 'boolean' },
    logoutLabel: { control: 'text' },
    showLanguageSwitch: { control: 'boolean' },
    languageSwitchLabel: { control: 'text' },
    dropdownIcon: { control: 'text' },
  },
} satisfies Meta<typeof LoginInfo>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    username: 'Admin',
    userGroup: 'Equipment Dept',
    showAvatar: true,
    avatarSrc: '',
    menuItems: [],
    showLogout: true,
    logoutLabel: 'Logout',
    showLanguageSwitch: true,
    languageSwitchLabel: 'Switch Language',
    dropdownIcon: 'mdi:chevron-down',
  },
  render: defaultRender,
};

export const WithAvatar: Story = {
  args: {
    ...Default.args,
    avatarSrc: 'https://randomuser.me/api/portraits/men/1.jpg',
  },
  render: defaultRender,
};

export const WithCustomMenuItems: Story = {
  args: {
    ...Default.args,
    menuItems: [
      { label: 'Profile', icon: 'mdi:account' },
      { label: 'Settings', icon: 'mdi:cog' },
      { label: 'Help', icon: 'mdi:help-circle' },
    ],
  },
  render: defaultRender,
};

export const WithoutAvatar: Story = {
  args: {
    ...Default.args,
    showAvatar: false,
  },
  render: defaultRender,
};

export const ChineseLanguage: Story = {
  args: {
    ...Default.args,
    username: '管理员',
    userGroup: '设备部门',
    logoutLabel: '退出登录',
    languageSwitchLabel: '切换语言',
  },
  render: defaultRender,
};
