import { computed, Ref } from 'vue';
import {
  MaterialConfig,
  MaterialInput,
  MaterialSize,
  MaterialRenderOptions,
  BeltItem,
  BeltItemInput,
  BeltItemsInput,
} from '../types';
import {
  DEFAULT_MATERIAL_SIZES,
  DEFAULT_MATERIAL_COLORS,
} from '../../SixAxisRobot/config';

/**
 * 标准化物料配置：将字符串转换为完整的MaterialConfig对象
 */
export function normalizeMaterialConfig(
  input: MaterialInput,
): MaterialConfig | null {
  if (!input) return null;

  if (typeof input === 'string') {
    // 字符串转换为标准对象，使用默认配置
    const materialType = input as MaterialConfig['type'];
    return {
      type: materialType,
      color: DEFAULT_MATERIAL_COLORS[materialType],
      size: DEFAULT_MATERIAL_SIZES[materialType],
    };
  }

  // 已经是对象，填充默认值
  const config = input as MaterialConfig;
  return {
    type: config.type,
    color: config.color || DEFAULT_MATERIAL_COLORS[config.type],
    size: config.size || DEFAULT_MATERIAL_SIZES[config.type],
    label: config.label,
  };
}

/**
 * 标准化单个 BeltItem 输入
 */
export function normalizeBeltItemInput(
  input: BeltItemInput,
  index: number,
  totalItems: number,
): BeltItem {
  // 如果已经是完整的 BeltItem 对象
  if (
    typeof input === 'object' &&
    input !== null &&
    'id' in input &&
    'position' in input &&
    'content' in input
  ) {
    return input as BeltItem;
  }

  // 计算默认位置（均匀分布）
  const defaultPosition = totalItems > 1 ? (index * 100) / (totalItems - 1) : 0;

  // 如果是字符串，作为物料类型处理
  if (typeof input === 'string') {
    return {
      id: `item-${index}`,
      position: defaultPosition,
      content: input.charAt(0).toUpperCase() + input.slice(1), // 首字母大写
      material: input,
    };
  }

  // 如果是物料配置对象
  const materialConfig = normalizeMaterialConfig(input as MaterialInput);
  if (materialConfig) {
    return {
      id: `item-${index}`,
      position: defaultPosition,
      content:
        materialConfig.label ||
        materialConfig.type.charAt(0).toUpperCase() +
          materialConfig.type.slice(1),
      material: input as MaterialInput,
    };
  }

  // 默认情况
  return {
    id: `item-${index}`,
    position: defaultPosition,
    content: `Item ${index + 1}`,
  };
}

/**
 * 标准化 items 输入数组
 */
export function normalizeBeltItemsInput(input: BeltItemsInput): BeltItem[] {
  return input.map((item, index) =>
    normalizeBeltItemInput(item, index, input.length),
  );
}

/**
 * 计算适配传送带的物料尺寸
 */
export function calculateMaterialSize(
  materialConfig: MaterialConfig,
  beltHeight: number,
  sizeRatio: number = 0.7,
): MaterialSize {
  const originalSize =
    materialConfig.size || DEFAULT_MATERIAL_SIZES[materialConfig.type];

  // 根据传送带高度和比例计算实际尺寸
  const maxSize = beltHeight * sizeRatio;
  const width = originalSize.width || 20;
  const height = originalSize.height || 20;
  const scale = Math.min(maxSize / Math.max(width, height), 1);

  return {
    width: Math.round(width * scale),
    height: Math.round(height * scale),
  };
}

/**
 * 获取物料的CSS类名
 */
export function getMaterialClassName(
  materialType: MaterialConfig['type'],
): string {
  return `material-${materialType}`;
}

/**
 * 获取物料的渲染选项
 */
export function getMaterialRenderOptions(
  materialConfig: MaterialConfig,
  beltHeight: number,
  sizeRatio: number = 0.7,
): MaterialRenderOptions {
  const size = calculateMaterialSize(materialConfig, beltHeight, sizeRatio);
  const color =
    materialConfig.color || DEFAULT_MATERIAL_COLORS[materialConfig.type];

  return {
    size,
    color,
    showLabel: !!materialConfig.label,
    labelPosition: 'bottom',
  };
}

/**
 * 传送带物料管理 Hook
 */
export function useMaterial(
  materialInput: Ref<MaterialInput>,
  beltHeight: Ref<number>,
  sizeRatio: Ref<number> = computed(() => 0.7),
) {
  // 标准化的物料配置
  const materialConfig = computed(() => {
    return normalizeMaterialConfig(materialInput.value);
  });

  // 是否有物料
  const hasMaterial = computed(() => {
    return !!materialConfig.value;
  });

  // 物料渲染选项
  const renderOptions = computed(() => {
    if (!materialConfig.value) return null;
    return getMaterialRenderOptions(
      materialConfig.value,
      beltHeight.value,
      sizeRatio.value,
    );
  });

  // 物料CSS类名
  const materialClassName = computed(() => {
    if (!materialConfig.value) return '';
    return getMaterialClassName(materialConfig.value.type);
  });

  // 物料样式对象
  const materialStyle = computed(() => {
    if (!renderOptions.value) return {};

    const { size, color } = renderOptions.value;
    return {
      width: `${size.width}px`,
      height: `${size.height}px`,
      '--material-color': color,
      '--material-width': `${size.width}px`,
      '--material-height': `${size.height}px`,
    };
  });

  return {
    materialConfig,
    hasMaterial,
    renderOptions,
    materialClassName,
    materialStyle,
  };
}
