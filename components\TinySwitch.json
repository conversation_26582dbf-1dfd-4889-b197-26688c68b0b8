{"icon": "switch", "name": {"zh_CN": "开关"}, "component": "TinySwitch", "description": "Switch 在两种状态间切换选择", "docUrl": "", "screenshot": "", "tags": "", "keywords": "", "devMode": "proCode", "npm": {"package": "@opentiny/vue", "exportName": "Switch"}, "group": "component", "priority": 9, "schema": {"properties": [{"label": {"zh_CN": "基础信息"}, "description": {"zh_CN": "基础信息"}, "content": [{"property": "disabled", "label": {"text": {"zh_CN": "禁用"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否被禁用"}, "labelPosition": "left"}, {"property": "modelValue", "label": {"text": {"zh_CN": "绑定值"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {}}, "description": {"zh_CN": "绑定默认值"}, "labelPosition": "left"}, {"property": "true-value", "label": {"text": {"zh_CN": "设置打开值"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {}}, "description": {"zh_CN": "设置打开时的值(Boolean / String / Number)"}, "labelPosition": "left"}, {"property": "false-value", "label": {"text": {"zh_CN": "设置关闭值"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {}}, "description": {"zh_CN": "设置关闭时的值(Boolean / String / Number)"}, "labelPosition": "left"}, {"property": "mini", "label": {"text": {"zh_CN": "迷你尺寸"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否显示为 mini 模式"}, "labelPosition": "left"}]}], "events": {"onChange": {"label": {"zh_CN": "点击事件"}, "description": {"zh_CN": "按钮被点击时触发的回调函数"}, "type": "event", "functionInfo": {"params": [], "returns": {}}, "defaultValue": ""}, "onUpdate:modelValue": {"label": {"zh_CN": "双向绑定的值改变时触发"}, "description": {"zh_CN": "开关的状态值改变时触发"}, "type": "event", "functionInfo": {"params": [{"name": "value", "type": "string", "defaultValue": "", "description": {"zh_CN": "双向绑定的开关状态值"}}], "returns": {}}, "defaultValue": ""}}}, "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["disabled", "mini"]}, "contextMenu": {"actions": ["create symbol"], "disable": ["copy", "remove"]}}, "snippets": [{"name": {"zh_CN": "开关"}, "icon": "switch", "screenshot": "", "snippetName": "TinySwitch", "schema": {"componentName": "TinySwitch", "props": {"modelValue": ""}}, "category": "form"}]}