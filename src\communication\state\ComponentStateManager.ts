/**
 * 组件状态管理器
 * 负责管理组件的响应式状态
 */
import { reactive, readonly, UnwrapRef } from 'vue';

/**
 * 组件状态管理器
 * 为每个组件实例维护一个响应式状态对象
 */
export class ComponentStateManager {
  private static instance: ComponentStateManager;

  // 存储所有组件的状态
  // Map<componentId, ReactiveState>
  private componentStates = new Map<string, any>();

  /**
   * 私有构造函数，确保单例模式
   */
  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): ComponentStateManager {
    if (!ComponentStateManager.instance) {
      ComponentStateManager.instance = new ComponentStateManager();
    }
    return ComponentStateManager.instance;
  }

  /**
   * 初始化组件状态
   * @param componentId 组件ID
   * @param initialState 初始状态
   * @returns 只读的响应式状态
   */
  public initComponentState<T extends object>(
    componentId: string,
    initialState: T,
  ): { state: UnwrapRef<T>; updateState: (newState: Partial<T>) => void } {
    // 如果已存在，返回现有状态
    if (this.componentStates.has(componentId)) {
      const existingState = this.componentStates.get(componentId);
      return {
        state: readonly(existingState.state),
        updateState: existingState.updateState,
      };
    }

    // 创建响应式状态
    const state = reactive({ ...initialState });

    // 创建更新函数
    const updateState = (newState: Partial<T>) => {
      Object.assign(state, newState);
    };

    // 存储状态和更新函数
    this.componentStates.set(componentId, { state, updateState });

    // 返回只读状态和更新函数
    return {
      state: readonly(state) as any,
      updateState,
    };
  }

  /**
   * 更新组件状态
   * @param componentId 组件ID
   * @param newState 新状态
   */
  public updateComponentState<T extends object>(
    componentId: string,
    newState: Partial<T>,
  ): void {
    const component = this.componentStates.get(componentId);
    if (component) {
      component.updateState(newState);
    }
  }

  /**
   * 清理组件状态
   * @param componentId 组件ID
   */
  public cleanupComponentState(componentId: string): void {
    this.componentStates.delete(componentId);
  }
}
