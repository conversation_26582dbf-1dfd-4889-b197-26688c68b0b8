{"id": 1, "version": "0.0.17", "name": {"zh_CN": "腔体"}, "component": "Chamber", "icon": "box", "description": "可配置的半导体设备腔体组件", "docUrl": "", "screenshot": "", "tags": "", "keywords": "", "devMode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "Chamber", "version": "0.0.17", "script": "http://*************:4874/@dcp/component-library@0.0.17/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "group": "DCP", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["equipmentWidth", "equipmentHeight", "waferState"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}}, "schema": {"properties": [{"label": {"zh_CN": "基础属性"}, "description": {"zh_CN": "腔体的基础配置属性"}, "content": [{"property": "equipmentWidth", "label": {"text": {"zh_CN": "宽度"}}, "description": {"zh_CN": "腔体组件的宽度（像素）"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 200, "widget": {"component": "NumberConfigurator", "props": {"min": 50, "max": 500, "step": 10}}}, {"property": "equipmentHeight", "label": {"text": {"zh_CN": "高度"}}, "description": {"zh_CN": "腔体组件的高度（像素）"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 200, "widget": {"component": "NumberConfigurator", "props": {"min": 50, "max": 500, "step": 10}}}, {"property": "name", "label": {"text": {"zh_CN": "名称"}}, "description": {"zh_CN": "腔体的显示名称"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "backgroundColor", "label": {"text": {"zh_CN": "背景颜色"}}, "description": {"zh_CN": "腔体的背景颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#bcbcbc", "widget": {"component": "ColorPickerConfigurator", "props": {}}}]}, {"label": {"zh_CN": "角落配置"}, "description": {"zh_CN": "腔体四个角落的形状配置"}, "content": [{"property": "L<PERSON><PERSON>ner", "label": {"text": {"zh_CN": "左上角形状"}}, "description": {"zh_CN": "左上角的形状类型"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "rect", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "直角", "value": "rect"}, {"label": "圆角", "value": "round"}]}}}, {"property": "<PERSON><PERSON><PERSON><PERSON>", "label": {"text": {"zh_CN": "右上角形状"}}, "description": {"zh_CN": "右上角的形状类型"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "rect", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "直角", "value": "rect"}, {"label": "圆角", "value": "round"}]}}}, {"property": "L<PERSON><PERSON>ner", "label": {"text": {"zh_CN": "左下角形状"}}, "description": {"zh_CN": "左下角的形状类型"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "rect", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "直角", "value": "rect"}, {"label": "圆角", "value": "round"}]}}}, {"property": "RB<PERSON><PERSON><PERSON>", "label": {"text": {"zh_CN": "右下角形状"}}, "description": {"zh_CN": "右下角的形状类型"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "rect", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "直角", "value": "rect"}, {"label": "圆角", "value": "round"}]}}}, {"property": "connerColor", "label": {"text": {"zh_CN": "角落颜色"}}, "description": {"zh_CN": "角落边框的颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#00c1e4", "widget": {"component": "MetaColor", "props": {}}}]}, {"label": {"zh_CN": "晶圆配置"}, "description": {"zh_CN": "腔体内晶圆的显示配置"}, "content": [{"property": "waferState", "label": {"text": {"zh_CN": "显示晶圆"}}, "description": {"zh_CN": "是否在腔体中显示晶圆"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator", "props": {}}}, {"property": "waferType", "label": {"text": {"zh_CN": "晶圆形状"}}, "description": {"zh_CN": "晶圆的形状类型"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "circle", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "圆形", "value": "circle"}, {"label": "方形", "value": "square"}]}}}, {"property": "waferName", "label": {"text": {"zh_CN": "晶圆名称"}}, "description": {"zh_CN": "晶圆的显示名称"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "waferSize", "label": {"text": {"zh_CN": "晶圆尺寸"}}, "description": {"zh_CN": "晶圆的显示尺寸（像素）"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 60, "widget": {"component": "NumberConfigurator", "props": {"min": 20, "max": 200, "step": 5}}}, {"property": "waferColor", "label": {"text": {"zh_CN": "晶圆颜色"}}, "description": {"zh_CN": "晶圆的显示颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#000", "widget": {"component": "ColorPickerConfigurator", "props": {}}}, {"property": "waferFontSize", "label": {"text": {"zh_CN": "晶圆字体大小"}}, "description": {"zh_CN": "晶圆名称的字体大小"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 14, "widget": {"component": "NumberConfigurator", "props": {"min": 8, "max": 30, "step": 1}}}]}, {"label": {"zh_CN": "交互配置"}, "description": {"zh_CN": "腔体的交互相关配置"}, "content": [{"property": "isDisabled", "label": {"text": {"zh_CN": "禁用状态"}}, "description": {"zh_CN": "是否禁用腔体的交互"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator", "props": {}}}, {"property": "link", "label": {"text": {"zh_CN": "链接"}}, "description": {"zh_CN": "点击腔体时的跳转链接"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "forSelect", "label": {"text": {"zh_CN": "可选择"}}, "description": {"zh_CN": "腔体是否可以被选择"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator", "props": {}}}]}], "events": {}, "slots": {}}, "snippets": [{"name": {"zh_CN": "腔体"}, "icon": "box", "screenshot": "", "snippetName": "Chamber", "schema": {"props": {"equipmentWidth": 200, "equipmentHeight": 200, "name": "腔体", "backgroundColor": "#bcbcbc", "LTConner": "rect", "RTConner": "rect", "LBConner": "rect", "RBConner": "rect", "connerColor": "#00c1e4"}}}], "category": "DCP"}