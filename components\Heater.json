{"id": 1, "version": "0.0.17", "name": {"zh_CN": "加热器"}, "component": "Heater", "icon": "heater", "description": "可配置的加热器组件", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "Heater", "version": "0.0.17", "script": "http://*************:4874/@dcp/component-library@0.0.17/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "group": "DCP", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["color", "rotate", "isDisabled"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}}, "schema": {"properties": [{"label": {"zh_CN": "基础属性"}, "description": {"zh_CN": "加热器的基础配置属性"}, "content": [{"property": "color", "label": {"text": {"zh_CN": "颜色"}}, "description": {"zh_CN": "加热器的颜色"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#FF5D03", "widget": {"component": "ColorConfigurator", "props": {}}}, {"property": "rotate", "label": {"text": {"zh_CN": "旋转角度"}}, "description": {"zh_CN": "加热器的旋转角度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 0, "widget": {"component": "NumberConfigurator", "props": {"min": 0, "max": 360, "step": 15}}}, {"property": "isDisabled", "label": {"text": {"zh_CN": "禁用状态"}}, "description": {"zh_CN": "是否禁用加热器"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator", "props": {}}}]}], "events": {"onClick": {"label": {"zh_CN": "点击时触发"}, "description": {"zh_CN": "当加热器被点击时触发的事件"}, "type": "event", "functionInfo": {"params": [], "returns": {}}, "defaultValue": ""}}}, "snippets": [{"name": {"zh_CN": "加热器"}, "icon": "heater", "screenshot": "", "snippetName": "Heater", "schema": {"props": {"color": "#FF5D03", "rotate": 0, "isDisabled": false}}}], "category": "DCP"}