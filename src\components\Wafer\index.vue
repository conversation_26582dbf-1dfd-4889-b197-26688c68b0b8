<template>
  <div class="wafer-warp">
    <div :style="waferStyle">
      <div>{{ name }}</div>
      <div v-if="showOcr">{{ ocrIo }}</div>
    </div>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent } from 'vue';
import { StyleValue } from 'vue';

export default defineComponent({
  name: 'Wafer',
  props: {
    size: {
      type: Number,
      default: 60,
    },
    color: {
      type: String,
      default: '#000',
    },
    rotate: {
      type: Number,
      default: 0,
    },
    name: {
      type: String,
      default: '',
    },
    /**
     * @deprecated 此属性已废弃，将在未来版本中移除
     */
    scale: {
      type: Number,
      default: 1,
    },
    waferShape: {
      type: String,
      default: 'circle',
    },
    showOcr: {
      type: Boolean,
      default: false,
    },
    ocrIo: {
      type: String,
      default: '',
    },
    fontSize: {
      type: Number,
      default: 14,
    },
  },
  setup(props) {
    const waferStyle = computed(() => {
      const pxSize = props.size ? `${props.size}px` : '60px';
      return {
        width: pxSize,
        height: pxSize,
        display: 'flex',
        justifyContent: 'center',
        flexDirection: 'column',
        fontSize: props.fontSize + 'px',
        color: '#eee',
        alignItems: 'center',
        backgroundColor: props.color,
        borderRadius: props.waferShape === 'circle' ? '50%' : 0,
      } as StyleValue;
    });

    return {
      waferStyle,
    };
  },
});
</script>

<style lang="scss" scoped>
.wafer-warp {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: v-bind('"scale(" + scale + ") rotate(" + -rotate + "deg)"');
  font-size: 0;
  position: relative;

  span {
    font-size: 16px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
  }
}
</style>
