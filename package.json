{"name": "@dcp/component-library", "version": "0.0.95", "files": ["dist", "js"], "types": "js/types/index.d.ts", "main": "js/web-component.mjs", "style": "js/component-library.css", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "publishConfig": {"registry": "http://*************:4873/"}, "scripts": {"build": "vite build", "storybook": "storybook dev -p 8088", "build-storybook": "storybook build"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.4", "@storybook/addon-essentials": "^8.5.8", "@storybook/addon-interactions": "^8.5.8", "@storybook/addon-onboarding": "^8.5.8", "@storybook/blocks": "^8.5.8", "@storybook/manager-api": "^8.5.8", "@storybook/test": "^8.5.8", "@storybook/theming": "^8.5.8", "@storybook/vue3": "^8.5.8", "@storybook/vue3-vite": "^8.5.8", "@types/color": "^4.2.0", "@types/lodash-es": "^4.17.12", "@types/node": "^22.10.7", "@vitejs/plugin-vue": "^5.2.1", "sass": "^1.83.4", "storybook": "^8.5.8", "typescript": "^5.7.3", "vite": "^6.0.7", "vite-plugin-css-injected-by-js": "^3.5.2", "vite-plugin-dts": "^4.5.0", "vue": "^3.5.13"}, "dependencies": {"@iconify/vue": "^4.3.0", "color": "^5.0.0", "element-plus": "^2.10.1", "gsap": "^3.12.7", "konva": "^9.3.20", "lodash-es": "^4.17.21", "mqtt": "^5.13.0", "pixi.js": "^7.2.4", "uuid": "^11.1.0", "vue-konva": "^3.2.1"}}