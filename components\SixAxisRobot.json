{"id": 1, "version": "0.0.17", "name": {"zh_CN": "机械臂 B"}, "component": "SixAxisRobot", "icon": "robot", "description": "可配置的机械臂组件", "docUrl": "", "screenshot": "", "tags": "机械臂,工业,自动化", "keywords": "robot,axis,industrial", "devMode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "SixAxisRobot", "version": "0.0.17", "script": "http://*************:4874/@dcp/component-library@0.0.17/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "group": "DCP组件库", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["width", "height", "currentPosition"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}}, "schema": {"properties": [{"label": {"zh_CN": "基础属性"}, "description": {"zh_CN": "机械臂的基础配置属性"}, "content": [{"property": "width", "label": {"text": {"zh_CN": "宽度"}}, "description": {"zh_CN": "机械臂组件的宽度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 500, "widget": {"component": "NumberConfigurator", "props": {"min": 100, "max": 2000, "step": 10}}}, {"property": "height", "label": {"text": {"zh_CN": "高度"}}, "description": {"zh_CN": "机械臂组件的高度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 500, "widget": {"component": "NumberConfigurator", "props": {"min": 100, "max": 2000, "step": 10}}}, {"property": "scale", "label": {"text": {"zh_CN": "缩放比例"}}, "description": {"zh_CN": "机械臂的整体缩放比例"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 1, "widget": {"component": "NumberConfigurator", "props": {"min": 0.1, "max": 5, "step": 0.1}}}, {"property": "left", "label": {"text": {"zh_CN": "左偏移"}}, "description": {"zh_CN": "机械臂的左侧偏移量"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 0, "widget": {"component": "NumberConfigurator", "props": {"step": 10}}}, {"property": "top", "label": {"text": {"zh_CN": "顶部偏移"}}, "description": {"zh_CN": "机械臂的顶部偏移量"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 0, "widget": {"component": "NumberConfigurator", "props": {"step": 10}}}]}, {"label": {"zh_CN": "位置配置"}, "description": {"zh_CN": "机械臂的动画和位置配置"}, "content": [{"property": "animateDuration", "label": {"text": {"zh_CN": "动画持续时间"}}, "description": {"zh_CN": "机械臂运动动画的持续时间(毫秒)"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 1, "widget": {"component": "NumberConfigurator", "props": {"min": 0, "max": 50, "step": 0.1}}}, {"property": "currentPosition", "label": {"text": {"zh_CN": "当前位置"}}, "description": {"zh_CN": "机械臂当前所处的位置名称"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "default", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "positionList", "label": {"text": {"zh_CN": "位置配置列表"}}, "description": {"zh_CN": "机械臂可用位置的配置列表"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "object", "defaultValue": {"default": {"firstArmAngle": -45, "secondArmAngle": 90, "thirdArmAngle": 90, "gripperAngle": 45, "firstArmLength": 120, "secondArmLength": 120, "thirdArmLength": 100, "gripperWidth": 25, "gripperHeight": 60, "gripperAnimate": 0}}, "widget": {"component": "CodeConfigurator", "props": {"language": "json", "height": 300}}}, {"property": "material", "label": {"text": {"zh_CN": "物料"}}, "description": "当前抓手上的物料", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "block", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "无物料", "value": null}, {"label": "木块", "value": "block"}, {"label": "魔方", "value": "cube"}, {"label": "球体", "value": "sphere"}, {"label": "圆柱体", "value": "cylinder"}, {"label": "托盘", "value": "tray"}]}}}]}], "events": {"onPositionChange": {"label": {"zh_CN": "位置变化时触发"}, "description": {"zh_CN": "当机械臂位置发生变化时触发的事件"}, "type": "event", "functionInfo": {"params": [{"name": "position", "type": "String", "defaultValue": "", "description": {"zh_CN": "当前位置名称"}}, {"name": "config", "type": "Object", "defaultValue": "", "description": {"zh_CN": "当前位置的配置信息"}}], "returns": {}}, "defaultValue": ""}, "onAnimationComplete": {"label": {"zh_CN": "动画完成时触发"}, "description": {"zh_CN": "当机械臂动画完成时触发的事件"}, "type": "event", "functionInfo": {"params": [{"name": "position", "type": "String", "defaultValue": "", "description": {"zh_CN": "当前位置名称"}}], "returns": {}}, "defaultValue": ""}}}, "snippets": [{"name": {"zh_CN": "机械臂 B"}, "icon": "robot", "screenshot": "", "snippetName": "SixAxisRobot", "schema": {"props": {"width": 500, "height": 500, "scale": 1, "animateDuration": 1.5, "currentPosition": "position1", "positionList": {"position1": {"firstArmAngle": 45, "secondArmAngle": 30, "thirdArmAngle": 15, "gripperAngle": 0, "firstArmLength": 100, "secondArmLength": 80, "thirdArmLength": 60, "gripperWidth": 20, "gripperHeight": 30}, "position2": {"firstArmAngle": 20, "secondArmAngle": 30, "thirdArmAngle": 15, "gripperAngle": 25, "firstArmLength": 150, "secondArmLength": 140, "thirdArmLength": 115, "gripperWidth": 25, "gripperHeight": 60, "gripperAnimate": 1, "endPosition": "position1"}, "position3": {"firstArmAngle": 60, "secondArmAngle": 40, "thirdArmAngle": -10, "gripperAngle": 0, "firstArmLength": 120, "secondArmLength": 110, "thirdArmLength": 80, "gripperWidth": 25, "gripperHeight": 60, "gripperAnimate": 1}}, "material": "tray"}}}], "category": "DCP"}