{"id": 1, "version": "0.0.17", "name": {"zh_CN": "对中器"}, "component": "<PERSON><PERSON><PERSON>", "icon": "aligner", "description": "晶圆对中器组件", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "<PERSON><PERSON><PERSON>", "version": "0.0.17", "script": "http://*************:4874/@dcp/component-library@0.0.17/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "group": "DCP", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["rotate", "scale", "waferState"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}}, "schema": {"properties": [{"label": {"zh_CN": "基础属性"}, "description": {"zh_CN": "对中器的基础配置属性"}, "content": [{"property": "rotate", "label": {"text": {"zh_CN": "旋转角度"}}, "description": {"zh_CN": "对中器的旋转角度"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 0, "widget": {"component": "NumberConfigurator", "props": {"min": 0, "max": 360, "step": 1}}}, {"property": "scale", "label": {"text": {"zh_CN": "缩放比例"}}, "description": {"zh_CN": "对中器的整体缩放比例"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 1, "widget": {"component": "NumberConfigurator", "props": {"min": 0.1, "max": 5, "step": 0.1}}}, {"property": "alignerPic", "label": {"text": {"zh_CN": "对中器图片"}}, "description": {"zh_CN": "自定义对中器图片URL，不填则使用默认图片"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "", "widget": {"component": "InputConfigurator", "props": {}}}]}, {"label": {"zh_CN": "晶圆属性"}, "description": {"zh_CN": "晶圆相关配置"}, "content": [{"property": "waferState", "label": {"text": {"zh_CN": "显示晶圆"}}, "description": {"zh_CN": "是否显示晶圆"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator", "props": {}}}, {"property": "waferColor", "label": {"text": {"zh_CN": "晶圆颜色"}}, "description": {"zh_CN": "晶圆的颜色"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#000", "widget": {"component": "ColorConfigurator", "props": {}}}, {"property": "waferName", "label": {"text": {"zh_CN": "晶圆名称"}}, "description": {"zh_CN": "晶圆的名称或编号"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "waferSize", "label": {"text": {"zh_CN": "晶圆缩放"}}, "description": {"zh_CN": "晶圆大小"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 1, "widget": {"component": "NumberConfigurator", "props": {"min": 20, "max": 100, "step": 1}}}, {"property": "waferShape", "label": {"text": {"zh_CN": "晶圆形状"}}, "description": {"zh_CN": "晶圆的形状"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "circle", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "圆形", "value": "circle"}, {"label": "方形", "value": "square"}]}}}]}, {"label": {"zh_CN": "OCR属性"}, "description": {"zh_CN": "OCR相关配置"}, "content": [{"property": "showOcr", "label": {"text": {"zh_CN": "显示OCR"}}, "description": {"zh_CN": "是否显示OCR信息"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator", "props": {}}}, {"property": "ocrIo", "label": {"text": {"zh_CN": "OCR数据"}}, "description": {"zh_CN": "OCR识别的数据"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "", "widget": {"component": "InputConfigurator", "props": {}}}]}], "events": {}, "slots": {}}, "snippets": [{"name": {"zh_CN": "对中器"}, "icon": "aligner", "screenshot": "", "snippetName": "<PERSON><PERSON><PERSON>", "schema": {"props": {"rotate": 0, "scale": 1, "waferState": true, "waferColor": "#3366FF", "waferName": "A-001", "waferSize": 80, "waferShape": "circle"}}}], "category": "DCP"}