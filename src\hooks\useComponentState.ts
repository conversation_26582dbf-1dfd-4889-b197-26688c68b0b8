import { toRefs, ref, computed, watch } from 'vue';

export default function useComponentState(option: any, originColor = '') {
  const {
    field: _field,
    forSelect,
    state,
    disabledColor,
    selectedColor,
    defaultColor,
    successColor,
    errorColor,
    wipColor,
  } = toRefs(option);
  const stateValue = ref<string>('default');

  const changeState = () => {};

  watch(
    () => state?.value,
    (cur) => {
      stateValue.value = cur || 'default';
    },
    {
      immediate: true,
    },
  );

  const stateStyle = computed(() => {
    if (stateValue.value === 'disabled' && disabledColor.value) {
      return {
        backgroundColor: disabledColor.value,
        color: '#FFF',
        cursor: forSelect?.value ? 'no-drop' : 'default',
      };
    } else if (stateValue.value === 'wip' && wipColor.value) {
      return {
        backgroundColor: wipColor.value,
        color: '#FFF',
        cursor: forSelect?.value ? 'no-drop' : 'default',
      };
    } else if (stateValue.value === 'success' && successColor.value) {
      return {
        backgroundColor: successColor.value,
        color: '#000',
        cursor: forSelect?.value ? 'pointer' : 'default',
      };
    } else if (stateValue.value === 'error' && errorColor.value) {
      return {
        backgroundColor: errorColor.value,
        color: '#000',
        cursor: forSelect?.value ? 'pointer' : 'default',
      };
    }
    if (forSelect?.value) {
      if (stateValue.value === 'selected') {
        return {
          backgroundColor: selectedColor.value,
          color: '#000',
          cursor: 'pointer',
        };
      } else {
        return {
          backgroundColor: defaultColor.value,
          color: '#000',
          cursor: 'pointer',
        };
      }
    }
    return {
      backgroundColor: defaultColor?.value ? defaultColor.value : originColor,
      color: '#000',
      cursor: 'default',
    };
  });

  const stateColor = computed(() => {
    if (stateValue.value === 'disabled' && disabledColor.value) {
      return disabledColor.value;
    } else if (stateValue.value === 'wip' && wipColor.value) {
      return wipColor.value;
    } else if (stateValue.value === 'success' && successColor.value) {
      return successColor.value;
    } else if (stateValue.value === 'error' && errorColor.value) {
      return errorColor.value;
    }
    if (forSelect?.value) {
      if (stateValue.value === 'selected') {
        return selectedColor.value;
      } else {
        return defaultColor.value;
      }
    }
    return defaultColor.value;
  });

  return {
    changeState,
    stateStyle,
    stateColor,
  };
}
