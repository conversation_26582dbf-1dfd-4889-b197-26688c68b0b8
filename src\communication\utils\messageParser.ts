/**
 * 消息解析工具函数
 * 用于处理 MQTT 消息的解析和字段提取
 */

import { MqttInfo } from '@/types';
import { get } from 'lodash-es';

// 函数缓存，避免重复编译相同的代码
const functionCache = new Map<string, Function>();

/**
 * 根据路径提取消息体中的值
 * @param message 原始消息
 * @param path 路径字符串，如 'Payload'
 * @returns 提取的值
 */
export function extractValueByPath(message: any, path: string): any {
  if (!message || !path) {
    return message;
  }

  const pathParts = path.split('.');
  // ! 由于 MQTT 回来的在目前业务下，肯定是数组，所以先取第一个元素
  let current = Array.isArray(message) ? message[0] : message;

  try {
    for (const part of pathParts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        console.warn(`路径 ${path} 在消息中不存在:`, message);
        return undefined;
      }
    }

    return current;
  } catch (error) {
    console.error('根据路径提取值时出错:', error, {
      message,
      path,
    });
    return undefined;
  }
}

/**
 * 安全执行用户提供的解析函数
 * @param code 用户提供的函数代码
 * @param result bodyPath下的数据
 * @param componentProps 组件props（排除dynamicPartInfo）
 * @param index 多主题时的索引
 * @returns 解析结果
 */
function executeParseFunction(
  code: string,
  result: any,
  componentProps: Record<string, any>,
  index?: number,
): any {
  try {
    // 从缓存中获取或创建函数
    let parseFunction = functionCache.get(code);

    if (!parseFunction) {
      // 智能处理用户代码：如果是完整函数定义，直接使用；否则包装为函数体
      let functionCode = code.trim();

      if (functionCode.startsWith('function')) {
        // 完整函数定义，提取函数体
        const match = functionCode.match(
          /function\s+\w*\s*\([^)]*\)\s*\{([\s\S]*)\}/,
        );
        if (match) {
          functionCode = match[1];
        }
      }

      // 如果代码没有return语句，自动添加return
      if (!functionCode.includes('return')) {
        functionCode = `return ${functionCode}`;
      }

      // 创建安全的函数，现在接收三个参数：result, componentProps, index
      parseFunction = new Function(
        'result',
        'componentProps',
        'index',
        functionCode,
      );

      // 缓存函数
      functionCache.set(code, parseFunction);
    }

    // 执行函数
    const value = parseFunction(result, componentProps, index);

    return value;
  } catch (error) {
    console.error('执行自定义解析函数时出错:', {
      code,
      result,
      index,
      error: error instanceof Error ? error.message : String(error),
    });
    return undefined;
  }
}

/**
 * 根据字段配置提取字段值
 * @param data 数据对象
 * @param config MQTT配置信息
 * @param componentProps 组件props（排除dynamicPartInfo）
 * @param index 多主题时的索引
 * @returns 提取的值
 */
export function extractFieldValue(
  data: any,
  config: MqttInfo,
  componentProps: Record<string, any>,
  index?: number,
): any {
  if (!data || !config.contentFields) {
    return undefined;
  }

  const { parseType = 'value', contentFields } = config;

  if (parseType === 'func') {
    // 使用自定义函数解析
    return executeParseFunction(contentFields, data, componentProps, index);
  } else {
    return get(data, contentFields);
  }
}

/**
 * 解析 MQTT 消息
 * 优化：当消息是数组时，根据AttributeDepthName匹配contentFields，使用最后一个匹配的元素
 * @param message 原始 MQTT 消息
 * @param config MQTT 配置
 * @param componentProps 组件props（排除dynamicPartInfo）
 * @param index 多主题时的索引（可选）
 * @returns 解析后的值
 */
export function parseMqttMessage(
  message: any,
  config: MqttInfo,
  componentProps: Record<string, any>,
  index?: number,
): any {
  try {
    let targetMessage = message;

    // 如果是数组，需要找到AttributeDepthName匹配contentFields的消息
    if (Array.isArray(message)) {
      const { contentFields } = config;

      // 从后往前查找匹配的消息（获取最新的）
      for (let i = message.length - 1; i >= 0; i--) {
        const item = message[i];
        const payloadData = get(item, config.bodyPath);

        // 检查AttributeDepthName是否以 ".contentFields" 结尾
        if (payloadData?.AttributeDepthName?.endsWith(`.${contentFields}`)) {
          targetMessage = item;
          break;
        }
      }

      // 如果没找到匹配的，使用第一个消息作为fallback
      if (targetMessage === message) {
        targetMessage = message[0];
      }
    }

    // 使用找到的目标消息继续正常的提取流程
    const messageBody = extractValueByPath(targetMessage, config.bodyPath);

    if (!messageBody) {
      console.warn('无法根据 bodyPath 提取消息体:', config.bodyPath, message);
      return undefined;
    }

    // 然后根据 contentFields 提取具体字段
    const fieldValue = extractFieldValue(
      messageBody,
      config,
      componentProps,
      index,
    );

    return fieldValue;
  } catch (error) {
    console.error('解析 MQTT 消息时出错:', error, {
      message,
      config,
      componentProps,
      index,
    });
    return undefined;
  }
}
