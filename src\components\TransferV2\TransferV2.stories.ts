import type { Meta, StoryObj } from '@storybook/vue3';
import TransferV2 from './index.vue';

const meta: Meta<typeof TransferV2> = {
  title: 'Equipment/TransferV2',
  component: TransferV2,
  tags: ['autodocs'],
  argTypes: {
    width: { control: 'number' },
    height: { control: 'number' },
    moving: { control: 'boolean' },
    speed: { control: { type: 'range', min: 10, max: 200, step: 10 } },
    direction: {
      control: 'radio',
      options: ['forward', 'backward', 'up', 'down'],
    },
    items: { control: 'object' },
    color: { control: 'color' },
    orientation: { control: 'radio', options: ['horizontal', 'vertical'] },
    stopAtEnd: { control: 'boolean' },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const defaultItems = [
  { id: 1, position: 0, content: 'A' },
  { id: 2, position: 25, content: 'B' },
  { id: 3, position: 60, content: 'C' },
  { id: 4, position: 90, content: 'D' },
];

export const Primary: Story = {
  args: {
    width: 600,
    height: 80,
    moving: true,
    speed: 50,
    direction: 'forward',
    items: defaultItems,
    color: '#c0c0c0',
    orientation: 'horizontal',
  },
};

export const Stopped: Story = {
  args: {
    ...Primary.args,
    moving: false,
  },
};

export const Vertical: Story = {
  args: {
    ...Primary.args,
    width: 80,
    height: 400,
    orientation: 'vertical',
    direction: 'down',
  },
};

export const GreenBelt: Story = {
  args: {
    ...Primary.args,
    color: '#4CAF50',
    items: [
      { id: 1, position: 0, content: 'Green', material: 'block' },
      { id: 2, position: 30, content: 'Belt', material: 'sphere' },
      { id: 3, position: 60, content: 'Demo', material: 'cube' },
    ],
  },
};

// 简化数组示例
export const SimpleArray: Story = {
  args: {
    ...Primary.args,
    items: ['block', 'cube', 'sphere', 'cylinder', 'tray'],
  },
};

export const SingleMaterial: Story = {
  args: {
    ...Primary.args,
    items: ['block'],
  },
};

// 悬停效果示例
export const StopAtEnd: Story = {
  args: {
    ...Primary.args,
    stopAtEnd: true,
    items: [
      { id: 1, position: 0, content: 'A' },
      { id: 2, position: 30, content: 'B' },
      { id: 3, position: 60, content: 'C' },
    ],
  },
};
