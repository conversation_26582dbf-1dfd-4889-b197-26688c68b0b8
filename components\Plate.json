{"id": 1, "version": "0.0.17", "name": {"zh_CN": "晶圆托盘"}, "component": "Plate", "icon": "plate", "description": "晶圆托盘组件，可以显示或隐藏晶圆", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "Plate", "version": "0.0.17", "script": "http://*************:4874/@dcp/component-library@0.0.17/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "group": "DCP", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["waferState", "waferColor", "scale"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}}, "schema": {"properties": [{"label": {"zh_CN": "基础属性"}, "description": {"zh_CN": "托盘的基础配置属性"}, "content": [{"property": "scale", "label": {"text": {"zh_CN": "托盘缩放"}}, "description": {"zh_CN": "托盘的整体缩放比例"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 1, "widget": {"component": "NumberConfigurator", "props": {"min": 0.1, "max": 3, "step": 0.1}}}]}, {"label": {"zh_CN": "晶圆配置"}, "description": {"zh_CN": "托盘上晶圆的配置属性"}, "content": [{"property": "waferState", "label": {"text": {"zh_CN": "晶圆状态"}}, "description": {"zh_CN": "是否显示晶圆"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "boolean", "defaultValue": false, "widget": {"component": "SwitchConfigurator", "props": {}}}, {"property": "waferColor", "label": {"text": {"zh_CN": "晶圆颜色"}}, "description": {"zh_CN": "晶圆的颜色"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "#A5D6F9", "widget": {"component": "ColorConfigurator", "props": {}}}, {"property": "waferName", "label": {"text": {"zh_CN": "晶圆名称"}}, "description": {"zh_CN": "晶圆的显示名称"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "waferScale", "label": {"text": {"zh_CN": "晶圆缩放"}}, "description": {"zh_CN": "晶圆的缩放比例"}, "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 1, "widget": {"component": "NumberConfigurator", "props": {"min": 0.5, "max": 2, "step": 0.1}}}]}], "events": {}}, "snippets": [{"name": {"zh_CN": "托盘"}, "icon": "plate", "screenshot": "", "snippetName": "Plate", "schema": {"props": {"scale": 1, "waferState": false, "waferColor": "#A5D6F9", "waferName": "", "waferScale": 1}}}], "category": "DCP"}