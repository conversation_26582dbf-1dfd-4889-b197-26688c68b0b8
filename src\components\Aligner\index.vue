<template>
  <div class="content">
    <div class="content-container">
      <img v-if="alignerPic" :src="alignerPic" alt="" />
      <img v-else :src="defaultAligner" alt="" />
      <transition
        enter-active-class="animate__animated animate__fadeInUp"
        leave-active-class="animate__animated animate__fadeOutDown"
      >
        <div class="wafer-container" v-show="waferState">
          <wafer
            :wafer-shape="waferShape"
            :size="waferSize"
            :color="waferColor"
            :name="waferName"
            :rotate="rotate"
            :showOcr="!!showOcr"
            :ocrIo="ocrIo"
          />
        </div>
      </transition>
    </div>
  </div>
</template>

<script lang="ts">
import { toRefs, defineComponent } from 'vue';
import defaultAligner from './aligner.png';
import Wafer from '../Wafer';

export default defineComponent({
  name: 'Aligner',
  components: {
    Wafer,
  },
  props: {
    rotate: {
      type: Number,
      default: 0,
    },
    scale: {
      type: Number,
      default: 1,
    },
    waferState: {
      type: Boolean,
      default: false,
    },
    waferColor: {
      type: String,
      default: '#000',
    },
    waferName: {
      type: String,
      default: '',
    },
    waferSize: {
      type: Number,
      default: 60,
    },
    waferShape: {
      type: String,
      default: 'circle',
    },
    alignerPic: {
      type: String,
      default: '',
    },
    showOcr: {
      type: Boolean,
      default: false,
    },
    ocrIo: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const { alignerPic } = toRefs(props);

    return {
      alignerPic,
      defaultAligner,
    };
  },
});
</script>

<style lang="scss" scoped>
.content {
  overflow: visible;

  .content-container {
    transition: all 0.5s ease;
    transform-origin: center center;
    transform: v-bind('"scale(" + scale + ") rotate(" + rotate + "deg)"');
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;

    img {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: auto;
    }

    .wafer-container {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 50%;
      height: 50%;
      z-index: 1;
    }
  }
}
</style>
