{"id": 1, "version": "2.4.2", "name": {"zh_CN": "输入框"}, "component": "ElInput", "icon": "input", "description": "通过鼠标或键盘输入字符", "doc_url": "", "screenshot": "", "tags": "", "keywords": "", "dev_mode": "proCode", "npm": {"package": "element-plus", "exportName": "ElInput"}, "group": "表单组件", "category": "element-plus", "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["type", "size"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent", "createBlock"], "disable": []}, "invalidity": [""], "clickCapture": true, "framework": "<PERSON><PERSON>"}, "schema": {"properties": [{"name": "0", "label": {"zh_CN": "基础属性"}, "content": [{"property": "modelValue", "label": {"text": {"zh_CN": "绑定值"}}, "description": {"zh_CN": "绑定值"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "size", "label": {"text": {"zh_CN": "尺寸"}}, "description": {"zh_CN": "尺寸"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "default", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "large", "value": "large"}, {"label": "default", "value": "default"}, {"label": "small", "value": "small"}]}}}, {"property": "type", "label": {"text": {"zh_CN": "类型"}}, "description": {"zh_CN": "类型"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "widget": {"component": "InputConfigurator", "props": {}}}, {"property": "placeholder", "label": {"text": {"zh_CN": "占位文本"}}, "description": {"zh_CN": "输入框占位文本"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "widget": {"component": "I18nConfigurator", "props": {}}, "device": []}, {"property": "maxlength", "label": {"text": {"zh_CN": "最大长度"}}, "description": {"zh_CN": "最大输入长度"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "widget": {"component": "NumberConfigurator", "props": {}}, "device": []}, {"property": "disabled", "label": {"text": {"zh_CN": "是否禁用"}}, "description": {"zh_CN": "是否禁用"}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "defaultValue": false, "type": "boolean", "widget": {"component": "CheckBoxConfigurator", "props": {}}, "device": []}], "description": {"zh_CN": ""}}], "events": {"onUpdate:modelValue": {"label": {"zh_CN": "双向绑定值改变时触发"}, "description": {"zh_CN": "双向绑定值改变时触发"}}, "onBlur": {"label": {"zh_CN": "输入框失去焦点时触发"}, "description": {"zh_CN": "输入框失去焦点时触发"}, "type": "event", "functionInfo": {"params": [{"name": "event", "type": "Object", "defaultValue": "", "description": {"zh_CN": "原生 event"}}], "returns": {}}, "defaultValue": ""}}, "slots": {"prefix": {"label": {"zh_CN": "头部内容"}, "description": {"zh_CN": "输入框头部内容，只对非 type='textarea' 有效"}}, "suffix": {"label": {"zh_CN": "尾部内容"}, "description": {"zh_CN": "输入框尾部内容，只对非 type='textarea' 有效"}}, "prepend": {"label": {"zh_CN": "前置内容"}, "description": {"zh_CN": "输入框前置内容，只对非 type='textarea' 有效"}}, "append": {"label": {"zh_CN": "后置内容"}, "description": {"zh_CN": "输入框后置内容，只对非 type='textarea' 有效"}}}}, "snippets": [{"name": {"zh_CN": "输入框"}, "icon": "input", "screenshot": "", "snippetName": "ElInput", "schema": {}, "category": "element-plus"}]}