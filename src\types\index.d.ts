/**
 * 类型定义导出文件
 * 将全局类型定义导出为模块
 */

type DynamicPartConfig = {
  type: 'partInfo';
  connectStrategy: string;
  attributeName: string;
};

type MqttInfo = {
  type: 'mqtt';
  baseUrl: string;
  topic: string;
  qos: number;
  bodyPath: string;
  parseType?: 'value' | 'func'; // 解析方式：value-字段提取，func-自定义函数
  contentFields: string; // value模式：字段名；func模式：JS函数代码
};

type DynamicPartInfo = Record<string, DynamicPartConfig | MqttInfo>;

/**
 * MQTT 适配器工厂函数类型
 * 宿主项目通过此类型的函数创建 MQTT 适配器实例
 */
type MqttAdapterFactory = (
  baseUrl: string,
  options?: any,
) => ICommunicationAdapter;

import type { ICommunicationAdapter } from '../communication/adapter/CommunicationAdapter';

// 重新导出全局类型定义
export type {
  DynamicPartConfig,
  MqttInfo,
  DynamicPartInfo,
  MqttAdapterFactory,
};
