<template>
  <div :class="['content', { disabled: !!isDisabled }]" @click="toggle">
    <svg
      t="1723711805090"
      class="icon"
      viewBox="0 0 1751 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="9532"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      width="54.71875"
      height="32"
    >
      <path
        d="M1711.203597 983.611896L894.394573 512.040388 1711.203597 40.388104zM40.388104 40.388104l816.849413 471.652284L40.388104 983.611896z"
        :fill="color"
        p-id="9533"
      ></path>
      <path
        d="M875.816045 115.954248v757.519287"
        :fill="color"
        p-id="9534"
      ></path>
    </svg>
  </div>
</template>

<script lang="ts">
import { toRefs, ref, defineComponent } from 'vue';

export default defineComponent({
  name: 'Valve',
  props: {
    color: {
      type: String,
      required: true,
    },
    isDisabled: {
      type: Boolean,
      default: false,
    },
    rotate: {
      type: Number,
      default: 0,
    },
    pauseFlow: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const {
      color: contentColor,
      isDisabled,
      rotate,
      pauseFlow,
    } = toRefs(props);
    const valveState = ref<number>(1);

    const toggle = () => {
      console.log('toggle');
    };

    return { contentColor, isDisabled, rotate, pauseFlow, toggle };
  },
});
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  .icon {
    transition: filter 0.3s;
  }
  &:not(.disabled) {
    .icon:hover {
      filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.8));
    }
  }

  &.disabled {
    .icon:hover {
      filter: drop-shadow(0 0 3px rgba(128, 128, 128, 0.3));
    }
  }
  &.disabled {
    cursor: no-drop;
  }
  svg {
    transform: v-bind('`rotate(${rotate}deg)`');
    width: 100%;
    height: 100%;
  }
}
</style>
