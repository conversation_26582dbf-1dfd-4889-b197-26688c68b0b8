{"component": "AgvRobot", "name": {"zh_CN": "AGV机器人"}, "icon": "AgvRobot", "group": "DCP", "category": "DCP", "description": "AGV机器人组件，结合了SixAxisRobot的机械臂功能和AGV底座。", "tags": "", "keywords": "", "doc_url": "", "devMode": "proCode", "npm": {"package": "@dcp/component-library", "exportName": "AgvRobot", "version": "0.0.49", "script": "http://*************:4874/@dcp/component-library@0.0.49/js/web-component.mjs", "destructuring": true, "npmrc": "@dcp:registry=http://*************:4873"}, "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "isPopper": false, "isNullNode": false, "isLayout": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "rootSelector": "", "shortcuts": {"properties": ["currentPosition", "left", "top"]}, "contextMenu": {"actions": ["copy", "remove", "insert", "updateAttr", "bindEevent"], "disable": []}, "clickCapture": false, "framework": "<PERSON><PERSON>"}, "schema": {"properties": [{"name": "0", "label": {"zh_CN": "基础属性"}, "description": {"zh_CN": "组件的核心功能配置"}, "content": [{"property": "currentStation", "label": {"text": {"zh_CN": "小车站点"}}, "description": "当前小车站点", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "position1", "widget": {"component": "InputConfigurator"}}, {"property": "stationList", "label": {"text": {"zh_CN": "小车位置列表"}}, "description": "小车位置配置列表", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "object", "defaultValue": {"position1": {"left": -150, "top": 130}, "position2": {"left": -120, "top": 230}, "position3": {"left": -250, "top": 200}}, "widget": {"component": "CodeConfigurator", "props": {"language": "json", "height": 150}}}, {"property": "currentPosition", "label": {"text": {"zh_CN": "手臂站点"}}, "description": "当前手臂站点", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "position1", "widget": {"component": "InputConfigurator"}}, {"property": "positionList", "label": {"text": {"zh_CN": "位置配置列表"}}, "description": "机械臂位置配置列表，包含各个位置的角度、长度等参数", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "object", "defaultValue": {"position1": {"firstArmAngle": -45, "secondArmAngle": 90, "thirdArmAngle": 90, "gripperAngle": 45, "firstArmLength": 120, "secondArmLength": 120, "thirdArmLength": 100, "gripperWidth": 25, "gripperHeight": 60, "gripperAnimate": 0}, "position2": {"firstArmAngle": 20, "secondArmAngle": 30, "thirdArmAngle": 15, "gripperAngle": 25, "firstArmLength": 150, "secondArmLength": 140, "thirdArmLength": 115, "gripperWidth": 25, "gripperHeight": 60, "gripperAnimate": 1, "endPosition": "position1"}, "position3": {"firstArmAngle": 60, "secondArmAngle": 40, "thirdArmAngle": -10, "gripperAngle": 0, "firstArmLength": 120, "secondArmLength": 110, "thirdArmLength": 80, "gripperWidth": 25, "gripperHeight": 60, "gripperAnimate": 1}}, "widget": {"component": "CodeConfigurator", "props": {"language": "json", "height": 150}}}, {"property": "material", "label": {"text": {"zh_CN": "物料"}}, "description": "当前抓手上的物料", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "string", "defaultValue": "block", "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "无物料", "value": null}, {"label": "木块", "value": "block"}, {"label": "魔方", "value": "cube"}, {"label": "球体", "value": "sphere"}, {"label": "圆柱体", "value": "cylinder"}, {"label": "托盘", "value": "tray"}]}}}]}, {"name": "1", "label": {"zh_CN": "样式属性"}, "description": {"zh_CN": "组件的外观和尺寸配置"}, "content": [{"property": "width", "label": {"text": {"zh_CN": "宽度"}}, "description": "组件宽度", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 600, "widget": {"component": "NumberConfigurator", "props": {"min": 50, "max": 2000, "step": 10}}}, {"property": "height", "label": {"text": {"zh_CN": "高度"}}, "description": "组件高度", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 500, "widget": {"component": "NumberConfigurator", "props": {"min": 50, "max": 2000, "step": 10}}}, {"property": "scale", "label": {"text": {"zh_CN": "缩放比例"}}, "description": "缩放比例", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 1, "widget": {"component": "NumberConfigurator", "props": {"min": 0.1, "max": 5, "step": 0.1}}}]}, {"name": "2", "label": {"zh_CN": "行为属性"}, "description": {"zh_CN": "组件的交互和动画配置"}, "content": [{"property": "animateDuration", "label": {"text": {"zh_CN": "动画持续时间"}}, "description": "动画持续时间", "required": false, "readOnly": false, "disabled": false, "cols": 12, "labelPosition": "left", "type": "number", "defaultValue": 1, "widget": {"component": "NumberConfigurator", "props": {"min": 0, "max": 50, "step": 0.1}}}]}], "events": {}, "slots": {}}, "snippets": [{"name": {"zh_CN": "AGV机器人"}, "icon": "AgvRobot", "snippetName": "AgvRobot", "schema": {"props": {"width": 600, "height": 500, "scale": 1, "currentStation": "position1", "stationList": {"position1": {"left": -150, "top": 130}, "position2": {"left": -120, "top": 230}, "position3": {"left": -250, "top": 200}}, "animateDuration": 1, "currentPosition": "position1", "positionList": {"position1": {"firstArmAngle": -45, "secondArmAngle": 90, "thirdArmAngle": 90, "gripperAngle": 45, "firstArmLength": 120, "secondArmLength": 120, "thirdArmLength": 100, "gripperWidth": 25, "gripperHeight": 60, "gripperAnimate": 0}, "position2": {"firstArmAngle": 20, "secondArmAngle": 30, "thirdArmAngle": 15, "gripperAngle": 25, "firstArmLength": 150, "secondArmLength": 140, "thirdArmLength": 115, "gripperWidth": 25, "gripperHeight": 60, "gripperAnimate": 1, "endPosition": "position1"}, "position3": {"firstArmAngle": 60, "secondArmAngle": 40, "thirdArmAngle": -10, "gripperAngle": 0, "firstArmLength": 120, "secondArmLength": 110, "thirdArmLength": 80, "gripperWidth": 25, "gripperHeight": 60, "gripperAnimate": 1}}, "material": "block"}}}]}