{"name": {"zh_CN": "按钮"}, "component": "TinyButton", "icon": "button", "description": "常用的操作按钮，提供包括默认按钮、图标按钮、图片按钮、下拉按钮等类型", "docUrl": "", "screenshot": "", "tags": "", "keywords": "", "devMode": "proCode", "npm": {"package": "@opentiny/vue", "exportName": "<PERSON><PERSON>"}, "group": "component", "priority": 2, "schema": {"properties": [{"label": {"zh_CN": "基础信息"}, "description": {"zh_CN": "基础信息"}, "content": [{"property": "text", "type": "string", "defaultValue": "按钮文案", "label": {"text": {"zh_CN": "按钮文字"}}, "cols": 12, "hidden": false, "required": true, "readOnly": false, "disabled": false, "widget": {"component": "I18nConfigurator", "props": {}}, "description": {"zh_CN": "按钮文字"}, "labelPosition": "left"}, {"property": "size", "type": "select", "label": {"text": {"zh_CN": "大小"}}, "cols": 12, "rules": [], "hidden": false, "required": true, "readOnly": false, "disabled": false, "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "large", "value": "large"}, {"label": "medium", "value": "medium"}, {"label": "small", "value": "small"}, {"label": "mini", "value": "mini"}]}}, "description": {"zh_CN": "按钮大小"}, "labelPosition": "left"}, {"property": "disabled", "label": {"text": {"zh_CN": "禁用"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否被禁用"}, "labelPosition": "left"}, {"property": "type", "label": {"text": {"zh_CN": "类型"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "primary", "value": "primary"}, {"label": "success", "value": "success"}, {"label": "info", "value": "info"}, {"label": "warning", "value": "warning"}, {"label": "danger", "value": "danger"}, {"label": "text", "value": "text"}]}}, "description": {"zh_CN": "设置不同的主题样式"}, "labelPosition": "left"}]}, {"name": "1", "label": {"zh_CN": "其他"}, "content": [{"property": "round", "label": {"text": {"zh_CN": "圆角"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否圆角按钮"}, "labelPosition": "left"}, {"property": "plain", "label": {"text": {"zh_CN": "朴素按钮"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否为朴素按钮"}, "labelPosition": "left"}, {"property": "reset-time", "label": {"text": {"zh_CN": "禁用时间"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "NumberConfigurator", "props": {}}, "description": {"zh_CN": "设置禁用时间，防止重复提交，单位毫秒"}, "labelPosition": "left"}, {"property": "circle", "label": {"text": {"zh_CN": "圆形按钮"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否圆形按钮"}, "labelPosition": "left"}, {"property": "autofocus", "label": {"text": {"zh_CN": "自动聚焦"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否默认聚焦"}, "labelPosition": "left"}, {"property": "loading", "label": {"text": {"zh_CN": "加载中样式"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否展示位加载中样式"}, "labelPosition": "left"}], "description": {"zh_CN": ""}}], "events": {"onClick": {"label": {"zh_CN": "点击事件"}, "description": {"zh_CN": "按钮被点击时触发的回调函数"}, "type": "event", "functionInfo": {"params": [], "returns": {}}, "defaultValue": ""}}}, "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "nestingRule": {"childWhitelist": "", "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["text", "size"]}, "contextMenu": {"actions": ["create symbol"], "disable": ["copy", "remove"]}}, "snippets": [{"name": {"zh_CN": "按钮"}, "icon": "button", "screenshot": "", "snippetName": "TinyButton", "schema": {"componentName": "TinyButton", "props": {"text": "按钮文案"}}, "category": "basic"}]}