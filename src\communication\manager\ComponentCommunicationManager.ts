/**
 * 组件通信管理器
 * 负责管理组件的通信订阅和消息路由
 */
import { MqttInfo } from '@/types';
import { ICommunicationAdapter } from '../adapter/CommunicationAdapter';
import mqtt from 'mqtt';

/**
 * 组件消息处理器接口
 */
export interface ComponentMessageHandler {
  /**
   * 处理接收到的消息
   * @param message 消息内容
   */
  handleMessage(message: any): void;
}

/**
 * 组件通信管理器
 * 管理所有组件的订阅关系，处理消息路由
 */
export class ComponentCommunicationManager {
  private static instance: ComponentCommunicationManager;
  private adapter: ICommunicationAdapter | null = null;

  // 存储组件订阅信息
  // 格式: Map<componentId, Map<topic, unsubscribeFunction>>
  private componentSubscriptions = new Map<string, Map<string, () => void>>();

  // 存储 MQTT 适配器实例
  // 格式: Map<baseUrl, ICommunicationAdapter>
  private mqttAdapters = new Map<string, ICommunicationAdapter>();

  /**
   * 私有构造函数，确保单例模式
   */
  private constructor() {}

  /**
   * 获取单例实例
   * @returns 组件通信管理器实例
   */
  public static getInstance(): ComponentCommunicationManager {
    if (!ComponentCommunicationManager.instance) {
      ComponentCommunicationManager.instance =
        new ComponentCommunicationManager();
    }
    return ComponentCommunicationManager.instance;
  }

  /**
   * 设置通信适配器
   * @param adapter 通信适配器实例
   */
  public setAdapter(adapter: ICommunicationAdapter): void {
    this.adapter = adapter;
  }

  /**
   * 获取通信适配器
   * @returns 通信适配器实例
   */
  public getAdapter(): ICommunicationAdapter | null {
    return this.adapter;
  }

  /**
   * 生成组件订阅主题
   * @param partId 部件ID
   * @param isFullTopic 是否使用完整主题
   * @returns 订阅主题
   */
  private generateTopic(partId: string, isFullTopic = false): string {
    return isFullTopic ? partId : `guiMessage/component/${partId}`;
  }

  /**
   * 注册组件订阅
   * @param componentId 组件实例ID
   * @param partId 部件ID
   * @param handler 消息处理器
   */
  public registerComponent(
    componentId: string,
    partId: string,
    handler: ComponentMessageHandler,
  ): void {
    if (!this.adapter) {
      console.warn('通信适配器未设置，无法注册组件订阅');
      return;
    }

    if (!partId) {
      console.warn(`组件 ${componentId} 未提供 partId，跳过注册`);
      return;
    }

    // 生成订阅主题
    const topic = this.generateTopic(partId);

    // 获取或创建组件的订阅映射
    let subscriptions = this.componentSubscriptions.get(componentId);
    if (!subscriptions) {
      subscriptions = new Map<string, () => void>();
      this.componentSubscriptions.set(componentId, subscriptions);
    }

    // 检查是否已经订阅了这个主题
    if (subscriptions.has(topic)) {
      return;
    }

    // 订阅主题
    const unsubscribe = this.adapter.subscribe(topic, (message, _) => {
      try {
        // 处理消息
        handler.handleMessage(message);
      } catch (error) {
        console.error(`处理组件 ${componentId} 的消息时出错:`, error);
      }
    });

    // 保存取消订阅函数
    subscriptions.set(topic, unsubscribe);
  }

  /**
   * 注销组件订阅
   * @param componentId 组件实例ID
   */
  public unregisterComponent(componentId: string): void {
    const subscriptions = this.componentSubscriptions.get(componentId);
    if (subscriptions) {
      // 调用所有取消订阅函数
      subscriptions.forEach((unsubscribe) => unsubscribe());
      // 从映射中移除
      this.componentSubscriptions.delete(componentId);
    }
  }

  /**
   * 发布消息到指定组件
   * @param partId 部件ID
   * @param message 消息内容
   */
  public publishToComponent(partId: string, message: any): void {
    if (!this.adapter) {
      console.warn('通信适配器未设置，无法发布消息');
      return;
    }

    if (!partId) {
      console.warn('未提供 partId，无法发布消息');
      return;
    }

    const topic = this.generateTopic(partId);
    this.adapter.publish(topic, message);
  }

  /**
   * 获取或创建 MQTT 适配器
   * @param baseUrl MQTT broker URL
   * @param options 连接选项
   * @returns MQTT 适配器实例
   */
  private getOrCreateMqttAdapter(
    baseUrl: string,
    options?: any,
  ): ICommunicationAdapter | null {
    // 检查是否已存在该 baseUrl 的适配器
    if (this.mqttAdapters.has(baseUrl)) {
      return this.mqttAdapters.get(baseUrl)!;
    }

    try {
      // 动态导入 MQTT 库并创建适配器

      // 创建 MQTT 客户端
      const client = mqtt.connect(baseUrl, {
        clientId: `dcp-mqtt-${Math.random().toString(16).substring(2, 10)}`,
        ...options,
      });

      // 创建适配器包装器
      const mqttAdapter: ICommunicationAdapter = {
        subscribe: (
          topic: string,
          callback: (message: any, topic: string) => void,
        ) => {
          client.subscribe(topic, (err: any) => {
            if (err) {
              console.error(`MQTT 订阅失败: ${topic}`, err);
            } else {
              console.log(`MQTT 订阅成功: ${topic}`);
            }
          });

          const messageHandler = (receivedTopic: string, message: Buffer) => {
            if (receivedTopic === topic) {
              try {
                const messageStr = message.toString();
                const parsedMessage = JSON.parse(messageStr);
                callback(parsedMessage, receivedTopic);
              } catch (error) {
                console.error('MQTT 消息解析失败:', error);
                // 如果解析失败，传递原始字符串
                callback(message.toString(), receivedTopic);
              }
            }
          };

          client.on('message', messageHandler);

          // 返回取消订阅函数
          return () => {
            client.unsubscribe(topic);
            client.off('message', messageHandler);
          };
        },

        publish: (topic: string, message: any) => {
          const messageStr =
            typeof message === 'string' ? message : JSON.stringify(message);
          client.publish(topic, messageStr);
        },

        isConnected: () => {
          return client.connected;
        },
      };

      // 缓存适配器实例
      this.mqttAdapters.set(baseUrl, mqttAdapter);

      return mqttAdapter;
    } catch (error) {
      console.error(`创建 MQTT 适配器失败: ${baseUrl}`, error);
      return null;
    }
  }

  /**
   * 注册 MQTT 组件订阅
   * @param componentId 组件实例ID
   * @param mqttConfig MQTT 配置
   * @param handler 消息处理器
   */
  public registerMqttComponent(
    componentId: string,
    mqttConfig: MqttInfo,
    handler: ComponentMessageHandler,
  ): void {
    const { baseUrl, topic, qos } = mqttConfig;

    if (!baseUrl || !topic) {
      console.warn(`MQTT 配置不完整:`, mqttConfig);
      return;
    }

    // 获取或创建 MQTT 适配器
    const mqttAdapter = this.getOrCreateMqttAdapter(baseUrl, { qos });
    if (!mqttAdapter) {
      console.error(`无法获取 MQTT 适配器: ${baseUrl}`);
      return;
    }

    console.log(`组件 ${componentId} 订阅 MQTT 主题: ${topic} (${baseUrl})`);

    // 获取或创建组件的订阅映射
    let subscriptions = this.componentSubscriptions.get(componentId);
    if (!subscriptions) {
      subscriptions = new Map<string, () => void>();
      this.componentSubscriptions.set(componentId, subscriptions);
    }

    // 生成唯一的订阅键（包含 baseUrl 和 topic）
    const subscriptionKey = `mqtt:${baseUrl}:${topic}`;

    // 检查是否已经订阅了这个主题
    if (subscriptions.has(subscriptionKey)) {
      console.log(
        `组件 ${componentId} 已经订阅了 MQTT 主题 ${topic}，跳过重复订阅`,
      );
      return;
    }

    // 订阅 MQTT 主题
    const unsubscribe = mqttAdapter.subscribe(
      topic,
      (message, _receivedTopic) => {
        try {
          // 处理消息
          handler.handleMessage(message);
        } catch (error) {
          console.error(`处理组件 ${componentId} 的 MQTT 消息时出错:`, error);
        }
      },
    );

    // 保存取消订阅函数
    subscriptions.set(subscriptionKey, unsubscribe);
  }
}
