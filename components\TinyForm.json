{"icon": "form", "name": {"zh_CN": "表单"}, "component": "TinyForm", "description": "由按钮、输入框、选择器、单选框、多选框等控件组成，用以收集、校验、提交数据", "docUrl": "", "screenshot": "", "tags": "", "keywords": "", "devMode": "proCode", "npm": {"package": "@opentiny/vue", "exportName": "Form"}, "group": "component", "priority": 5, "schema": {"properties": [{"label": {"zh_CN": "基础信息"}, "description": {"zh_CN": "基础信息"}, "content": [{"property": "disabled", "label": {"text": {"zh_CN": "禁用"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "是否禁用"}, "labelPosition": "left"}, {"property": "label-width", "label": {"text": {"zh_CN": "标签宽度"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {}}, "description": {"zh_CN": "表单中标签占位宽度，默认为 80px"}, "labelPosition": "left"}, {"property": "inline", "label": {"text": {"zh_CN": "行内布局"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "行内布局模式，默认为 false"}, "labelPosition": "left"}, {"property": "label-align", "label": {"text": {"zh_CN": "必填标识占位"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CheckBoxConfigurator", "props": {}}, "description": {"zh_CN": "必填标识 * 是否占位"}, "labelPosition": "left"}, {"property": "label-suffix", "label": {"text": {"zh_CN": "标签后缀"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "InputConfigurator", "props": {}}, "description": {"zh_CN": "表单中标签后缀"}, "labelPosition": "left"}, {"property": "label-position", "label": {"text": {"zh_CN": "标签位置"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "SelectConfigurator", "props": {"options": [{"label": "right", "value": "right"}, {"label": "left ", "value": "left "}, {"label": "top", "value": "top"}]}}, "description": {"zh_CN": "表单中标签的布局位置"}, "labelPosition": "left"}]}, {"name": "1", "label": {"zh_CN": "校验属性"}, "content": [{"property": "model", "label": {"text": {"zh_CN": "数据对象"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CodeConfigurator", "props": {}}, "description": {"zh_CN": "表单数据对象"}, "labelPosition": "top"}, {"property": "rules", "label": {"text": {"zh_CN": "校验规则"}}, "required": true, "readOnly": false, "disabled": false, "cols": 12, "widget": {"component": "CodeConfigurator", "props": {}}, "description": {"zh_CN": "表单验证规则"}, "labelPosition": "top"}], "description": {"zh_CN": ""}}], "events": {"onValidate": {"label": {"zh_CN": "表单项被校验后触发"}, "description": {"zh_CN": "表单项被校验后触发"}, "type": "event", "functionInfo": {"params": [{"name": "function", "type": "Function", "defaultValue": "(valid) => {}", "description": {"zh_CN": "校验回调函数"}}], "returns": {}}, "defaultValue": ""}, "onInput": {"label": {"zh_CN": "输入值改变时触发"}, "description": {"zh_CN": "在 Input 输入值改变时触发"}, "type": "event", "functionInfo": {"params": [{"name": "value", "type": "string", "defaultValue": "", "description": {"zh_CN": "输入框输入的值"}}], "returns": {}}, "defaultValue": ""}, "onBlur": {"label": {"zh_CN": "失去焦点时触发"}, "description": {"zh_CN": "在 Input 失去焦点时触发"}, "type": "event", "functionInfo": {"params": [{"name": "event", "type": "Object", "defaultValue": "", "description": {"zh_CN": "原生 event"}}], "returns": {}}, "defaultValue": ""}, "onFocus": {"label": {"zh_CN": "获取焦点时触发"}, "description": {"zh_CN": "在 Input 获取焦点时触发"}, "type": "event", "functionInfo": {"params": [{"name": "event", "type": "Object", "defaultValue": "", "description": {"zh_CN": "原生 event"}}], "returns": {}}, "defaultValue": ""}, "onClear": {"label": {"zh_CN": "点击清空按钮时触发"}, "description": {"zh_CN": "点击清空按钮时触发"}, "type": "event", "functionInfo": {"params": [], "returns": {}}, "defaultValue": ""}}}, "configure": {"loop": true, "condition": true, "styles": true, "isContainer": false, "isModal": false, "nestingRule": {"childWhitelist": [], "parentWhitelist": "", "descendantBlacklist": "", "ancestorWhitelist": ""}, "isNullNode": false, "isLayout": false, "rootSelector": "", "shortcuts": {"properties": ["label-width", "disabled"]}, "contextMenu": {"actions": ["create symbol"], "disable": ["copy", "remove"]}}, "snippets": [{"name": {"zh_CN": "表单"}, "screenshot": "", "snippetName": "tiny-form", "icon": "form", "schema": {"componentName": "TinyForm", "props": {"labelWidth": "80px", "labelPosition": "top"}, "children": [{"componentName": "TinyFormItem", "props": {"label": "人员"}, "children": [{"componentName": "TinyInput", "props": {"placeholder": "请输入", "modelValue": ""}}]}, {"componentName": "TinyFormItem", "props": {"label": "密码"}, "children": [{"componentName": "TinyInput", "props": {"placeholder": "请输入", "modelValue": "", "type": "password"}}]}, {"componentName": "TinyFormItem", "props": {"label": ""}, "children": [{"componentName": "TinyButton", "props": {"text": "提交", "type": "primary", "style": "margin-right: 10px"}}, {"componentName": "TinyButton", "props": {"text": "重置", "type": "primary"}}]}]}, "category": "form"}]}