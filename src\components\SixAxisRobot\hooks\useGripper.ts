import { ref, onMounted } from 'vue';

import * as PIXI from 'pixi.js';

export const useGripper = () => {
  const gripperContainer = ref<any>();

  const drawGripper = () => {
    // 左爪
    const leftGripper = new PIXI.Graphics();
    leftGripper.lineStyle(2, 0x292327); // 线条宽度从3减至2
    leftGripper.beginFill(0x292327);

    // 左爪外轮廓 - 所有坐标值减半
    leftGripper.moveTo(-11.25, 0); // -22.5 -> -11.25
    leftGripper.lineTo(-22.5, 22.5); // -45 -> -22.5, 45 -> 22.5
    leftGripper.lineTo(-11.25, 37.5); // -22.5 -> -11.25, 75 -> 37.5
    leftGripper.lineTo(0, 37.5); // 75 -> 37.5
    leftGripper.lineTo(0, 30); // 60 -> 30
    leftGripper.lineTo(-7.5, 30); // -15 -> -7.5, 60 -> 30
    leftGripper.lineTo(-15, 22.5); // -30 -> -15, 45 -> 22.5
    leftGripper.lineTo(-11.25, 0); // -22.5 -> -11.25
    leftGripper.closePath();
    leftGripper.endFill();

    // 设置左爪的支点为顶部中心
    leftGripper.pivot.set(0, 30); // 60 -> 30
    leftGripper.position.set(0, 30); // 60 -> 30

    // 右爪
    const rightGripper = new PIXI.Graphics();
    rightGripper.lineStyle(3, 0x292327); // 线条宽度从6减至3
    rightGripper.beginFill(0x292327);

    // 右爪外轮廓 - 所有坐标值减半
    rightGripper.moveTo(11.25, 0); // 22.5 -> 11.25
    rightGripper.lineTo(22.5, 22.5); // 45 -> 22.5, 45 -> 22.5
    rightGripper.lineTo(11.25, 37.5); // 22.5 -> 11.25, 75 -> 37.5
    rightGripper.lineTo(0, 37.5); // 75 -> 37.5
    rightGripper.lineTo(0, 30); // 60 -> 30
    rightGripper.lineTo(7.5, 30); // 15 -> 7.5, 60 -> 30
    rightGripper.lineTo(15, 22.5); // 30 -> 15, 45 -> 22.5
    rightGripper.lineTo(11.25, 0); // 22.5 -> 11.25
    rightGripper.closePath();
    rightGripper.endFill();

    // 设置右爪的支点为顶部中心
    rightGripper.pivot.set(0, 30); // 60 -> 30
    rightGripper.position.set(0, 30); // 60 -> 30

    // 爪子手柄
    const gripperHandle = new PIXI.Graphics();
    gripperHandle.lineStyle(3, 0x292327); // 线条宽度从6减至3
    gripperHandle.beginFill(0x292327);
    gripperHandle.drawCircle(0, 5, 11); // y: 10 -> 5, radius: 22 -> 11
    gripperHandle.endFill();

    gripperHandle.lineStyle(2, 0x292327); // 线条宽度从4减至2
    gripperHandle.beginFill(0x292327);
    gripperHandle.drawRect(-11.25, -16.25, 22.5, 20); // 所有值减半
    gripperHandle.endFill();

    // 添加橙色圆形轮廓
    gripperHandle.lineStyle(2, 0xff5d03); // 线条宽度从4减至2
    gripperHandle.drawCircle(0, 5, 6); // y: 10 -> 5, radius: 12 -> 6

    // 添加小实心圆
    gripperHandle.lineStyle(0);
    gripperHandle.beginFill(0xff5d03);
    gripperHandle.drawCircle(0, 5, 2); // y: 10 -> 5, radius: 4 -> 2
    gripperHandle.endFill();
    gripperHandle.position.set(0, 50); // 100 -> 50

    // 组装爪子
    gripperContainer.value.addChild(leftGripper);
    gripperContainer.value.addChild(rightGripper);
    gripperContainer.value.addChild(gripperHandle);

    gripperContainer.value.leftGripper = leftGripper;
    gripperContainer.value.rightGripper = rightGripper;

    // 调整整体支点和位置
    gripperContainer.value.pivot.set(-5, 45); // 120 -> 60
    gripperContainer.value.position.set(0, -62); // -125 -> -62.5
    gripperContainer.value.rotation = 0.5;
  };

  onMounted(() => {
    drawGripper();
  });

  return {
    gripperContainer,
  };
};
