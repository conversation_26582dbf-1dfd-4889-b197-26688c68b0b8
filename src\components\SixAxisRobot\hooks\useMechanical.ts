import { ref, onMounted } from 'vue';
import * as PIXI from 'pixi.js';

export const useMechanical = () => {
  const mechanicalContainer = ref<any>();

  const drawMechanicalClaw = () => {
    // 创建一个容器来管理整个爪子组件
    const clawContainer = new PIXI.Container();

    const METAL_DARK = 0x2c3e50;
    const METAL_LIGHT = 0xff5d03;

    // 左爪组件
    const leftClaw = new PIXI.Graphics();
    leftClaw.lineStyle(4, METAL_DARK);
    leftClaw.beginFill(METAL_LIGHT);

    // 调整左爪路径以呈现更自然的抓取姿势
    leftClaw.moveTo(-20, 0);
    leftClaw.lineTo(-40, -20);
    leftClaw.lineTo(-45, -40);
    leftClaw.quadraticCurveTo(-48, -70, -45, -90);
    leftClaw.lineTo(-40, -110);
    leftClaw.lineTo(-35, -115);
    leftClaw.lineTo(-30, -110);
    leftClaw.quadraticCurveTo(-25, -70, -20, -40);
    leftClaw.lineTo(-15, -20);
    leftClaw.lineTo(-20, 0);
    leftClaw.endFill();

    // 左爪装饰线条
    leftClaw.lineStyle(2, METAL_DARK, 0.5);
    leftClaw.moveTo(-40, -40);
    leftClaw.lineTo(-25, -40);
    leftClaw.moveTo(-38, -60);
    leftClaw.lineTo(-28, -60);
    leftClaw.moveTo(-37, -80);
    leftClaw.lineTo(-27, -80);

    // 右爪组件
    const rightClaw = new PIXI.Graphics();
    rightClaw.lineStyle(4, METAL_DARK);
    rightClaw.beginFill(METAL_LIGHT);

    // 调整右爪路径
    rightClaw.moveTo(20, 0);
    rightClaw.lineTo(40, -20);
    rightClaw.lineTo(45, -40);
    rightClaw.quadraticCurveTo(48, -70, 45, -90);
    rightClaw.lineTo(40, -110);
    rightClaw.lineTo(35, -115);
    rightClaw.lineTo(30, -110);
    rightClaw.quadraticCurveTo(25, -70, 20, -40);
    rightClaw.lineTo(15, -20);
    rightClaw.lineTo(20, 0);
    rightClaw.endFill();

    // 右爪装饰
    rightClaw.lineStyle(2, METAL_DARK, 0.5);
    rightClaw.moveTo(40, -40);
    rightClaw.lineTo(25, -40);
    rightClaw.moveTo(38, -60);
    rightClaw.lineTo(28, -60);
    rightClaw.moveTo(37, -80);
    rightClaw.lineTo(27, -80);

    // 调整左右爪的初始角度
    leftClaw.rotation = 0.3; // 向外打开约17度
    rightClaw.rotation = -0.3;

    // 添加液压缸
    const hydraulics = new PIXI.Graphics();
    hydraulics.lineStyle(3, METAL_DARK);
    hydraulics.beginFill(0xcccccc);

    // 调整液压缸角度以匹配爪子姿势
    hydraulics.moveTo(-15, -10);
    hydraulics.lineTo(-35, -45);
    hydraulics.lineTo(-30, -45);
    hydraulics.lineTo(-10, -10);
    hydraulics.endFill();

    hydraulics.beginFill(0xcccccc);
    hydraulics.moveTo(15, -10);
    hydraulics.lineTo(35, -45);
    hydraulics.lineTo(30, -45);
    hydraulics.lineTo(10, -10);
    hydraulics.endFill();

    // 组装爪子
    clawContainer.addChild(leftClaw);
    clawContainer.addChild(rightClaw);
    clawContainer.addChild(hydraulics);

    // 整体姿态调整
    clawContainer.rotation = -0.5; // 整体旋转约-30度
    clawContainer.position.set(300, 400);

    // 添加简单的抓取动画
    // let isGrabbing = false;
    // const baseRotation = leftClaw.rotation;

    // const animate = () => {
    //   if (isGrabbing) {
    //     leftClaw.rotation = baseRotation - 0.2;
    //     rightClaw.rotation = -baseRotation + 0.2;
    //   } else {
    //     leftClaw.rotation = baseRotation;
    //     rightClaw.rotation = -baseRotation;
    //   }
    //   isGrabbing = !isGrabbing;
    // };

    // 每2秒执行一次抓取动作
    // setInterval(animate, 2000);

    mechanicalContainer.value = clawContainer;
  };

  onMounted(() => {
    drawMechanicalClaw();
  });

  return {
    mechanicalContainer,
  };
};
